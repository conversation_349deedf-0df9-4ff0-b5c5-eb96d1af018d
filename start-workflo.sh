#!/bin/bash

# WorkFlo Development Startup Script
# This script starts both frontend and backend services for local development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    printf "${1}${2}${NC}\n"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local name=$2
    local max_attempts=30
    local attempt=1

    print_color $YELLOW "Waiting for $name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            print_color $GREEN "✅ $name is ready!"
            return 0
        fi
        
        printf "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_color $RED "❌ $name failed to start within expected time"
    return 1
}

# Function to start backend
start_backend() {
    print_color $BLUE "🚀 Starting WorkFlo Backend..."
    
    cd workflo_back
    
    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        print_color $YELLOW "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Install dependencies
    print_color $YELLOW "Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Run migrations
    print_color $YELLOW "Running database migrations..."
    python manage.py migrate
    
    # Create superuser if it doesn't exist
    print_color $YELLOW "Creating superuser..."
    python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(email='<EMAIL>').exists():
    User.objects.create_superuser(
        email='<EMAIL>',
        username='admin',
        password='admin123',
        first_name='System',
        last_name='Administrator',
        employee_id='EMP0001',
        role='admin'
    )
    print('Superuser created: <EMAIL> / admin123')
else:
    print('Superuser already exists')
"
    
    # Start development server
    print_color $GREEN "Starting Django development server on port 8000..."
    python manage.py runserver 8000 &
    BACKEND_PID=$!
    
    cd ..
    
    # Wait for backend to be ready
    wait_for_service "http://localhost:8000/api/health/" "Backend"
}

# Function to start frontend
start_frontend() {
    print_color $BLUE "🚀 Starting WorkFlo Frontend..."
    
    cd workflo-front
    
    # Install dependencies
    if [ ! -d "node_modules" ]; then
        print_color $YELLOW "Installing Node.js dependencies..."
        npm install
    fi
    
    # Start development server
    print_color $GREEN "Starting Next.js development server on port 3000..."
    npm run dev &
    FRONTEND_PID=$!
    
    cd ..
    
    # Wait for frontend to be ready
    wait_for_service "http://localhost:3000" "Frontend"
}

# Function to test integration
test_integration() {
    print_color $BLUE "🧪 Testing Frontend-Backend Integration..."
    
    cd workflo-front
    npm run test:integration:verbose
    cd ..
}

# Function to cleanup processes
cleanup() {
    print_color $YELLOW "🛑 Shutting down services..."
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    # Kill any remaining processes on ports 3000 and 8000
    pkill -f "python manage.py runserver" 2>/dev/null || true
    pkill -f "next dev" 2>/dev/null || true
    
    print_color $GREEN "✅ Services stopped"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --backend-only    Start only the backend service"
    echo "  --frontend-only   Start only the frontend service"
    echo "  --test           Run integration tests after starting services"
    echo "  --help           Show this help message"
    echo ""
    echo "Default: Start both frontend and backend services"
}

# Main function
main() {
    print_color $PURPLE "🌟 WorkFlo Development Environment Startup"
    print_color $CYAN "============================================"
    
    # Parse command line arguments
    BACKEND_ONLY=false
    FRONTEND_ONLY=false
    RUN_TESTS=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --backend-only)
                BACKEND_ONLY=true
                shift
                ;;
            --frontend-only)
                FRONTEND_ONLY=true
                shift
                ;;
            --test)
                RUN_TESTS=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                print_color $RED "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Check prerequisites
    if ! command_exists python3; then
        print_color $RED "❌ Python 3 is required but not installed"
        exit 1
    fi
    
    if ! command_exists node; then
        print_color $RED "❌ Node.js is required but not installed"
        exit 1
    fi
    
    if ! command_exists npm; then
        print_color $RED "❌ npm is required but not installed"
        exit 1
    fi
    
    # Check if ports are available
    if [ "$FRONTEND_ONLY" != true ] && port_in_use 8000; then
        print_color $RED "❌ Port 8000 is already in use"
        exit 1
    fi
    
    if [ "$BACKEND_ONLY" != true ] && port_in_use 3000; then
        print_color $RED "❌ Port 3000 is already in use"
        exit 1
    fi
    
    # Set up signal handlers for cleanup
    trap cleanup EXIT INT TERM
    
    # Start services
    if [ "$FRONTEND_ONLY" != true ]; then
        start_backend
    fi
    
    if [ "$BACKEND_ONLY" != true ]; then
        start_frontend
    fi
    
    # Run integration tests if requested
    if [ "$RUN_TESTS" = true ]; then
        sleep 5  # Give services time to fully start
        test_integration
    fi
    
    # Show service information
    print_color $GREEN "🎉 WorkFlo Development Environment is Ready!"
    print_color $CYAN "============================================"
    
    if [ "$FRONTEND_ONLY" != true ]; then
        print_color $BLUE "🔧 Backend Services:"
        print_color $NC "   API:           http://localhost:8000/api/"
        print_color $NC "   Admin:         http://localhost:8000/admin/"
        print_color $NC "   Documentation: http://localhost:8000/api/docs/"
        print_color $NC "   Health:        http://localhost:8000/api/health/"
    fi
    
    if [ "$BACKEND_ONLY" != true ]; then
        print_color $BLUE "🌐 Frontend Services:"
        print_color $NC "   Application:   http://localhost:3000/"
    fi
    
    print_color $BLUE "🔐 Default Credentials:"
    print_color $NC "   Admin:         <EMAIL> / admin123"
    print_color $NC "   Supervisor:    <EMAIL> / password123"
    print_color $NC "   HR Staff:      <EMAIL> / password123"
    print_color $NC "   Accountant:    <EMAIL> / password123"
    print_color $NC "   Employee:      <EMAIL> / password123"
    
    print_color $YELLOW "Press Ctrl+C to stop all services"
    
    # Wait for user to stop services
    wait
}

# Run main function
main "$@"
