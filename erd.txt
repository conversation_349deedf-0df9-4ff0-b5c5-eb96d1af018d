=====================================================
WORKFLO-FRONT DATABASE ERD (Entity Relationship Diagram)
=====================================================

LEGEND:
- PK = Primary Key
- FK = Foreign Key
- UK = Unique Key
- ||--o{ = One-to-Many relationship
- ||--|| = One-to-One relationship
- }o--o{ = Many-to-Many relationship

=====================================================
1. CORE AUTHENTICATION & USER MANAGEMENT
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                            USERS                                │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • phone_number                     │
│ • email (UK)                 • profile_picture                  │
│ • password_hash              • role                             │
│ • first_name                 • is_active                        │
│ • last_name                  • is_deleted                       │
│ • employee_id (UK)           • date_joined                      │
│ • created_at                 • last_login                       │
│ • updated_at                 • created_by (FK → users.id)       │
│ • deleted_at                 • updated_by (FK → users.id)       │
│ • deleted_by (FK → users.id)                                    │
└─────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │               │               │
                    ▼               ▼               ▼
        ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
        │ USER_SESSIONS   │ │PASSWORD_RESET_  │ │ EMPLOYEE_       │
        │                 │ │TOKENS           │ │ PROFILES        │
        ├─────────────────┤ ├─────────────────┤ ├─────────────────┤
        │ • id (PK)       │ │ • id (PK)       │ │ • id (PK)       │
        │ • user_id (FK)  │ │ • user_id (FK)  │ │ • user_id (FK)  │
        │ • access_token  │ │ • token (UK)    │ │ • department_id │
        │ • refresh_token │ │ • expires_at    │ │ • job_title     │
        │ • expires_at    │ │ • used          │ │ • hire_date     │
        │ • created_at    │ │ • created_at    │ │ • salary        │
        │ • last_used     │ └─────────────────┘ │ • status        │
        │ • ip_address    │                     │ • (+ 30 fields) │
        │ • user_agent    │                     └─────────────────┘
        └─────────────────┘

=====================================================
2. ORGANIZATIONAL STRUCTURE
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                         DEPARTMENTS                             │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • budget                           │
│ • name (UK)                  • location                         │
│ • description                • is_active                        │
│ • supervisor_id (FK → users.id)                                 │
│ • parent_department_id (FK → departments.id) [SELF-REFERENCE]   │
│ • created_at                 • updated_at                       │
│ • created_by (FK → users.id) • updated_by (FK → users.id)       │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                      EMPLOYEE_PROFILES                         │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • nssf_number (Kenya)              │
│ • user_id (FK → users.id) UK • nhif_number (Kenya)              │
│ • department_id (FK → departments.id)                          │
│ • supervisor_id (FK → users.id)                                │
│ • job_title                  • kra_pin (Kenya)                  │
│ • hire_date                  • national_id                      │
│ • employment_type            • date_of_birth                    │
│ • work_location              • gender                           │
│ • marital_status             • nationality                      │
│ • address                    • city                             │
│ • state                      • postal_code                      │
│ • country                    • status                           │
│ • termination_date           • termination_reason               │
│ • created_at                 • updated_at                       │
│ • created_by                 • updated_by                       │
└─────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┬───────────────┐
                    │               │               │               │
                    ▼               ▼               ▼               ▼
        ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
        │ SALARY_         │ │ BANK_           │ │ EMERGENCY_      │ │ SALARY_         │
        │ PROFILES        │ │ PROFILES        │ │ CONTACTS        │ │ ADJUSTMENTS     │
        ├─────────────────┤ ├─────────────────┤ ├─────────────────┤ ├─────────────────┤
        │ • id (PK)       │ │ • id (PK)       │ │ • id (PK)       │ │ • id (PK)       │
        │ • employee_id   │ │ • employee_id   │ │ • employee_id   │ │ • salary_       │
        │   (FK → users)  │ │   (FK → users)  │ │   (FK → users)  │ │   profile_id    │
        │ • basic_salary  │ │ • bank_name     │ │ • contact_name  │ │ • adjustment_   │
        │ • hourly_rate   │ │ • bank_code     │ │ • relationship  │ │   type          │
        │ • currency      │ │ • branch_name   │ │ • phone_number  │ │ • previous_     │
        │ • pay_frequency │ │ • branch_code   │ │ • email         │ │   basic_salary  │
        │ • allowances    │ │ • account_      │ │ • address       │ │ • new_basic_    │
        │ • overtime_rate │ │   number        │ │ • priority_     │ │   salary        │
        │ • commission_   │ │ • account_name  │ │   order         │ │ • percentage_   │
        │   rate          │ │ • account_type  │ │ • is_active     │ │   change        │
        │ • tax_exemption │ │ • swift_code    │ │ • created_at    │ │ • amount_change │
        │ • tax_relief    │ │ • is_primary    │ │ • updated_at    │ │ • reason        │
        │ • effective_    │ │ • is_active     │ │ • created_by    │ │ • justification │
        │   from          │ │ • created_at    │ │ • updated_by    │ │ • status        │
        │ • effective_to  │ │ • updated_at    │ └─────────────────┘ │ • effective_    │
        │ • is_active     │ │ • created_by    │                     │   from          │
        │ • created_at    │ │ • updated_by    │                     │ • effective_to  │
        │ • updated_at    │ └─────────────────┘                     │ • approved_by   │
        │ • created_by    │                                         │ • approved_at   │
        │ • updated_by    │                                         │ • created_at    │
        └─────────────────┘                                         │ • updated_at    │
                                                                    └─────────────────┘

=====================================================
3. ATTENDANCE & TIME TRACKING
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                     ATTENDANCE_RECORDS                         │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • break_time                       │
│ • employee_id (FK → users.id)• status                          │
│ • date (UK with employee_id) • notes                           │
│ • check_in                   • biostar_synced                   │
│ • check_out                  • biostar_event_ids[]             │
│ • break_start                • created_at                       │
│ • break_end                  • updated_at                       │
│ • total_hours                                                   │
│ • regular_hours                                                 │
│ • overtime_hours                                                │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                       BIOSTAR_EVENTS                           │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • location                         │
│ • biostar_event_id (UK)      • processed                       │
│ • employee_id (FK → users.id)• attendance_record_id (FK)       │
│ • device_id                  • created_at                       │
│ • device_name                                                   │
│ • event_type (ENTRY/EXIT)                                       │
│ • event_datetime                                                │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                      BIOSTAR_DEVICES                           │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • device_type                      │
│ • biostar_device_id (UK)     • status                          │
│ • name                       • last_seen                        │
│ • ip_address                 • created_at                       │
│ • port                       • updated_at                       │
│ • location                                                      │
└─────────────────────────────────────────────────────────────────┘

=====================================================
4. OVERTIME MANAGEMENT SYSTEM
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                      OVERTIME_TYPES                            │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • max_hours_per_month              │
│ • name (UK)                  • requires_pre_approval            │
│ • description                • auto_approve_threshold           │
│ • rate_multiplier            • is_active                        │
│ • max_hours_per_day          • created_at                       │
│ • max_hours_per_week         • updated_at                       │
└─────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┬───────────────┐
                    │               │               │               │
                    ▼               ▼               ▼               ▼
        ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
        │ OVERTIME_       │ │ OVERTIME_       │ │ OVERTIME_       │ │ OVERTIME_       │
        │ REQUESTS        │ │ RECORDS         │ │ APPROVAL_       │ │ BUDGETS         │
        │                 │ │                 │ │ WORKFLOWS       │ │                 │
        ├─────────────────┤ ├─────────────────┤ ├─────────────────┤ ├─────────────────┤
        │ • id (PK)       │ │ • id (PK)       │ │ • id (PK)       │ │ • id (PK)       │
        │ • employee_id   │ │ • employee_id   │ │ • name          │ │ • department_id │
        │ • overtime_     │ │ • overtime_     │ │ • description   │ │ • employee_id   │
        │   type_id       │ │   request_id    │ │ • department_id │ │ • budget_year   │
        │ • request_date  │ │ • overtime_     │ │ • requires_     │ │ • budget_month  │
        │ • planned_start │ │   type_id       │ │   supervisor_   │ │ • allocated_    │
        │ • planned_end   │ │ • attendance_   │ │   approval      │ │   hours         │
        │ • planned_hours │ │   record_id     │ │ • requires_     │ │ • allocated_    │
        │ • reason        │ │ • overtime_date │ │   admin_        │ │   amount        │
        │ • justification │ │ • start_time    │ │   approval      │ │ • used_hours    │
        │ • project_code  │ │ • end_time      │ │ • supervisor_   │ │ • used_amount   │
        │ • status        │ │ • total_hours   │ │   approval_     │ │ • remaining_    │
        │ • supervisor_   │ │ • rate_         │ │   threshold     │ │   hours         │
        │   approved_by   │ │   multiplier    │ │ • admin_        │ │ • remaining_    │
        │ • supervisor_   │ │ • regular_      │ │   approval_     │ │   amount        │
        │   approved_at   │ │   hourly_rate   │ │   threshold     │ │ • warning_      │
        │ • admin_        │ │ • overtime_     │ │ • auto_approval │ │   threshold_    │
        │   approved_by   │ │   hourly_rate   │ │   threshold     │ │   percentage    │
        │ • admin_        │ │ • total_amount  │ │ • approval_     │ │ • block_        │
        │   approved_at   │ │ • currency      │ │   deadline_     │ │   threshold_    │
        │ • final_        │ │ • overtime_     │ │   hours         │ │   percentage    │
        │   approved_by   │ │   category      │ │ • advance_      │ │ • is_active     │
        │ • final_        │ │ • is_emergency  │ │   notice_hours  │ │ • budget_       │
        │   approved_at   │ │ • is_pre_       │ │ • escalation_   │ │   exceeded      │
        │ • actual_start  │ │   approved      │ │   enabled       │ │ • created_at    │
        │ • actual_end    │ │ • status        │ │ • escalation_   │ │ • updated_at    │
        │ • actual_hours  │ │ • approved_by   │ │   hours         │ └─────────────────┘
        │ • completion_   │ │ • approved_at   │ │ • notify_       │
        │   notes         │ │ • pay_cycle_id  │ │   employee      │
        │ • completed_by  │ │ • included_in_  │ │ • notify_       │
        │ • completed_at  │ │   payroll       │ │   supervisor    │
        │ • created_at    │ │ • created_at    │ │ • is_active     │
        │ • updated_at    │ │ • updated_at    │ │ • created_at    │
        └─────────────────┘ └─────────────────┘ │ • updated_at    │
                                │               └─────────────────┘
                                │ ||--o{
                                ▼
                    ┌─────────────────────────────────────┐
                    │        OVERTIME_CALCULATIONS       │
                    ├─────────────────────────────────────┤
                    │ • id (PK)                           │
                    │ • employee_id (FK → users.id)      │
                    │ • overtime_record_id (FK) UK       │
                    │ • pay_cycle_id (FK)                 │
                    │ • base_hourly_rate                  │
                    │ • overtime_rate_multiplier          │
                    │ • calculated_overtime_rate          │
                    │ • total_overtime_hours              │
                    │ • regular_hours_equivalent          │
                    │ • gross_overtime_amount             │
                    │ • overtime_tax_rate                 │
                    │ • overtime_tax_amount               │
                    │ • overtime_nssf_amount              │
                    │ • overtime_nhif_amount              │
                    │ • overtime_housing_levy             │
                    │ • total_overtime_deductions         │
                    │ • net_overtime_amount               │
                    │ • calculation_date                  │
                    │ • calculation_method                │
                    │ • currency                          │
                    │ • status                            │
                    │ • approved_by (FK → users.id)      │
                    │ • approved_at                       │
                    │ • included_in_payroll               │
                    │ • payroll_record_id (FK)           │
                    │ • created_at                        │
                    │ • updated_at                        │
                    └─────────────────────────────────────┘

=====================================================
5. LEAVE MANAGEMENT
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                        LEAVE_TYPES                             │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • requires_approval                │
│ • name (UK)                  • is_paid                          │
│ • description                • is_active                        │
│ • max_days_per_year          • created_at                       │
│ • carry_forward_allowed                                         │
│ • max_carry_forward_days                                        │
└─────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │ ||--o{        │ ||--o{        │
                    ▼               ▼               │
        ┌─────────────────┐ ┌─────────────────┐     │
        │ LEAVE_BALANCES  │ │ LEAVE_          │     │
        │                 │ │ APPLICATIONS    │     │
        ├─────────────────┤ ├─────────────────┤     │
        │ • id (PK)       │ │ • id (PK)       │     │
        │ • employee_id   │ │ • employee_id   │     │
        │ • leave_type_id │ │ • leave_type_id │     │
        │ • year          │ │ • start_date    │     │
        │ • allocated_days│ │ • end_date      │     │
        │ • used_days     │ │ • days_requested│     │
        │ • pending_days  │ │ • reason        │     │
        │ • remaining_days│ │ • status        │     │
        │   (COMPUTED)    │ │ • approved_by   │     │
        └─────────────────┘ │ • approved_at   │     │
                            │ • reviewer_     │     │
                            │   comments      │     │
                            └─────────────────┘     │
                                                    │
                                                    ▼
                                        ┌─────────────────┐
                                        │ COMPANY_        │
                                        │ HOLIDAYS        │
                                        ├─────────────────┤
                                        │ • id (PK)       │
                                        │ • name          │
                                        │ • date          │
                                        │ • description   │
                                        │ • is_recurring  │
                                        │ • applies_to_all│
                                        │ • department_ids│
                                        │ • created_by    │
                                        └─────────────────┘

=====================================================
5. PAYROLL SYSTEM
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                         PAY_CYCLES                              │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • total_net_amount                 │
│ • pay_period                 • total_deductions                 │
│ • start_date                 • notes                            │
│ • end_date                   • created_at                       │
│ • pay_date                   • updated_at                       │
│ • status                     • created_by (FK → users.id)       │
│ • total_employees            • processed_by (FK → users.id)     │
│ • total_gross_amount         • processed_at                     │
└─────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │ ||--o{        │ ||--o{        │
                    ▼               ▼               │
        ┌─────────────────┐ ┌─────────────────┐     │
        │ PAYROLL_RECORDS │ │ PAYROLL_        │     │
        │                 │ │ ADJUSTMENTS     │     │
        ├─────────────────┤ ├─────────────────┤     │
        │ • id (PK)       │ │ • id (PK)       │     │
        │ • employee_id   │ │ • employee_id   │     │
        │ • pay_cycle_id  │ │ • pay_cycle_id  │     │
        │ • basic_salary  │ │ • adjustment_   │     │
        │ • allowances    │ │   type          │     │
        │ • overtime_amt  │ │ • category      │     │
        │ • bonuses       │ │ • amount        │     │
        │ • gross_salary  │ │ • adjustment_   │     │
        │ • tax_deduction │ │   effect        │     │
        │ • nssf_deduction│ │ • description   │     │
        │ • nhif_deduction│ │ • is_recurring  │     │
        │ • housing_levy  │ │ • effective_date│     │
        │ • net_salary    │ │ • end_date      │     │
        │ • payment_status│ └─────────────────┘     │
        │   (ENUM)        │                         │
        └─────────────────┘                         │
                                                    │
                                                    ▼
                                        ┌─────────────────┐
                                        │ EMPLOYEE_       │
                                        │ BENEFITS        │
                                        ├─────────────────┤
                                        │ • id (PK)       │
                                        │ • employee_id   │
                                        │ • benefit_type  │
                                        │ • benefit_name  │
                                        │ • value_type    │
                                        │ • value         │
                                        │ • provider      │
                                        │ • start_date    │
                                        │ • end_date      │
                                        │ • status        │
                                        │ • employee_     │
                                        │   contribution  │
                                        │ • employer_     │
                                        │   contribution  │
                                        └─────────────────┘

=====================================================
6. PERFORMANCE MANAGEMENT
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                PERFORMANCE_REVIEW_TEMPLATES                     │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • rating_scale                     │
│ • name                       • is_active                        │
│ • description                • created_at                       │
│ • review_type                • created_by (FK → users.id)       │
│ • criteria (JSONB)                                              │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                     PERFORMANCE_REVIEWS                        │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • reviewer_comments                │
│ • employee_id (FK → users.id)• employee_comments               │
│ • reviewer_id (FK → users.id)• goals_for_next_period           │
│ • template_id (FK)           • status                          │
│ • review_period_start        • employee_acknowledged           │
│ • review_period_end          • employee_acknowledged_at        │
│ • review_type                • due_date                        │
│ • overall_rating             • completed_date                  │
│ • technical_skills           • next_review_date                │
│ • communication              • created_at                      │
│ • teamwork                   • updated_at                      │
│ • leadership                                                   │
│ • problem_solving                                              │
│ • time_management                                              │
│ • strengths                                                    │
│ • areas_for_improvement                                        │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                      PERFORMANCE_GOALS                         │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • progress_percentage              │
│ • employee_id (FK → users.id)• completion_date                 │
│ • review_id (FK)             • notes                           │
│ • title                      • created_at                      │
│ • description                • updated_at                      │
│ • target_date                • created_by (FK → users.id)      │
│ • priority                                                     │
│ • status                                                       │
└─────────────────────────────────────────────────────────────────┘

=====================================================
7. TRAINING & DEVELOPMENT
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                      TRAINING_MODULES                          │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • instructor_email                 │
│ • title                      • materials_url                    │
│ • description                • is_mandatory                     │
│ • content                    • is_active                        │
│ • duration_hours             • created_at                       │
│ • difficulty_level           • updated_at                       │
│ • category                   • created_by (FK → users.id)       │
│ • prerequisites                                                 │
│ • learning_objectives                                           │
│ • instructor_name                                               │
└─────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │ ||--o{        │ ||--o{        │
                    ▼               ▼               │
        ┌─────────────────┐ ┌─────────────────┐     │
        │ EMPLOYEE_       │ │ TRAINING_       │     │
        │ TRAINING_       │ │ SESSIONS        │     │
        │ ASSIGNMENTS     │ │                 │     │
        ├─────────────────┤ ├─────────────────┤     │
        │ • id (PK)       │ │ • id (PK)       │     │
        │ • employee_id   │ │ • training_     │     │
        │ • training_     │ │   module_id     │     │
        │   module_id     │ │ • venue_id      │     │
        │ • venue_id      │ │ • instructor_id │     │
        │ • assigned_by   │ │ • title         │     │
        │ • assigned_date │ │ • start_datetime│     │
        │ • start_date    │ │ • end_datetime  │     │
        │ • end_date      │ │ • max_          │     │
        │ • due_date      │ │   participants  │     │
        │ • status        │ │ • current_      │     │
        │ • progress_%    │ │   participants  │     │
        │ • completion_   │ │ • status        │     │
        │   date          │ │ • notes         │     │
        │ • score         │ │ • created_at    │     │
        │ • feedback      │ │ • created_by    │     │
        │ • certificate_ │ └─────────────────┘     │
        │   url           │           │             │
        └─────────────────┘           │ ||--o{      │
                                      ▼             │
                            ┌─────────────────┐     │
                            │ TRAINING_       │     │
                            │ SESSION_        │     │
                            │ PARTICIPANTS    │     │
                            ├─────────────────┤     │
                            │ • id (PK)       │     │
                            │ • session_id    │     │
                            │ • employee_id   │     │
                            │ • attendance_   │     │
                            │   status        │     │
                            │ • completion_   │     │
                            │   status        │     │
                            │ • score         │     │
                            │ • feedback      │     │
                            │ • certificate_  │     │
                            │   issued        │     │
                            │ • registered_at │     │
                            └─────────────────┘     │
                                                    │
                                                    ▼
                                        ┌─────────────────┐
                                        │ TRAINING_VENUES │
                                        ├─────────────────┤
                                        │ • id (PK)       │
                                        │ • name          │
                                        │ • location      │
                                        │ • address       │
                                        │ • capacity      │
                                        │ • equipment[]   │
                                        │ • facilities[]  │
                                        │ • hourly_rate   │
                                        │ • contact_person│
                                        │ • contact_phone │
                                        │ • contact_email │
                                        │ • status        │
                                        │ • created_at    │
                                        │ • updated_at    │
                                        └─────────────────┘

=====================================================
8. RECRUITMENT & JOB MANAGEMENT
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                        JOB_POSTINGS                             │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • application_deadline             │
│ • title                      • status                           │
│ • department_id (FK)         • positions_available             │
│ • description                • positions_filled                │
│ • requirements               • priority                         │
│ • responsibilities           • hiring_manager_id (FK)           │
│ • qualifications             • created_at                       │
│ • salary_min                 • updated_at                       │
│ • salary_max                 • created_by (FK → users.id)       │
│ • currency                                                      │
│ • location                                                      │
│ • employment_type                                               │
│ • experience_level                                              │
│ • remote_allowed                                                │
│ • travel_required                                               │
│ • posted_date                                                   │
│ • closing_date                                                  │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                      JOB_APPLICATIONS                          │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • visa_status                      │
│ • job_posting_id (FK)        • referral_source                 │
│ • first_name                 • status                          │
│ • last_name                  • stage                           │
│ • email                      • rating                          │
│ • phone_number               • notes                           │
│ • address                    • applied_date                    │
│ • city                       • last_updated                    │
│ • country                    • assigned_recruiter_id (FK)      │
│ • resume_url                                                   │
│ • cover_letter                                                 │
│ • portfolio_url                                                │
│ • linkedin_profile                                             │
│ • expected_salary                                              │
│ • available_start_date                                         │
└─────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │ ||--o{        │ ||--o{        │
                    ▼               ▼               │
        ┌─────────────────┐ ┌─────────────────┐     │
        │ INTERVIEW_      │ │ CANDIDATE_      │     │
        │ SCHEDULES       │ │ EVALUATIONS     │     │
        ├─────────────────┤ ├─────────────────┤     │
        │ • id (PK)       │ │ • id (PK)       │     │
        │ • application_id│ │ • application_id│     │
        │ • interview_type│ │ • interview_id  │     │
        │ • interviewer_  │ │ • evaluator_id  │     │
        │   ids[]         │ │ • evaluation_   │     │
        │ • scheduled_    │ │   criteria      │     │
        │   datetime      │ │   (JSONB)       │     │
        │ • duration_     │ │ • overall_rating│     │
        │   minutes       │ │ • technical_    │     │
        │ • location      │ │   skills        │     │
        │ • meeting_link  │ │ • communication_│     │
        │ • agenda        │ │   skills        │     │
        │ • status        │ │ • cultural_fit  │     │
        │ • feedback      │ │ • experience_   │     │
        │ • rating        │ │   relevance     │     │
        │ • recommendation│ │ • strengths     │     │
        │ • next_steps    │ │ • concerns      │     │
        │ • created_at    │ │ • recommendation│     │
        │ • updated_at    │ │ • comments      │     │
        │ • created_by    │ │ • created_at    │     │
        └─────────────────┘ └─────────────────┘     │
                    │                               │
                    └───────────────────────────────┘
                            interview_id (FK)

=====================================================
9. DOCUMENT MANAGEMENT
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                    DOCUMENT_CATEGORIES                          │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • retention_period_months          │
│ • name (UK)                  • parent_category_id (FK)          │
│ • description                  [SELF-REFERENCE]                 │
│ • is_employee_accessible     • created_at                       │
│ • is_mandatory                                                  │
└─────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │ ||--o{        │ ||--o{        │
                    ▼               ▼               │
        ┌─────────────────┐ ┌─────────────────┐     │
        │ EMPLOYEE_       │ │ COMPANY_        │     │
        │ DOCUMENTS       │ │ DOCUMENTS       │     │
        ├─────────────────┤ ├─────────────────┤     │
        │ • id (PK)       │ │ • id (PK)       │     │
        │ • employee_id   │ │ • category_id   │     │
        │ • category_id   │ │ • title         │     │
        │ • document_name │ │ • description   │     │
        │ • document_type │ │ • file_path     │     │
        │ • file_path     │ │ • file_size     │     │
        │ • file_size     │ │ • file_type     │     │
        │ • file_type     │ │ • version       │     │
        │ • description   │ │ • is_public     │     │
        │ • is_confidential│ │ • requires_     │     │
        │ • expiry_date   │ │   acknowledgment│     │
        │ • version       │ │ • effective_date│     │
        │ • status        │ │ • expiry_date   │     │
        │ • uploaded_by   │ │ • department_   │     │
        │ • approved_by   │ │   ids[]         │     │
        │ • approved_at   │ │ • role_access[] │     │
        │ • created_at    │ │ • status        │     │
        │ • updated_at    │ │ • created_at    │     │
        └─────────────────┘ │ • updated_at    │     │
                            │ • created_by    │     │
                            │ • approved_by   │     │
                            │ • approved_at   │     │
                            └─────────────────┘     │
                                      │             │
                                      │ ||--o{      │
                                      ▼             │
                            ┌─────────────────┐     │
                            │ DOCUMENT_       │     │
                            │ ACKNOWLEDGMENTS │     │
                            ├─────────────────┤     │
                            │ • id (PK)       │     │
                            │ • document_id   │     │
                            │ • employee_id   │     │
                            │ • acknowledged_ │     │
                            │   at            │     │
                            │ • ip_address    │     │
                            │ • user_agent    │     │
                            │ • digital_      │     │
                            │   signature     │     │
                            └─────────────────┘     │
                                                    │
                                                    │
=====================================================
10. NOTIFICATIONS & COMMUNICATION
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                   NOTIFICATION_TEMPLATES                       │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • notification_channels[]          │
│ • name (UK)                  • recipient_roles[]               │
│ • description                • is_active                        │
│ • event_type                 • created_at                       │
│ • subject_template           • updated_at                       │
│ • body_template                                                 │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                       NOTIFICATIONS                            │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • channels[]                       │
│ • recipient_id (FK → users.id)• is_read                        │
│ • sender_id (FK → users.id)  • read_at                         │
│ • template_id (FK)           • action_url                      │
│ • title                      • action_label                    │
│ • message                    • metadata (JSONB)                │
│ • notification_type          • expires_at                      │
│ • category                   • created_at                      │
│ • priority                                                     │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                         EMAIL_LOGS                             │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • provider_message_id              │
│ • notification_id (FK)       • error_message                   │
│ • recipient_email            • sent_at                         │
│ • sender_email               • delivered_at                    │
│ • subject                    • opened_at                       │
│ • body                       • clicked_at                      │
│ • status                     • created_at                      │
└─────────────────────────────────────────────────────────────────┘

=====================================================
11. AUDIT LOGS & SYSTEM TRACKING
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                         AUDIT_LOGS                             │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • user_agent                       │
│ • user_id (FK → users.id)    • session_id                      │
│ • action                     • description                      │
│ • table_name                 • created_at                       │
│ • record_id                                                     │
│ • old_values (JSONB)                                            │
│ • new_values (JSONB)                                            │
│ • ip_address                                                    │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                       ACTIVITY_LOGS                            │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • metadata (JSONB)                 │
│ • user_id (FK → users.id)    • ip_address                      │
│ • activity_type              • user_agent                       │
│ • activity_description       • duration_seconds                │
│ • module                     • created_at                       │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                       SYSTEM_SETTINGS                          │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • category                         │
│ • setting_key (UK)           • is_public                        │
│ • setting_value              • is_editable                      │
│ • setting_type               • created_at                       │
│ • description                • updated_at                       │
│                              • updated_by (FK → users.id)       │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                        COMPANY_INFO                            │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • industry                         │
│ • company_name               • company_size                     │
│ • company_logo               • founded_year                     │
│ • address                    • description                      │
│ • city                       • mission_statement                │
│ • state                      • vision_statement                 │
│ • postal_code                • values                           │
│ • country                    • timezone                         │
│ • phone_number               • currency                         │
│ • email                      • fiscal_year_start_month          │
│ • website                    • working_days_per_week            │
│ • tax_id                     • working_hours_per_day            │
│ • registration_number        • overtime_threshold_hours         │
│                              • created_at                       │
│                              • updated_at                       │
│                              • updated_by (FK → users.id)       │
└─────────────────────────────────────────────────────────────────┘

=====================================================
12. EMPLOYEE ENGAGEMENT & WELLNESS
=====================================================

￼
￼

┌─────────────────────────────────────────────────────────────────┐
│                      EMPLOYEE_SURVEYS                          │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • is_anonymous                     │
│ • title                      • start_date                       │
│ • description                • end_date                         │
│ • survey_type                • status                           │
│ • questions (JSONB)          • response_count                   │
│ • target_audience            • target_count                     │
│ • department_ids[]           • created_at                       │
│ • role_filters[]             • updated_at                       │
│ • employee_ids[]             • created_by (FK → users.id)       │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                      SURVEY_RESPONSES                          │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • completion_time_seconds          │
│ • survey_id (FK)             • ip_address                       │
│ • employee_id (FK)           • submitted_at                     │
│ • responses (JSONB)          • UK(survey_id, employee_id)       │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                   RECOGNITION_CATEGORIES                       │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • points_value                     │
│ • name (UK)                  • is_active                        │
│ • description                • created_at                       │
│ • icon                                                          │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                   EMPLOYEE_RECOGNITIONS                        │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • milestone_type                   │
│ • recipient_id (FK → users.id)• achievement_date               │
│ • nominator_id (FK → users.id)• approved_by (FK → users.id)    │
│ • category_id (FK)           • approved_at                      │
│ • title                      • status                           │
│ • description                • created_at                       │
│ • recognition_type                                              │
│ • points_awarded                                                │
│ • is_public                                                     │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                        EAP_RESOURCES                           │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • is_confidential                  │
│ • title                      • is_active                        │
│ • description                • access_count                     │
│ • category                   • created_at                       │
│ • resource_type              • updated_at                       │
│ • content_url                                                   │
│ • contact_info                                                  │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                      EAP_ACCESS_LOGS                           │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • user_agent                       │
│ • resource_id (FK)           • accessed_at                      │
│ • employee_id (FK)           │
│ • access_type                │
│ • ip_address                 │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                      COMPANY_EVENTS                            │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • is_mandatory                     │
│ • title                      • department_ids[]                 │
│ • description                • role_filters[]                   │
│ • event_type                 • organizer_id (FK → users.id)     │
│ • start_datetime             • status                           │
│ • end_datetime               • registration_required            │
│ • location                   • registration_deadline            │
│ • is_virtual                 • created_at                       │
│ • meeting_link               • updated_at                       │
│ • max_participants           • created_by (FK → users.id)       │
│ • current_participants                                          │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                     EVENT_PARTICIPANTS                         │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • feedback_rating                  │
│ • event_id (FK)              • feedback_comments                │
│ • employee_id (FK)           • registered_at                    │
│ • registration_status        • UK(event_id, employee_id)        │
│ • attendance_status                                             │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                     WELLNESS_PROGRAMS                          │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • is_team_based                    │
│ • title                      • max_participants                 │
│ • description                • current_participants             │
│ • program_type               • status                           │
│ • start_date                 • created_at                       │
│ • end_date                   • created_by (FK → users.id)       │
│ • goal_type                                                     │
│ • goal_target                                                   │
│ • reward_points                                                 │
│ • reward_description                                            │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                   WELLNESS_PARTICIPANTS                        │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • points_earned                    │
│ • program_id (FK)            • joined_at                        │
│ • employee_id (FK)           • completed_at                     │
│ • team_name                  • UK(program_id, employee_id)      │
│ • current_progress                                              │
│ • goal_achieved                                                 │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                  WELLNESS_ACTIVITY_LOGS                        │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • notes                            │
│ • participant_id (FK)        • data_source                      │
│ • activity_date              • created_at                       │
│ • activity_value                                                │
└─────────────────────────────────────────────────────────────────┘

=====================================================
13. WORKFLOW AUTOMATION & APPROVALS
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                    WORKFLOW_TEMPLATES                          │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • is_active                        │
│ • name                       • version                          │
│ • description                • created_at                       │
│ • workflow_type              • updated_at                       │
│ • trigger_event              • created_by (FK → users.id)       │
│ • steps (JSONB)                                                 │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                    WORKFLOW_INSTANCES                          │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • priority                         │
│ • template_id (FK)           • due_date                         │
│ • entity_type                • metadata (JSONB)                 │
│ • entity_id                  • started_at                       │
│ • initiated_by (FK → users.id)• completed_at                    │
│ • current_step                                                  │
│ • status                                                        │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                 WORKFLOW_STEP_EXECUTIONS                       │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • due_date                         │
│ • workflow_instance_id (FK)  • started_at                       │
│ • step_number                • completed_at                     │
│ • step_name                  • escalated_to (FK → users.id)     │
│ • assigned_to (FK → users.id)• escalated_at                     │
│ • action_type                                                   │
│ • status                                                        │
│ • action_taken                                                  │
│ • comments                                                      │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                   AUTOMATED_REMINDERS                          │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • repeat_count                     │
│ • reminder_type              • sent_count                       │
│ • entity_type                • status                           │
│ • entity_id                  • last_sent_at                     │
│ • recipient_id (FK → users.id)• created_at                      │
│ • title                                                         │
│ • message                                                       │
│ • reminder_datetime                                             │
│ • repeat_interval                                               │
└─────────────────────────────────────────────────────────────────┘

=====================================================
14. COMPANY MANAGEMENT & BILLING
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                    SUBSCRIPTION_PLANS                          │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • price_per_month                  │
│ • plan_name (UK)             • billing_cycle                    │
│ • description                • is_active                        │
│ • max_employees              • created_at                       │
│ • features (JSONB)                                              │
│ • price_per_employee                                            │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                   COMPANY_SUBSCRIPTIONS                        │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • auto_renew                       │
│ • plan_id (FK)               • payment_method                   │
│ • start_date                 • billing_contact_id (FK → users.id)│
│ • end_date                   • created_at                       │
│ • employee_count             • updated_at                       │
│ • monthly_cost                                                  │
│ • status                                                        │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                     BILLING_INVOICES                           │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • currency                         │
│ • subscription_id (FK)       • status                           │
│ • invoice_number (UK)        • due_date                         │
│ • billing_period_start       • paid_date                        │
│ • billing_period_end         • payment_reference                │
│ • subtotal                   • payment_method                   │
│ • tax_amount                 • created_at                       │
│ • total_amount                                                  │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                      MODULE_SETTINGS                           │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • default_value                    │
│ • module_name                • validation_rules (JSONB)         │
│ • setting_key                • created_at                       │
│ • setting_value              • updated_at                       │
│ • setting_type               • updated_by (FK → users.id)       │
│ • description                • UK(module_name, setting_key)     │
│ • is_required                                                   │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                      FEATURE_TOGGLES                           │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • target_departments[]             │
│ • feature_name (UK)          • environment                      │
│ • description                • created_at                       │
│ • is_enabled                 • updated_at                       │
│ • rollout_percentage         • updated_by (FK → users.id)       │
│ • target_roles[]                                                │
└─────────────────────────────────────────────────────────────────┘

=====================================================
15. REPORTS & ANALYTICS
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                        SAVED_REPORTS                           │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • recipients[]                     │
│ • name                       • is_active                        │
│ • description                • last_generated                   │
│ • report_type                • next_generation                  │
│ • parameters (JSONB)         • created_at                       │
│ • schedule_frequency         • updated_at                       │
│ • schedule_day               • created_by (FK → users.id)       │
│ • schedule_time                                                 │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                       REPORT_HISTORY                           │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • parameters_used (JSONB)          │
│ • saved_report_id (FK)       • status                          │
│ • generated_by (FK → users.id)• error_message                  │
│ • file_path                  • created_at                       │
│ • file_size                                                     │
│ • generation_time_seconds                                       │
└─────────────────────────────────────────────────────────────────┘

=====================================================
16. KEY RELATIONSHIPS SUMMARY
=====================================================

CENTRAL HUB: USERS TABLE
┌─────────────────────────────────────────────────────────────────┐
│                         USERS (Central Entity)                 │
│                                                                 │
│  Connected to ALL major entities through foreign keys:         │
│                                                                 │
│  • user_sessions (1:M)           • performance_reviews (1:M)   │
│  • password_reset_tokens (1:M)   • performance_goals (1:M)     │
│  • employee_profiles (1:1)       • training_assignments (1:M)  │
│  • salary_profiles (1:1)         • training_sessions (1:M)     │
│  • bank_profiles (1:M)           • training_participants (1:M) │
│  • emergency_contacts (1:M)      • job_postings (1:M)          │
│  • departments (1:M supervisor)  • job_applications (1:M)      │
│  • attendance_records (1:M)      • interview_schedules (1:M)   │
│  • biostar_events (1:M)          • candidate_evaluations (1:M) │
│  • leave_balances (1:M)          • employee_documents (1:M)    │
│  • leave_applications (1:M)      • company_documents (1:M)     │
│  • pay_cycles (1:M)              • document_acknowledgments(1:M)│
│  • payroll_records (1:M)         • payroll_adjustments (1:M)   │
│  • employee_benefits (1:M)       • salary_adjustments (via     │
│                                    salary_profiles) (1:M)      │
│  • overtime_requests (1:M)       • overtime_records (1:M)      │
│  • overtime_calculations (1:M)   • overtime_budgets (1:M)      │
│  • employee_surveys (1:M)        • audit_logs (1:M)            │
│  • survey_responses (1:M)        • saved_reports (1:M)         │
│  • employee_recognitions (1:M)   • report_history (1:M)        │
│  • eap_access_logs (1:M)         • company_events (1:M)        │
│  • event_participants (1:M)      • wellness_participants (1:M) │
│  • automated_reminders (1:M)     • module_settings (1:M)       │
│  • wellness_participants (1:M)   • workflow_step_executions(1:M)│
│  • company_subscriptions (1:M)   • feature_toggles (1:M)       │
└─────────────────────────────────────────────────────────────────┘

MAJOR RELATIONSHIP PATTERNS:
═══════════════════════════════════════════════════════════════════

1. ONE-TO-ONE RELATIONSHIPS:
   • users ||--|| employee_profiles (extends user data)
   • users ||--|| salary_profiles (financial information)

2. ONE-TO-MANY RELATIONSHIPS (Most Common):
   • users ||--o{ [most tables] (user as owner/creator)
   • departments ||--o{ employee_profiles
   • leave_types ||--o{ leave_balances
   • leave_types ||--o{ leave_applications
   • pay_cycles ||--o{ payroll_records
   • training_modules ||--o{ employee_training_assignments
   • job_postings ||--o{ job_applications
   • document_categories ||--o{ employee_documents
   • document_categories ||--o{ company_documents
   • notification_templates ||--o{ notifications
   • saved_reports ||--o{ report_history
   • employee_surveys ||--o{ survey_responses
   • recognition_categories ||--o{ employee_recognitions
   • eap_resources ||--o{ eap_access_logs
   • company_events ||--o{ event_participants
   • wellness_programs ||--o{ wellness_participants
   • wellness_participants ||--o{ wellness_activity_logs
   • workflow_templates ||--o{ workflow_instances
   • workflow_instances ||--o{ workflow_step_executions
   • subscription_plans ||--o{ company_subscriptions
   • company_subscriptions ||--o{ billing_invoices
   • users ||--o{ bank_profiles (multiple bank accounts)
   • users ||--o{ emergency_contacts (multiple contacts)
   • salary_profiles ||--o{ salary_adjustments (salary history)
   • overtime_types ||--o{ overtime_requests (overtime requests by type)
   • overtime_types ||--o{ overtime_records (overtime records by type)
   • overtime_requests ||--o{ overtime_records (request to actual work)
   • departments ||--o{ overtime_approval_workflows (dept workflows)
   • departments ||--o{ overtime_budgets (department budgets)
   • overtime_records ||--|| overtime_calculations (one calculation per record)
   • payroll_records ||--o{ overtime_calculations (payroll integration)

3. SELF-REFERENCING RELATIONSHIPS:
   • departments.parent_department_id → departments.id
   • document_categories.parent_category_id → document_categories.id
   • users.created_by → users.id (audit trail)
   • users.updated_by → users.id (audit trail)

4. MANY-TO-MANY RELATIONSHIPS (via Junction Tables):
   • training_sessions }o--o{ users (via training_session_participants)
   • company_documents }o--o{ users (via document_acknowledgments)

5. COMPLEX RELATIONSHIPS:
   • job_applications → interview_schedules → candidate_evaluations
   • attendance_records ← biostar_events (BioStar integration)
   • performance_reviews → performance_goals (goal tracking)

=====================================================
17. SPECIAL FEATURES & NOTES
=====================================================

KENYAN COMPLIANCE FIELDS:
• employee_profiles.nssf_number (NSSF registration)
• employee_profiles.nhif_number (NHIF registration)
• employee_profiles.kra_pin (Kenya Revenue Authority PIN)
• employee_profiles.national_id (National ID number)
• payroll_records.nssf_deduction (6% NSSF deduction)
• payroll_records.nhif_deduction (2.75% NHIF/SHA deduction)
• payroll_records.housing_levy (1.5% Housing Levy)
• Default currency: KSH (Kenyan Shilling)

ENUM TYPES:
• payment_status_enum (draft, pending_approval, approved, processing, paid, failed, cancelled, on_hold)

OVERTIME MANAGEMENT FEATURES:
• Pre-approval workflow system with supervisor and admin approval
• Automatic overtime calculations with Kenyan tax compliance
• Budget tracking and limits per department/employee
• Integration with payroll system via overtime_calculations table
• Multiple overtime types with different rate multipliers
• Emergency overtime with fast-track approval
• Comprehensive audit trail for all overtime activities

BIOSTAR INTEGRATION:
• biostar_devices (device management)
• biostar_events (biometric events)
• attendance_records.biostar_synced (sync status)
• attendance_records.biostar_event_ids[] (linked events)

FLEXIBLE DATA STRUCTURES:
• performance_review_templates.criteria (JSONB)
• candidate_evaluations.evaluation_criteria (JSONB)
• notifications.metadata (JSONB)
• audit_logs.old_values/new_values (JSONB)
• saved_reports.parameters (JSONB)

ARRAY FIELDS:
• company_holidays.department_ids[] (department targeting)
• company_documents.role_access[] (role-based access)
• interview_schedules.interviewer_ids[] (multiple interviewers)
• training_venues.equipment[] (equipment list)
• training_venues.facilities[] (facility list)
• notification_templates.notification_channels[] (multi-channel)
• notification_templates.recipient_roles[] (role targeting)

AUDIT TRAIL:
• Most tables include: created_at, updated_at, created_by, updated_by
• audit_logs table tracks all changes
• activity_logs table tracks user activities
• Soft delete support with deleted_at, deleted_by fields

COMPUTED FIELDS:
• leave_balances.remaining_days (auto-calculated)

UNIQUE CONSTRAINTS:
• users.email, users.employee_id
• attendance_records(employee_id, date)
• leave_balances(employee_id, leave_type_id, year)
• payroll_records(employee_id, pay_cycle_id)
• employee_training_assignments(employee_id, training_module_id)
• training_session_participants(session_id, employee_id)
• document_acknowledgments(document_id, employee_id)
• survey_responses(survey_id, employee_id)
• event_participants(event_id, employee_id)
• wellness_participants(program_id, employee_id)
• module_settings(module_name, setting_key)
• subscription_plans.plan_name
• billing_invoices.invoice_number
• feature_toggles.feature_name
• recognition_categories.name

=====================================================
END OF ERD DIAGRAM
=====================================================
