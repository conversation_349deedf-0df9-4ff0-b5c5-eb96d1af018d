#!/usr/bin/env node

/**
 * WorkFlo Frontend-Backend Integration Test
 * Tests the connection between frontend and backend services
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  backendUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api',
  frontendUrl: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  timeout: 10000,
  verbose: process.argv.includes('--verbose'),
  testAuth: process.argv.includes('--auth'),
  testEndpoints: process.argv.includes('--endpoints'),
};

// Test credentials
const testCredentials = {
  admin: { email: '<EMAIL>', password: 'admin123' },
  supervisor: { email: '<EMAIL>', password: 'password123' },
  hr: { email: '<EMAIL>', password: 'password123' },
  accountant: { email: '<EMAIL>', password: 'password123' },
  employee: { email: '<EMAIL>', password: 'password123' },
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logVerbose(message) {
  if (config.verbose) {
    log(`  ${message}`, 'cyan');
  }
}

async function testBackendHealth() {
  log('\n🏥 Testing Backend Health...', 'blue');
  
  try {
    const response = await axios.get(`${config.backendUrl}/health/`, {
      timeout: config.timeout,
    });
    
    if (response.status === 200) {
      log('✅ Backend health check passed', 'green');
      logVerbose(`Status: ${response.status}`);
      logVerbose(`Response: ${JSON.stringify(response.data, null, 2)}`);
      return true;
    } else {
      log(`❌ Backend health check failed with status: ${response.status}`, 'red');
      return false;
    }
  } catch (error) {
    log(`❌ Backend health check failed: ${error.message}`, 'red');
    logVerbose(`Error details: ${error.stack}`);
    return false;
  }
}

async function testBackendAuth() {
  log('\n🔐 Testing Backend Authentication...', 'blue');
  
  try {
    // Test login with admin credentials
    const loginResponse = await axios.post(`${config.backendUrl}/auth/login/`, testCredentials.admin, {
      timeout: config.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (loginResponse.status === 200 && loginResponse.data.access) {
      log('✅ Authentication successful', 'green');
      logVerbose(`Access token received: ${loginResponse.data.access.substring(0, 20)}...`);
      
      // Test authenticated endpoint
      const userResponse = await axios.get(`${config.backendUrl}/users/me/`, {
        timeout: config.timeout,
        headers: {
          'Authorization': `Bearer ${loginResponse.data.access}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (userResponse.status === 200) {
        log('✅ Authenticated endpoint access successful', 'green');
        logVerbose(`User data: ${JSON.stringify(userResponse.data, null, 2)}`);
        return { success: true, token: loginResponse.data.access };
      } else {
        log(`❌ Authenticated endpoint failed with status: ${userResponse.status}`, 'red');
        return { success: false };
      }
    } else {
      log(`❌ Authentication failed with status: ${loginResponse.status}`, 'red');
      return { success: false };
    }
  } catch (error) {
    log(`❌ Authentication test failed: ${error.message}`, 'red');
    if (error.response) {
      logVerbose(`Response status: ${error.response.status}`);
      logVerbose(`Response data: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    return { success: false };
  }
}

async function testCorsConfiguration() {
  log('\n🌐 Testing CORS Configuration...', 'blue');
  
  try {
    const response = await axios.options(`${config.backendUrl}/auth/login/`, {
      timeout: config.timeout,
      headers: {
        'Origin': config.frontendUrl,
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type,Authorization',
      },
    });
    
    const corsHeaders = {
      'access-control-allow-origin': response.headers['access-control-allow-origin'],
      'access-control-allow-methods': response.headers['access-control-allow-methods'],
      'access-control-allow-headers': response.headers['access-control-allow-headers'],
      'access-control-allow-credentials': response.headers['access-control-allow-credentials'],
    };
    
    log('✅ CORS preflight successful', 'green');
    logVerbose(`CORS headers: ${JSON.stringify(corsHeaders, null, 2)}`);
    return true;
  } catch (error) {
    log(`❌ CORS test failed: ${error.message}`, 'red');
    return false;
  }
}

async function testApiEndpoints(authToken) {
  log('\n📡 Testing API Endpoints...', 'blue');
  
  const endpoints = [
    { path: '/users/', method: 'GET', name: 'Users List' },
    { path: '/departments/', method: 'GET', name: 'Departments' },
    { path: '/employee-profiles/', method: 'GET', name: 'Employee Profiles' },
    { path: '/leave-types/', method: 'GET', name: 'Leave Types' },
    { path: '/pay-cycles/', method: 'GET', name: 'Pay Cycles' },
  ];
  
  const results = [];
  
  for (const endpoint of endpoints) {
    try {
      const response = await axios({
        method: endpoint.method,
        url: `${config.backendUrl}${endpoint.path}`,
        timeout: config.timeout,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (response.status === 200) {
        log(`✅ ${endpoint.name}: OK`, 'green');
        logVerbose(`Response: ${JSON.stringify(response.data, null, 2).substring(0, 200)}...`);
        results.push({ endpoint: endpoint.name, status: 'success' });
      } else {
        log(`❌ ${endpoint.name}: Failed (${response.status})`, 'red');
        results.push({ endpoint: endpoint.name, status: 'failed', code: response.status });
      }
    } catch (error) {
      log(`❌ ${endpoint.name}: Error - ${error.message}`, 'red');
      results.push({ endpoint: endpoint.name, status: 'error', error: error.message });
    }
  }
  
  return results;
}

async function generateReport(results) {
  log('\n📊 Generating Integration Test Report...', 'blue');
  
  const report = {
    timestamp: new Date().toISOString(),
    configuration: config,
    results: results,
    summary: {
      total: Object.keys(results).length,
      passed: Object.values(results).filter(r => r === true || (r && r.success)).length,
      failed: Object.values(results).filter(r => r === false || (r && !r.success)).length,
    },
  };
  
  const reportPath = path.join(__dirname, '..', 'test-results', 'integration-report.json');
  
  // Ensure directory exists
  const reportDir = path.dirname(reportPath);
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  log(`✅ Report saved to: ${reportPath}`, 'green');
  return report;
}

async function main() {
  log('🚀 WorkFlo Frontend-Backend Integration Test', 'bright');
  log(`Backend URL: ${config.backendUrl}`, 'cyan');
  log(`Frontend URL: ${config.frontendUrl}`, 'cyan');
  
  const results = {};
  
  // Test backend health
  results.health = await testBackendHealth();
  
  // Test CORS configuration
  results.cors = await testCorsConfiguration();
  
  // Test authentication
  const authResult = await testBackendAuth();
  results.auth = authResult.success;
  
  // Test API endpoints if authentication succeeded
  if (authResult.success && (config.testEndpoints || config.testAuth)) {
    results.endpoints = await testApiEndpoints(authResult.token);
  }
  
  // Generate report
  const report = await generateReport(results);
  
  // Summary
  log('\n📋 Test Summary:', 'bright');
  log(`✅ Passed: ${report.summary.passed}`, 'green');
  log(`❌ Failed: ${report.summary.failed}`, 'red');
  
  if (report.summary.failed > 0) {
    log('\n⚠️  Some tests failed. Check the report for details.', 'yellow');
    process.exit(1);
  } else {
    log('\n🎉 All tests passed! Frontend-Backend integration is working correctly.', 'green');
    process.exit(0);
  }
}

// Handle errors
process.on('unhandledRejection', (error) => {
  log(`❌ Unhandled error: ${error.message}`, 'red');
  process.exit(1);
});

// Run the tests
main().catch((error) => {
  log(`❌ Test execution failed: ${error.message}`, 'red');
  process.exit(1);
});
