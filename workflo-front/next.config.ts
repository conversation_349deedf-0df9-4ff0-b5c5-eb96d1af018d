import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  env: {
    // Backend API Configuration
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api',
    NEXT_PUBLIC_USE_MOCK_API: process.env.NEXT_PUBLIC_USE_MOCK_API || 'false',

    // BioStar Configuration
    NEXT_PUBLIC_BIOSTAR_API_URL: process.env.NEXT_PUBLIC_BIOSTAR_API_URL || 'https://bs2api.biostar2.com',
    NEXT_PUBLIC_BIOSTAR_USERNAME: process.env.NEXT_PUBLIC_BIOSTAR_USERNAME || '',
    NEXT_PUBLIC_BIOSTAR_PASSWORD: process.env.NEXT_PUBLIC_BIOSTAR_PASSWORD || '',
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api'}/:path*`,
      },
    ];
  },
};

export default nextConfig;
