{"name": "workflo-front", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:debug": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "test:biostar": "node scripts/test-biostar-connection.js", "test:biostar:verbose": "node scripts/test-biostar-connection.js --verbose", "test:biostar:production": "node scripts/test-biostar-connection.js --config production", "test:integration": "node scripts/test-backend-integration.js", "test:integration:verbose": "node scripts/test-backend-integration.js --verbose", "test:integration:auth": "node scripts/test-backend-integration.js --auth --verbose", "test:integration:endpoints": "node scripts/test-backend-integration.js --endpoints --verbose", "biostar:monitor": "node -e \"require('./src/lib/biostarMonitoring').biostarMonitoring.startMonitoring(); console.log('BioStar monitoring started...');\""}, "dependencies": {"@tanstack/react-query": "^5.62.7", "axios": "^1.8.4", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.468.0", "next": "^15.3.2", "react": "^19.0.0", "react-datepicker": "^7.5.0", "react-dom": "^19.0.0", "react-hook-form": "^7.53.2", "react-icons": "^5.4.0", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/forms": "^0.5.9", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.8", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "babel-jest": "^29.7.0", "eslint": "^9", "eslint-config-next": "15.1.8", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "jest-watch-typeahead": "^2.2.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}