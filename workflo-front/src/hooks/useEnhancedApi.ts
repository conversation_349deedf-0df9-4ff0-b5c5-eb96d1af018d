import { useState, useEffect, useCallback } from 'react';
import { enhancedApi } from '@/lib/enhancedApi';
import { 
  EnhancedApiResponse, 
  EnhancedUser, 
  EnhancedDepartment,
  EnhancedEmployeeProfile,
  EnhancedSalaryProfile,
  EnhancedBankProfile,
  EnhancedEmergencyContact,
  EnhancedAttendanceRecord,
  EnhancedLeaveApplication,
  EnhancedLeaveType,
  EnhancedLeaveBalance,
  EnhancedPayCycle,
  EnhancedPayrollRecord,
  EmployeeFilters,
  AttendanceFilters,
  LeaveFilters,
  PayrollFilters,
  PaginationParams
} from '@/types/enhanced';

// Generic hook for API operations
export function useApiState<T>() {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const execute = useCallback(async (apiCall: () => Promise<EnhancedApiResponse<T>>) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiCall();
      if (response.success) {
        setData(response.data || null);
      } else {
        setError(response.error || 'An error occurred');
      }
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
  }, []);

  return { data, loading, error, execute, reset, setData };
}

// Generic hook for paginated data
export function usePaginatedApi<T>() {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    count: 0,
    next: null as string | null,
    previous: null as string | null,
    page: 1,
    pageSize: 20
  });

  const execute = useCallback(async (
    apiCall: (params?: any) => Promise<EnhancedApiResponse<any>>,
    params?: any
  ) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiCall(params);
      if (response.success) {
        if (response.results) {
          setData(response.results);
          setPagination(prev => ({
            ...prev,
            count: response.count || 0,
            next: response.next || null,
            previous: response.previous || null
          }));
        } else {
          setData(response.data || []);
        }
      } else {
        setError(response.error || 'An error occurred');
      }
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  const reset = useCallback(() => {
    setData([]);
    setError(null);
    setLoading(false);
    setPagination({
      count: 0,
      next: null,
      previous: null,
      page: 1,
      pageSize: 20
    });
  }, []);

  return { data, loading, error, pagination, execute, reset, setData };
}

// User management hooks
export function useUsers(filters?: PaginationParams) {
  const { data, loading, error, pagination, execute, reset } = usePaginatedApi<EnhancedUser>();

  const fetchUsers = useCallback(() => {
    execute(() => enhancedApi.getUsers(filters), filters);
  }, [execute, filters]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  return { users: data, loading, error, pagination, refetch: fetchUsers, reset };
}

export function useUser(id: number | null) {
  const { data, loading, error, execute, reset } = useApiState<EnhancedUser>();

  const fetchUser = useCallback(() => {
    if (id) {
      execute(() => enhancedApi.getUser(id));
    }
  }, [execute, id]);

  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  return { user: data, loading, error, refetch: fetchUser, reset };
}

// Department management hooks
export function useDepartments(filters?: PaginationParams) {
  const { data, loading, error, pagination, execute, reset } = usePaginatedApi<EnhancedDepartment>();

  const fetchDepartments = useCallback(() => {
    execute(() => enhancedApi.getDepartments(filters), filters);
  }, [execute, filters]);

  useEffect(() => {
    fetchDepartments();
  }, [fetchDepartments]);

  return { departments: data, loading, error, pagination, refetch: fetchDepartments, reset };
}

export function useDepartment(id: number | null) {
  const { data, loading, error, execute, reset } = useApiState<EnhancedDepartment>();

  const fetchDepartment = useCallback(() => {
    if (id) {
      execute(() => enhancedApi.getDepartment(id));
    }
  }, [execute, id]);

  useEffect(() => {
    fetchDepartment();
  }, [fetchDepartment]);

  return { department: data, loading, error, refetch: fetchDepartment, reset };
}

// Employee Profile management hooks
export function useEmployeeProfiles(filters?: EmployeeFilters) {
  const { data, loading, error, pagination, execute, reset } = usePaginatedApi<EnhancedEmployeeProfile>();

  const fetchEmployeeProfiles = useCallback(() => {
    execute(() => enhancedApi.getEmployeeProfiles(filters), filters);
  }, [execute, filters]);

  useEffect(() => {
    fetchEmployeeProfiles();
  }, [fetchEmployeeProfiles]);

  return { employeeProfiles: data, loading, error, pagination, refetch: fetchEmployeeProfiles, reset };
}

export function useEmployeeProfile(id: number | null) {
  const { data, loading, error, execute, reset } = useApiState<EnhancedEmployeeProfile>();

  const fetchEmployeeProfile = useCallback(() => {
    if (id) {
      execute(() => enhancedApi.getEmployeeProfile(id));
    }
  }, [execute, id]);

  useEffect(() => {
    fetchEmployeeProfile();
  }, [fetchEmployeeProfile]);

  return { employeeProfile: data, loading, error, refetch: fetchEmployeeProfile, reset };
}

// Salary Profile management hooks
export function useSalaryProfiles(filters?: PaginationParams) {
  const { data, loading, error, pagination, execute, reset } = usePaginatedApi<EnhancedSalaryProfile>();

  const fetchSalaryProfiles = useCallback(() => {
    execute(() => enhancedApi.getSalaryProfiles(filters), filters);
  }, [execute, filters]);

  useEffect(() => {
    fetchSalaryProfiles();
  }, [fetchSalaryProfiles]);

  return { salaryProfiles: data, loading, error, pagination, refetch: fetchSalaryProfiles, reset };
}

// Bank Profile management hooks
export function useBankProfiles(employeeId?: number) {
  const { data, loading, error, execute, reset } = usePaginatedApi<EnhancedBankProfile>();

  const fetchBankProfiles = useCallback(() => {
    execute(() => enhancedApi.getBankProfiles(employeeId));
  }, [execute, employeeId]);

  useEffect(() => {
    fetchBankProfiles();
  }, [fetchBankProfiles]);

  return { bankProfiles: data, loading, error, refetch: fetchBankProfiles, reset };
}

// Emergency Contact management hooks
export function useEmergencyContacts(employeeId?: number) {
  const { data, loading, error, execute, reset } = usePaginatedApi<EnhancedEmergencyContact>();

  const fetchEmergencyContacts = useCallback(() => {
    execute(() => enhancedApi.getEmergencyContacts(employeeId));
  }, [execute, employeeId]);

  useEffect(() => {
    fetchEmergencyContacts();
  }, [fetchEmergencyContacts]);

  return { emergencyContacts: data, loading, error, refetch: fetchEmergencyContacts, reset };
}

// Attendance management hooks
export function useAttendanceRecords(filters?: AttendanceFilters) {
  const { data, loading, error, pagination, execute, reset } = usePaginatedApi<EnhancedAttendanceRecord>();

  const fetchAttendanceRecords = useCallback(() => {
    execute(() => enhancedApi.getAttendanceRecords(filters), filters);
  }, [execute, filters]);

  useEffect(() => {
    fetchAttendanceRecords();
  }, [fetchAttendanceRecords]);

  return { attendanceRecords: data, loading, error, pagination, refetch: fetchAttendanceRecords, reset };
}

// Leave management hooks
export function useLeaveTypes() {
  const { data, loading, error, execute, reset } = usePaginatedApi<EnhancedLeaveType>();

  const fetchLeaveTypes = useCallback(() => {
    execute(() => enhancedApi.getLeaveTypes());
  }, [execute]);

  useEffect(() => {
    fetchLeaveTypes();
  }, [fetchLeaveTypes]);

  return { leaveTypes: data, loading, error, refetch: fetchLeaveTypes, reset };
}

export function useLeaveBalances(employeeId?: number) {
  const { data, loading, error, execute, reset } = usePaginatedApi<EnhancedLeaveBalance>();

  const fetchLeaveBalances = useCallback(() => {
    execute(() => enhancedApi.getLeaveBalances(employeeId));
  }, [execute, employeeId]);

  useEffect(() => {
    fetchLeaveBalances();
  }, [fetchLeaveBalances]);

  return { leaveBalances: data, loading, error, refetch: fetchLeaveBalances, reset };
}

export function useLeaveApplications(filters?: LeaveFilters) {
  const { data, loading, error, pagination, execute, reset } = usePaginatedApi<EnhancedLeaveApplication>();

  const fetchLeaveApplications = useCallback(() => {
    execute(() => enhancedApi.getLeaveApplications(filters), filters);
  }, [execute, filters]);

  useEffect(() => {
    fetchLeaveApplications();
  }, [fetchLeaveApplications]);

  return { leaveApplications: data, loading, error, pagination, refetch: fetchLeaveApplications, reset };
}

// Payroll management hooks
export function usePayCycles(filters?: PaginationParams) {
  const { data, loading, error, pagination, execute, reset } = usePaginatedApi<EnhancedPayCycle>();

  const fetchPayCycles = useCallback(() => {
    execute(() => enhancedApi.getPayCycles(filters), filters);
  }, [execute, filters]);

  useEffect(() => {
    fetchPayCycles();
  }, [fetchPayCycles]);

  return { payCycles: data, loading, error, pagination, refetch: fetchPayCycles, reset };
}

export function usePayrollRecords(filters?: PayrollFilters) {
  const { data, loading, error, pagination, execute, reset } = usePaginatedApi<EnhancedPayrollRecord>();

  const fetchPayrollRecords = useCallback(() => {
    execute(() => enhancedApi.getPayrollRecords(filters), filters);
  }, [execute, filters]);

  useEffect(() => {
    fetchPayrollRecords();
  }, [fetchPayrollRecords]);

  return { payrollRecords: data, loading, error, pagination, refetch: fetchPayrollRecords, reset };
}

// CRUD operation hooks
export function useCrudOperations<T>() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const executeOperation = useCallback(async (
    operation: () => Promise<EnhancedApiResponse<T>>,
    onSuccess?: (data: T) => void,
    onError?: (error: string) => void
  ) => {
    setLoading(true);
    setError(null);

    try {
      const response = await operation();
      if (response.success) {
        onSuccess?.(response.data!);
        return response.data;
      } else {
        const errorMessage = response.error || 'Operation failed';
        setError(errorMessage);
        onError?.(errorMessage);
        return null;
      }
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(errorMessage);
      onError?.(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return { loading, error, executeOperation, setError };
}

// Dashboard statistics hook
export function useDashboardStats() {
  const [stats, setStats] = useState({
    totalEmployees: 0,
    totalDepartments: 0,
    pendingLeaves: 0,
    activeAttendance: 0,
    monthlyPayroll: 0,
    loading: true,
    error: null as string | null
  });

  const fetchStats = useCallback(async () => {
    setStats(prev => ({ ...prev, loading: true, error: null }));

    try {
      // Fetch multiple endpoints in parallel
      const [employeesRes, departmentsRes, leavesRes, attendanceRes, payrollRes] = await Promise.allSettled([
        enhancedApi.getEmployeeProfiles({ page_size: 1 }),
        enhancedApi.getDepartments({ page_size: 1 }),
        enhancedApi.getLeaveApplications({ status: 'pending', page_size: 1 }),
        enhancedApi.getAttendanceRecords({
          date_from: new Date().toISOString().split('T')[0],
          page_size: 1
        }),
        enhancedApi.getPayrollRecords({ page_size: 1 })
      ]);

      setStats({
        totalEmployees: employeesRes.status === 'fulfilled' && employeesRes.value.success
          ? employeesRes.value.count || 0 : 0,
        totalDepartments: departmentsRes.status === 'fulfilled' && departmentsRes.value.success
          ? departmentsRes.value.count || 0 : 0,
        pendingLeaves: leavesRes.status === 'fulfilled' && leavesRes.value.success
          ? leavesRes.value.count || 0 : 0,
        activeAttendance: attendanceRes.status === 'fulfilled' && attendanceRes.value.success
          ? attendanceRes.value.count || 0 : 0,
        monthlyPayroll: payrollRes.status === 'fulfilled' && payrollRes.value.success
          ? payrollRes.value.count || 0 : 0,
        loading: false,
        error: null
      });
    } catch (error: any) {
      setStats(prev => ({
        ...prev,
        loading: false,
        error: error.message || 'Failed to fetch dashboard statistics'
      }));
    }
  }, []);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return { ...stats, refetch: fetchStats };
}

// System health hook
export function useSystemHealth() {
  const { data, loading, error, execute } = useApiState<any>();

  const checkHealth = useCallback(() => {
    execute(() => enhancedApi.getSystemHealth());
  }, [execute]);

  useEffect(() => {
    checkHealth();
    // Check health every 30 seconds
    const interval = setInterval(checkHealth, 30000);
    return () => clearInterval(interval);
  }, [checkHealth]);

  return { health: data, loading, error, refetch: checkHealth };
}
