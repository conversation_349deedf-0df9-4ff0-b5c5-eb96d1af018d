import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { AuthTokens, ApiResponse } from '@/types';
import { mockApi, setMockUserEmail, clearMockUserEmail } from './mockApi';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';
const USE_MOCK_API = process.env.NEXT_PUBLIC_USE_MOCK_API === 'true' || false; // Default to false for real backend integration

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token management
class TokenManager {
  private static readonly ACCESS_TOKEN_KEY = 'access_token';
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token';

  static getAccessToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(this.ACCESS_TOKEN_KEY);
  }

  static getRefreshToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  static setTokens(tokens: AuthTokens): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(this.ACCESS_TOKEN_KEY, tokens.access);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, tokens.refresh);
  }

  static clearTokens(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(this.ACCESS_TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
  }

  static isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch {
      return true;
    }
  }
}

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = TokenManager.getAccessToken();
    if (token && !TokenManager.isTokenExpired(token)) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      const refreshToken = TokenManager.getRefreshToken();
      if (refreshToken && !TokenManager.isTokenExpired(refreshToken)) {
        try {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh/`, {
            refresh: refreshToken,
          });

          const newTokens: AuthTokens = response.data;
          TokenManager.setTokens(newTokens);

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${newTokens.access}`;
          return apiClient(originalRequest);
        } catch (refreshError) {
          // Refresh failed, redirect to login
          TokenManager.clearTokens();
          if (typeof window !== 'undefined') {
            window.location.href = '/login';
          }
          return Promise.reject(refreshError);
        }
      } else {
        // No valid refresh token, redirect to login
        TokenManager.clearTokens();
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }
      }
    }

    return Promise.reject(error);
  }
);

// API wrapper class
class ApiService {
  // Generic request method
  private async request<T>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await apiClient(config);
      return response.data;
    } catch (error) {
      console.error('API Request Error:', error);
      throw error;
    }
  }

  // GET request
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'GET', url });
  }

  // POST request
  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'POST', url, data });
  }

  // PUT request
  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'PUT', url, data });
  }

  // PATCH request
  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'PATCH', url, data });
  }

  // DELETE request
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'DELETE', url });
  }

  // File upload
  async uploadFile<T>(url: string, file: File, onProgress?: (progress: number) => void): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    return this.request<T>({
      method: 'POST',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
  }
}

// Create API service instance
export const api = new ApiService();

// Authentication API
export const authApi = {
  login: async (credentials: { email: string; password: string }) => {
    if (USE_MOCK_API) {
      const tokens = await mockApi.login(credentials);
      setMockUserEmail(credentials.email);
      return tokens;
    }
    return api.post<AuthTokens>('/auth/login/', credentials);
  },

  refreshToken: (refreshToken: string) =>
    api.post<AuthTokens>('/auth/refresh/', { refresh: refreshToken }),

  logout: () => {
    TokenManager.clearTokens();
    if (USE_MOCK_API) {
      clearMockUserEmail();
    }
    if (typeof window !== 'undefined') {
      window.location.href = '/login';
    }
  },

  getCurrentUser: () => {
    if (USE_MOCK_API) {
      return mockApi.getCurrentUser();
    }
    return api.get('/users/me/');
  },
};

// Employee API
export const employeeApi = {
  getAll: () => {
    if (USE_MOCK_API) {
      return mockApi.getEmployees();
    }
    return api.get('/get_employees/');
  },
  getById: (id: number) => {
    if (USE_MOCK_API) {
      return mockApi.getEmployeeById(id);
    }
    return api.get(`/employees/${id}/`);
  },
  create: (data: any) => api.post('/new_employee/', data),
  update: (id: number, data: any) => api.put(`/new_employee/${id}/`, data),
  delete: (id: number) => api.delete(`/employees/${id}/`),
  getProfile: () => {
    if (USE_MOCK_API) {
      return mockApi.getCurrentUser();
    }
    return api.get('/employees/me/');
  },
};

// Department API
export const departmentApi = {
  getAll: () => api.get('/get_departments/'),
  create: (data: any) => api.post('/departments/', data),
  update: (id: number, data: any) => api.put(`/departments/${id}/`, data),
  delete: (id: number) => api.delete(`/departments/${id}/`),
};

// Attendance API
export const attendanceApi = {
  getMyAttendance: () => api.get('/attendance/'),
  clockIn: () => api.post('/attendance/', { action: 'clock_in' }),
  clockOut: () => api.post('/attendance/', { action: 'clock_out' }),
  getByEmployee: (employeeId: number) => api.get(`/attendance/?employee=${employeeId}`),
};

// Leave API
export const leaveApi = {
  getMyLeaves: () => api.get('/leave_applications/'),
  getPendingLeaves: () => api.get('/pending_leave/'),
  getRejectedLeaves: () => api.get('/my_rejected_leaves/'),
  apply: (data: any) => api.post('/leave_applications/', data),
  approve: (id: number, data: any) => api.put(`/pending_leave/${id}/`, data),
  reject: (id: number, data: any) => api.put(`/pending_leave/${id}/`, data),
};

// Payroll API
export const payrollApi = {
  getMyPayroll: () => api.get('/payroll/'),
  getPayCycles: () => api.get('/paycycle/'),
  createPayCycle: (data: any) => api.post('/paycycle/', data),
  markPaid: (id: number) => api.patch(`/paycycle/${id}/mark-paid/`),
  bulkCreatePayroll: (data: any) => api.post('/payroll_create/bulk-create/', data),
  getEmployeePayroll: (employeeId: number, payCycleId: number) =>
    api.get(`/payroll_create/employee-detail/?employee=${employeeId}&pay_cycle=${payCycleId}`),
};

// Performance API
export const performanceApi = {
  getMyReviews: () => api.get('/performance_reviews/'),
  getTeamReviews: () => api.get('/reviews/'),
  create: (data: any) => api.post('/performance_reviews/', data),
  update: (id: number, data: any) => api.put(`/performance_reviews/${id}/`, data),
};

// Training API
export const trainingApi = {
  getModules: () => api.get('/training_modules/'),
  getMyTrainings: () => api.get('/employee_trainings/'),
  assignTraining: (data: any) => api.post('/employee_trainings/', data),
  updateProgress: (id: number, data: any) => api.put(`/employee_trainings/${id}/`, data),
};

// Job API
export const jobApi = {
  getPostings: () => api.get('/get_job_postings/'),
  getCandidates: () => api.get('/candidates/'),
  createPosting: (data: any) => api.post('/job_postings/', data),
  apply: (data: any) => api.post('/candidates/', data),
};

// Document API
export const documentApi = {
  getMyDocuments: () => api.get('/user_documents/'),
  getAllDocuments: () => api.get('/admin_documents/'),
  getCompanyDocuments: () => api.get('/company_documents/'),
  upload: (file: File, onProgress?: (progress: number) => void) =>
    api.uploadFile('/employee_documents/', file, onProgress),
};

// Benefits API
export const benefitApi = {
  getAll: () => api.get('/benefits/'),
  create: (data: any) => api.post('/benefits/', data),
  update: (id: number, data: any) => api.put(`/benefits/${id}/`, data),
  delete: (id: number) => api.delete(`/benefits/${id}/`),
};

// Export token manager for use in components
export { TokenManager };

// Export the configured axios instance for custom requests
export { apiClient };
