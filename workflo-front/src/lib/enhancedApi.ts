import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { 
  EnhancedApiResponse, 
  EnhancedUser, 
  EnhancedDepartment,
  EnhancedEmployeeProfile,
  EnhancedSalaryProfile,
  EnhancedBankProfile,
  EnhancedEmergencyContact,
  EnhancedAttendanceRecord,
  EnhancedLeaveApplication,
  EnhancedLeaveType,
  EnhancedLeaveBalance,
  EnhancedPayCycle,
  EnhancedPayrollRecord,
  EmployeeFilters,
  AttendanceFilters,
  LeaveFilters,
  PayrollFilters,
  PaginationParams
} from '@/types/enhanced';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

// Create axios instance with enhanced configuration
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // Increased timeout for complex operations
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for token refresh and error handling
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh/`, {
            refresh: refreshToken
          });
          
          const { access } = response.data;
          localStorage.setItem('access_token', access);
          
          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);

// Enhanced API Service Class
class EnhancedApiService {
  // Generic request method with enhanced error handling
  private async request<T>(config: AxiosRequestConfig): Promise<EnhancedApiResponse<T>> {
    try {
      const response: AxiosResponse<T> = await apiClient(config);
      return {
        success: true,
        data: response.data,
        message: 'Request successful'
      };
    } catch (error: any) {
      console.error('API Request Error:', error);
      
      const errorResponse: EnhancedApiResponse<T> = {
        success: false,
        error: error.response?.data?.detail || error.message || 'An error occurred',
        errors: error.response?.data?.errors || {}
      };
      
      return errorResponse;
    }
  }

  // Generic CRUD methods
  async get<T>(url: string, params?: any): Promise<EnhancedApiResponse<T>> {
    return this.request<T>({ method: 'GET', url, params });
  }

  async post<T>(url: string, data?: any): Promise<EnhancedApiResponse<T>> {
    return this.request<T>({ method: 'POST', url, data });
  }

  async put<T>(url: string, data?: any): Promise<EnhancedApiResponse<T>> {
    return this.request<T>({ method: 'PUT', url, data });
  }

  async patch<T>(url: string, data?: any): Promise<EnhancedApiResponse<T>> {
    return this.request<T>({ method: 'PATCH', url, data });
  }

  async delete<T>(url: string): Promise<EnhancedApiResponse<T>> {
    return this.request<T>({ method: 'DELETE', url });
  }

  // Authentication methods
  async login(email: string, password: string) {
    return this.post('/auth/login/', { email, password });
  }

  async refreshToken(refresh: string) {
    return this.post('/auth/refresh/', { refresh });
  }

  async logout() {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    return { success: true };
  }

  // User management
  async getUsers(filters?: PaginationParams) {
    return this.get<EnhancedApiResponse<EnhancedUser[]>>('/users/', filters);
  }

  async getUser(id: number) {
    return this.get<EnhancedUser>(`/users/${id}/`);
  }

  async createUser(data: Partial<EnhancedUser>) {
    return this.post<EnhancedUser>('/users/', data);
  }

  async updateUser(id: number, data: Partial<EnhancedUser>) {
    return this.patch<EnhancedUser>(`/users/${id}/`, data);
  }

  async deleteUser(id: number) {
    return this.delete(`/users/${id}/`);
  }

  // Department management
  async getDepartments(filters?: PaginationParams) {
    return this.get<EnhancedApiResponse<EnhancedDepartment[]>>('/departments/', filters);
  }

  async getDepartment(id: number) {
    return this.get<EnhancedDepartment>(`/departments/${id}/`);
  }

  async createDepartment(data: Partial<EnhancedDepartment>) {
    return this.post<EnhancedDepartment>('/departments/', data);
  }

  async updateDepartment(id: number, data: Partial<EnhancedDepartment>) {
    return this.patch<EnhancedDepartment>(`/departments/${id}/`, data);
  }

  async deleteDepartment(id: number) {
    return this.delete(`/departments/${id}/`);
  }

  // Employee Profile management
  async getEmployeeProfiles(filters?: EmployeeFilters) {
    return this.get<EnhancedApiResponse<EnhancedEmployeeProfile[]>>('/employee-profiles/', filters);
  }

  async getEmployeeProfile(id: number) {
    return this.get<EnhancedEmployeeProfile>(`/employee-profiles/${id}/`);
  }

  async createEmployeeProfile(data: Partial<EnhancedEmployeeProfile>) {
    return this.post<EnhancedEmployeeProfile>('/employee-profiles/', data);
  }

  async updateEmployeeProfile(id: number, data: Partial<EnhancedEmployeeProfile>) {
    return this.patch<EnhancedEmployeeProfile>(`/employee-profiles/${id}/`, data);
  }

  async deleteEmployeeProfile(id: number) {
    return this.delete(`/employee-profiles/${id}/`);
  }

  // Salary Profile management
  async getSalaryProfiles(filters?: PaginationParams) {
    return this.get<EnhancedApiResponse<EnhancedSalaryProfile[]>>('/salary-profiles/', filters);
  }

  async getSalaryProfile(id: number) {
    return this.get<EnhancedSalaryProfile>(`/salary-profiles/${id}/`);
  }

  async createSalaryProfile(data: Partial<EnhancedSalaryProfile>) {
    return this.post<EnhancedSalaryProfile>('/salary-profiles/', data);
  }

  async updateSalaryProfile(id: number, data: Partial<EnhancedSalaryProfile>) {
    return this.patch<EnhancedSalaryProfile>(`/salary-profiles/${id}/`, data);
  }

  async deleteSalaryProfile(id: number) {
    return this.delete(`/salary-profiles/${id}/`);
  }

  // Bank Profile management
  async getBankProfiles(employeeId?: number) {
    const params = employeeId ? { employee_id: employeeId } : {};
    return this.get<EnhancedApiResponse<EnhancedBankProfile[]>>('/bank-profiles/', params);
  }

  async getBankProfile(id: number) {
    return this.get<EnhancedBankProfile>(`/bank-profiles/${id}/`);
  }

  async createBankProfile(data: Partial<EnhancedBankProfile>) {
    return this.post<EnhancedBankProfile>('/bank-profiles/', data);
  }

  async updateBankProfile(id: number, data: Partial<EnhancedBankProfile>) {
    return this.patch<EnhancedBankProfile>(`/bank-profiles/${id}/`, data);
  }

  async deleteBankProfile(id: number) {
    return this.delete(`/bank-profiles/${id}/`);
  }

  // Emergency Contact management
  async getEmergencyContacts(employeeId?: number) {
    const params = employeeId ? { employee_id: employeeId } : {};
    return this.get<EnhancedApiResponse<EnhancedEmergencyContact[]>>('/emergency-contacts/', params);
  }

  async getEmergencyContact(id: number) {
    return this.get<EnhancedEmergencyContact>(`/emergency-contacts/${id}/`);
  }

  async createEmergencyContact(data: Partial<EnhancedEmergencyContact>) {
    return this.post<EnhancedEmergencyContact>('/emergency-contacts/', data);
  }

  async updateEmergencyContact(id: number, data: Partial<EnhancedEmergencyContact>) {
    return this.patch<EnhancedEmergencyContact>(`/emergency-contacts/${id}/`, data);
  }

  async deleteEmergencyContact(id: number) {
    return this.delete(`/emergency-contacts/${id}/`);
  }

  // Attendance management
  async getAttendanceRecords(filters?: AttendanceFilters) {
    return this.get<EnhancedApiResponse<EnhancedAttendanceRecord[]>>('/attendance-records/', filters);
  }

  async getAttendanceRecord(id: number) {
    return this.get<EnhancedAttendanceRecord>(`/attendance-records/${id}/`);
  }

  async createAttendanceRecord(data: Partial<EnhancedAttendanceRecord>) {
    return this.post<EnhancedAttendanceRecord>('/attendance-records/', data);
  }

  async updateAttendanceRecord(id: number, data: Partial<EnhancedAttendanceRecord>) {
    return this.patch<EnhancedAttendanceRecord>(`/attendance-records/${id}/`, data);
  }

  async deleteAttendanceRecord(id: number) {
    return this.delete(`/attendance-records/${id}/`);
  }

  // Leave management
  async getLeaveTypes() {
    return this.get<EnhancedApiResponse<EnhancedLeaveType[]>>('/leave-types/');
  }

  async getLeaveBalances(employeeId?: number) {
    const params = employeeId ? { employee_id: employeeId } : {};
    return this.get<EnhancedApiResponse<EnhancedLeaveBalance[]>>('/leave-balances/', params);
  }

  async getLeaveApplications(filters?: LeaveFilters) {
    return this.get<EnhancedApiResponse<EnhancedLeaveApplication[]>>('/leave-applications/', filters);
  }

  async getLeaveApplication(id: number) {
    return this.get<EnhancedLeaveApplication>(`/leave-applications/${id}/`);
  }

  async createLeaveApplication(data: Partial<EnhancedLeaveApplication>) {
    return this.post<EnhancedLeaveApplication>('/leave-applications/', data);
  }

  async updateLeaveApplication(id: number, data: Partial<EnhancedLeaveApplication>) {
    return this.patch<EnhancedLeaveApplication>(`/leave-applications/${id}/`, data);
  }

  async deleteLeaveApplication(id: number) {
    return this.delete(`/leave-applications/${id}/`);
  }

  // Payroll management
  async getPayCycles(filters?: PaginationParams) {
    return this.get<EnhancedApiResponse<EnhancedPayCycle[]>>('/pay-cycles/', filters);
  }

  async getPayCycle(id: number) {
    return this.get<EnhancedPayCycle>(`/pay-cycles/${id}/`);
  }

  async createPayCycle(data: Partial<EnhancedPayCycle>) {
    return this.post<EnhancedPayCycle>('/pay-cycles/', data);
  }

  async updatePayCycle(id: number, data: Partial<EnhancedPayCycle>) {
    return this.patch<EnhancedPayCycle>(`/pay-cycles/${id}/`, data);
  }

  async deletePayCycle(id: number) {
    return this.delete(`/pay-cycles/${id}/`);
  }

  async getPayrollRecords(filters?: PayrollFilters) {
    return this.get<EnhancedApiResponse<EnhancedPayrollRecord[]>>('/payroll-records/', filters);
  }

  async getPayrollRecord(id: number) {
    return this.get<EnhancedPayrollRecord>(`/payroll-records/${id}/`);
  }

  async createPayrollRecord(data: Partial<EnhancedPayrollRecord>) {
    return this.post<EnhancedPayrollRecord>('/payroll-records/', data);
  }

  async updatePayrollRecord(id: number, data: Partial<EnhancedPayrollRecord>) {
    return this.patch<EnhancedPayrollRecord>(`/payroll-records/${id}/`, data);
  }

  async deletePayrollRecord(id: number) {
    return this.delete(`/payroll-records/${id}/`);
  }

  // System health and monitoring
  async getSystemHealth() {
    return this.get('/health/');
  }

  async getSystemMetrics() {
    return this.get('/metrics/');
  }

  // BioStar Integration
  async syncBiostarData() {
    return this.post('/biostar/sync/');
  }

  async getBiostarDevices() {
    return this.get('/biostar/devices/');
  }

  async getBiostarEvents(filters?: any) {
    return this.get('/biostar/events/', filters);
  }

  // Company management
  async getCompanyInfo() {
    return this.get('/company/info/');
  }

  async updateCompanyInfo(data: any) {
    return this.patch('/company/info/', data);
  }

  // Bulk operations
  async bulkCreateEmployees(employees: any[]) {
    return this.post('/employees/bulk-create/', { employees });
  }

  async bulkUpdateEmployees(updates: any[]) {
    return this.post('/employees/bulk-update/', { updates });
  }

  async bulkDeleteEmployees(ids: number[]) {
    return this.post('/employees/bulk-delete/', { ids });
  }

  // Advanced reporting
  async getEmployeeReport(filters?: any) {
    return this.get('/reports/employees/', filters);
  }

  async getAttendanceReport(filters?: any) {
    return this.get('/reports/attendance/', filters);
  }

  async getPayrollReport(filters?: any) {
    return this.get('/reports/payroll/', filters);
  }

  async getLeaveReport(filters?: any) {
    return this.get('/reports/leave/', filters);
  }

  // Export functionality
  async exportData(type: string, format: string, filters?: any) {
    return this.post('/export/', { type, format, filters });
  }

  // Notifications
  async getNotifications(filters?: any) {
    return this.get('/notifications/', filters);
  }

  async markNotificationRead(id: number) {
    return this.patch(`/notifications/${id}/`, { is_read: true });
  }

  async markAllNotificationsRead() {
    return this.post('/notifications/mark-all-read/');
  }
}

// Export singleton instance
export const enhancedApi = new EnhancedApiService();
export default enhancedApi;
