'use client';

import React, { useState } from 'react';
import { useDashboardStats, useSystemHealth } from '@/hooks/useEnhancedApi';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { LoadingStates } from '@/components/ui/LoadingStates';
import {
  UsersIcon,
  BuildingOfficeIcon,
  CalendarDaysIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  EyeIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';

interface DashboardCardProps {
  title: string;
  value: string | number;
  icon: React.ComponentType<{ className?: string }>;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'indigo';
  loading?: boolean;
  onClick?: () => void;
}

const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  value,
  icon: Icon,
  trend,
  color,
  loading = false,
  onClick
}) => {
  const colorClasses = {
    blue: 'bg-blue-500 text-white',
    green: 'bg-green-500 text-white',
    yellow: 'bg-yellow-500 text-white',
    red: 'bg-red-500 text-white',
    purple: 'bg-purple-500 text-white',
    indigo: 'bg-indigo-500 text-white'
  };

  const bgColorClasses = {
    blue: 'bg-blue-50 border-blue-200',
    green: 'bg-green-50 border-green-200',
    yellow: 'bg-yellow-50 border-yellow-200',
    red: 'bg-red-50 border-red-200',
    purple: 'bg-purple-50 border-purple-200',
    indigo: 'bg-indigo-50 border-indigo-200'
  };

  if (loading) {
    return (
      <Card className="p-6">
        <LoadingStates.Card />
      </Card>
    );
  }

  return (
    <Card 
      className={`p-6 ${bgColorClasses[color]} ${onClick ? 'cursor-pointer hover:shadow-lg transition-shadow' : ''}`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-3xl font-bold text-gray-900">{value}</p>
          {trend && (
            <div className="flex items-center mt-2">
              {trend.isPositive ? (
                <ArrowTrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <ArrowTrendingDownIcon className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm font-medium ${trend.isPositive ? 'text-green-600' : 'text-red-600'}`}>
                {Math.abs(trend.value)}%
              </span>
              <span className="text-sm text-gray-500 ml-1">vs last month</span>
            </div>
          )}
        </div>
        <div className={`p-3 rounded-full ${colorClasses[color]}`}>
          <Icon className="h-6 w-6" />
        </div>
      </div>
    </Card>
  );
};

interface SystemHealthIndicatorProps {
  health: any;
  loading: boolean;
  error: string | null;
}

const SystemHealthIndicator: React.FC<SystemHealthIndicatorProps> = ({ health, loading, error }) => {
  if (loading) {
    return (
      <Card className="p-4">
        <div className="flex items-center gap-3">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="text-sm text-gray-600">Checking system health...</span>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-4 bg-red-50 border-red-200">
        <div className="flex items-center gap-3">
          <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
          <div>
            <p className="text-sm font-medium text-red-800">System Health Check Failed</p>
            <p className="text-xs text-red-600">{error}</p>
          </div>
        </div>
      </Card>
    );
  }

  const isHealthy = health?.status === 'healthy' || health?.database === 'connected';

  return (
    <Card className={`p-4 ${isHealthy ? 'bg-green-50 border-green-200' : 'bg-yellow-50 border-yellow-200'}`}>
      <div className="flex items-center gap-3">
        {isHealthy ? (
          <CheckCircleIcon className="h-6 w-6 text-green-600" />
        ) : (
          <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600" />
        )}
        <div>
          <p className={`text-sm font-medium ${isHealthy ? 'text-green-800' : 'text-yellow-800'}`}>
            System Status: {isHealthy ? 'Healthy' : 'Warning'}
          </p>
          <p className={`text-xs ${isHealthy ? 'text-green-600' : 'text-yellow-600'}`}>
            Database: {health?.database || 'Unknown'} | 
            API: {health?.api || 'Unknown'}
          </p>
        </div>
      </div>
    </Card>
  );
};

interface QuickActionProps {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick: () => void;
  color: 'blue' | 'green' | 'purple' | 'indigo';
}

const QuickAction: React.FC<QuickActionProps> = ({ title, description, icon: Icon, onClick, color }) => {
  const colorClasses = {
    blue: 'text-blue-600 bg-blue-100 hover:bg-blue-200',
    green: 'text-green-600 bg-green-100 hover:bg-green-200',
    purple: 'text-purple-600 bg-purple-100 hover:bg-purple-200',
    indigo: 'text-indigo-600 bg-indigo-100 hover:bg-indigo-200'
  };

  return (
    <button
      onClick={onClick}
      className="w-full p-4 text-left border border-gray-200 rounded-lg hover:shadow-md transition-shadow"
    >
      <div className="flex items-start gap-3">
        <div className={`p-2 rounded-lg ${colorClasses[color]}`}>
          <Icon className="h-5 w-5" />
        </div>
        <div>
          <h3 className="font-medium text-gray-900">{title}</h3>
          <p className="text-sm text-gray-600 mt-1">{description}</p>
        </div>
      </div>
    </button>
  );
};

interface EnhancedDashboardProps {
  className?: string;
}

const EnhancedDashboard: React.FC<EnhancedDashboardProps> = ({ className = '' }) => {
  const [refreshKey, setRefreshKey] = useState(0);
  
  // Hooks for data fetching
  const {
    totalEmployees,
    totalDepartments,
    pendingLeaves,
    activeAttendance,
    monthlyPayroll,
    loading: statsLoading,
    error: statsError,
    refetch: refetchStats
  } = useDashboardStats();

  const {
    health,
    loading: healthLoading,
    error: healthError,
    refetch: refetchHealth
  } = useSystemHealth();

  // Event handlers
  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
    refetchStats();
    refetchHealth();
  };

  const quickActions = [
    {
      title: 'Add New Employee',
      description: 'Create a new employee profile',
      icon: UsersIcon,
      color: 'blue' as const,
      onClick: () => console.log('Navigate to add employee')
    },
    {
      title: 'Process Payroll',
      description: 'Generate payroll for current period',
      icon: CurrencyDollarIcon,
      color: 'green' as const,
      onClick: () => console.log('Navigate to payroll')
    },
    {
      title: 'View Reports',
      description: 'Access analytics and reports',
      icon: ChartBarIcon,
      color: 'purple' as const,
      onClick: () => console.log('Navigate to reports')
    },
    {
      title: 'System Settings',
      description: 'Configure system preferences',
      icon: Cog6ToothIcon,
      color: 'indigo' as const,
      onClick: () => console.log('Navigate to settings')
    }
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Welcome back! Here's what's happening with your workforce.</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleRefresh}>
            Refresh
          </Button>
          <Button onClick={() => console.log('Navigate to detailed view')}>
            <EyeIcon className="h-4 w-4 mr-2" />
            View Details
          </Button>
        </div>
      </div>

      {/* System Health */}
      <SystemHealthIndicator 
        health={health} 
        loading={healthLoading} 
        error={healthError} 
      />

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6">
        <DashboardCard
          title="Total Employees"
          value={totalEmployees}
          icon={UsersIcon}
          color="blue"
          loading={statsLoading}
          onClick={() => console.log('Navigate to employees')}
        />
        <DashboardCard
          title="Departments"
          value={totalDepartments}
          icon={BuildingOfficeIcon}
          color="green"
          loading={statsLoading}
          onClick={() => console.log('Navigate to departments')}
        />
        <DashboardCard
          title="Pending Leaves"
          value={pendingLeaves}
          icon={CalendarDaysIcon}
          color="yellow"
          loading={statsLoading}
          onClick={() => console.log('Navigate to leave requests')}
        />
        <DashboardCard
          title="Today's Attendance"
          value={activeAttendance}
          icon={ClockIcon}
          color="purple"
          loading={statsLoading}
          onClick={() => console.log('Navigate to attendance')}
        />
        <DashboardCard
          title="Payroll Records"
          value={monthlyPayroll}
          icon={CurrencyDollarIcon}
          color="indigo"
          loading={statsLoading}
          onClick={() => console.log('Navigate to payroll')}
        />
      </div>

      {/* Quick Actions */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action, index) => (
            <QuickAction
              key={index}
              title={action.title}
              description={action.description}
              icon={action.icon}
              color={action.color}
              onClick={action.onClick}
            />
          ))}
        </div>
      </Card>

      {/* Error Display */}
      {statsError && (
        <Card className="p-4 bg-red-50 border-red-200">
          <div className="flex items-center gap-3">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
            <div>
              <p className="text-sm font-medium text-red-800">Error Loading Dashboard Data</p>
              <p className="text-xs text-red-600">{statsError}</p>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleRefresh}
                className="mt-2"
              >
                Try Again
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default EnhancedDashboard;
