'use client';

import React, { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { useDepartments, useCrudOperations } from '@/hooks/useEnhancedApi';
import { enhancedApi } from '@/lib/enhancedApi';
import { 
  EnhancedEmployeeProfile, 
  EnhancedUser,
  EnhancedSalaryProfile,
  EnhancedBankProfile,
  EnhancedEmergencyContact 
} from '@/types/enhanced';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Modal } from '@/components/ui/Modal';
import { LoadingStates } from '@/components/ui/LoadingStates';
import { 
  UserIcon, 
  BuildingOfficeIcon,
  CurrencyDollarIcon,
  BanknotesIcon,
  PhoneIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface EmployeeFormData {
  // User Information
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  employee_id: string;
  role: string;
  
  // Employee Profile
  job_title: string;
  department_id: number | null;
  hire_date: string;
  employment_type: string;
  work_location: string;
  supervisor_id: number | null;
  
  // Personal Information
  date_of_birth: string;
  gender: string;
  marital_status: string;
  nationality: string;
  national_id: string;
  nssf_number: string;
  nhif_number: string;
  kra_pin: string;
  
  // Contact Information
  address: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  
  // Salary Information
  basic_salary: number;
  hourly_rate: number;
  currency: string;
  pay_frequency: string;
  allowances: number;
  overtime_rate: number;
  
  // Bank Information
  bank_name: string;
  bank_code: string;
  branch_name: string;
  account_number: string;
  account_name: string;
  account_type: string;
  
  // Emergency Contact
  emergency_contact_name: string;
  emergency_contact_phone: string;
  emergency_contact_relationship: string;
  emergency_contact_email: string;
  
  // Status
  status: string;
}

interface EmployeeFormProps {
  isOpen: boolean;
  onClose: () => void;
  employee?: EnhancedEmployeeProfile | null;
  onSuccess?: (employee: EnhancedEmployeeProfile) => void;
}

const EmployeeForm: React.FC<EmployeeFormProps> = ({
  isOpen,
  onClose,
  employee,
  onSuccess
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { departments, loading: departmentsLoading } = useDepartments();
  const { loading: operationLoading, executeOperation } = useCrudOperations<EnhancedEmployeeProfile>();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<EmployeeFormData>({
    defaultValues: {
      currency: 'KSH',
      country: 'Kenya',
      pay_frequency: 'monthly',
      employment_type: 'full_time',
      work_location: 'office',
      role: 'employee',
      status: 'active',
      account_type: 'savings'
    }
  });

  // Reset form when employee changes
  useEffect(() => {
    if (employee) {
      // Populate form with employee data
      reset({
        // User data
        first_name: employee.user?.first_name || '',
        last_name: employee.user?.last_name || '',
        email: employee.user?.email || '',
        phone_number: employee.user?.phone_number || '',
        employee_id: employee.user?.employee_id || '',
        role: employee.user?.role || 'employee',
        
        // Employee profile data
        job_title: employee.job_title,
        department_id: employee.department_id,
        hire_date: employee.hire_date,
        employment_type: employee.employment_type,
        work_location: employee.work_location,
        supervisor_id: employee.supervisor_id,
        
        // Personal information
        date_of_birth: employee.date_of_birth || '',
        gender: employee.gender || '',
        marital_status: employee.marital_status || '',
        nationality: employee.nationality || 'Kenyan',
        national_id: employee.national_id || '',
        nssf_number: employee.nssf_number || '',
        nhif_number: employee.nhif_number || '',
        kra_pin: employee.kra_pin || '',
        
        // Contact information
        address: employee.address || '',
        city: employee.city || '',
        state: employee.state || '',
        postal_code: employee.postal_code || '',
        country: employee.country,
        
        // Status
        status: employee.status
      });
    } else {
      reset();
    }
  }, [employee, reset]);

  const onSubmit = async (data: EmployeeFormData) => {
    setIsSubmitting(true);
    
    try {
      if (employee) {
        // Update existing employee
        const result = await executeOperation(
          () => enhancedApi.updateEmployeeProfile(employee.id, {
            job_title: data.job_title,
            department_id: data.department_id,
            hire_date: data.hire_date,
            employment_type: data.employment_type,
            work_location: data.work_location,
            supervisor_id: data.supervisor_id,
            date_of_birth: data.date_of_birth,
            gender: data.gender,
            marital_status: data.marital_status,
            nationality: data.nationality,
            national_id: data.national_id,
            nssf_number: data.nssf_number,
            nhif_number: data.nhif_number,
            kra_pin: data.kra_pin,
            address: data.address,
            city: data.city,
            state: data.state,
            postal_code: data.postal_code,
            country: data.country,
            status: data.status
          })
        );
        
        if (result) {
          onSuccess?.(result);
          onClose();
        }
      } else {
        // Create new employee - this would involve multiple API calls
        // 1. Create user
        // 2. Create employee profile
        // 3. Create salary profile
        // 4. Create bank profile
        // 5. Create emergency contact
        
        // For now, just create the employee profile
        const result = await executeOperation(
          () => enhancedApi.createEmployeeProfile({
            job_title: data.job_title,
            department_id: data.department_id,
            hire_date: data.hire_date,
            employment_type: data.employment_type,
            work_location: data.work_location,
            supervisor_id: data.supervisor_id,
            date_of_birth: data.date_of_birth,
            gender: data.gender,
            marital_status: data.marital_status,
            nationality: data.nationality,
            national_id: data.national_id,
            nssf_number: data.nssf_number,
            nhif_number: data.nhif_number,
            kra_pin: data.kra_pin,
            address: data.address,
            city: data.city,
            state: data.state,
            postal_code: data.postal_code,
            country: data.country,
            status: data.status
          })
        );
        
        if (result) {
          onSuccess?.(result);
          onClose();
        }
      }
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const steps = [
    { id: 1, name: 'Personal Info', icon: UserIcon },
    { id: 2, name: 'Job Details', icon: BuildingOfficeIcon },
    { id: 3, name: 'Salary & Bank', icon: CurrencyDollarIcon },
    { id: 4, name: 'Emergency Contact', icon: PhoneIcon }
  ];

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Personal Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  First Name *
                </label>
                <Input
                  {...register('first_name', { required: 'First name is required' })}
                  error={errors.first_name?.message}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Last Name *
                </label>
                <Input
                  {...register('last_name', { required: 'Last name is required' })}
                  error={errors.last_name?.message}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email *
                </label>
                <Input
                  type="email"
                  {...register('email', { 
                    required: 'Email is required',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Invalid email address'
                    }
                  })}
                  error={errors.email?.message}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <Input
                  {...register('phone_number')}
                  error={errors.phone_number?.message}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Employee ID *
                </label>
                <Input
                  {...register('employee_id', { required: 'Employee ID is required' })}
                  error={errors.employee_id?.message}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date of Birth
                </label>
                <Input
                  type="date"
                  {...register('date_of_birth')}
                  error={errors.date_of_birth?.message}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Gender
                </label>
                <select
                  {...register('gender')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                  <option value="prefer_not_to_say">Prefer not to say</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Marital Status
                </label>
                <select
                  {...register('marital_status')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Status</option>
                  <option value="single">Single</option>
                  <option value="married">Married</option>
                  <option value="divorced">Divorced</option>
                  <option value="widowed">Widowed</option>
                </select>
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Job Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Job Title *
                </label>
                <Input
                  {...register('job_title', { required: 'Job title is required' })}
                  error={errors.job_title?.message}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Department
                </label>
                <select
                  {...register('department_id')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={departmentsLoading}
                >
                  <option value="">Select Department</option>
                  {departments.map((dept) => (
                    <option key={dept.id} value={dept.id}>
                      {dept.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Hire Date *
                </label>
                <Input
                  type="date"
                  {...register('hire_date', { required: 'Hire date is required' })}
                  error={errors.hire_date?.message}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Employment Type *
                </label>
                <select
                  {...register('employment_type', { required: 'Employment type is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="full_time">Full Time</option>
                  <option value="part_time">Part Time</option>
                  <option value="contract">Contract</option>
                  <option value="intern">Intern</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Work Location *
                </label>
                <select
                  {...register('work_location', { required: 'Work location is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="office">Office</option>
                  <option value="remote">Remote</option>
                  <option value="hybrid">Hybrid</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Role *
                </label>
                <select
                  {...register('role', { required: 'Role is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="employee">Employee</option>
                  <option value="supervisor">Supervisor</option>
                  <option value="hr">HR</option>
                  <option value="accountant">Accountant</option>
                  <option value="admin">Admin</option>
                </select>
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Salary Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Basic Salary (KSH) *
                  </label>
                  <Input
                    type="number"
                    {...register('basic_salary', { 
                      required: 'Basic salary is required',
                      min: { value: 0, message: 'Salary must be positive' }
                    })}
                    error={errors.basic_salary?.message}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Hourly Rate (KSH)
                  </label>
                  <Input
                    type="number"
                    {...register('hourly_rate')}
                    error={errors.hourly_rate?.message}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Allowances (KSH)
                  </label>
                  <Input
                    type="number"
                    {...register('allowances')}
                    error={errors.allowances?.message}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Overtime Rate (KSH)
                  </label>
                  <Input
                    type="number"
                    {...register('overtime_rate')}
                    error={errors.overtime_rate?.message}
                  />
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900">Bank Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Bank Name *
                  </label>
                  <Input
                    {...register('bank_name', { required: 'Bank name is required' })}
                    error={errors.bank_name?.message}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Account Number *
                  </label>
                  <Input
                    {...register('account_number', { required: 'Account number is required' })}
                    error={errors.account_number?.message}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Account Name *
                  </label>
                  <Input
                    {...register('account_name', { required: 'Account name is required' })}
                    error={errors.account_name?.message}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Account Type
                  </label>
                  <select
                    {...register('account_type')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="savings">Savings</option>
                    <option value="current">Current</option>
                    <option value="fixed_deposit">Fixed Deposit</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Emergency Contact</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Contact Name *
                </label>
                <Input
                  {...register('emergency_contact_name', { required: 'Emergency contact name is required' })}
                  error={errors.emergency_contact_name?.message}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Contact Phone *
                </label>
                <Input
                  {...register('emergency_contact_phone', { required: 'Emergency contact phone is required' })}
                  error={errors.emergency_contact_phone?.message}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Relationship *
                </label>
                <Input
                  {...register('emergency_contact_relationship', { required: 'Relationship is required' })}
                  error={errors.emergency_contact_relationship?.message}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Contact Email
                </label>
                <Input
                  type="email"
                  {...register('emergency_contact_email')}
                  error={errors.emergency_contact_email?.message}
                />
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={employee ? 'Edit Employee' : 'Add New Employee'}
    >
      <div className="max-w-4xl mx-auto">
        {/* Step Indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                    currentStep >= step.id
                      ? 'bg-blue-600 border-blue-600 text-white'
                      : 'border-gray-300 text-gray-500'
                  }`}
                >
                  <step.icon className="w-5 h-5" />
                </div>
                <div className="ml-3">
                  <p className={`text-sm font-medium ${
                    currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'
                  }`}>
                    {step.name}
                  </p>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 ml-4 ${
                    currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Form Content */}
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="min-h-[400px]">
            {renderStepContent()}
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-6 border-t border-gray-200">
            <Button
              type="button"
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 1}
            >
              Previous
            </Button>
            
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
              >
                Cancel
              </Button>
              
              {currentStep < steps.length ? (
                <Button
                  type="button"
                  onClick={nextStep}
                >
                  Next
                </Button>
              ) : (
                <Button
                  type="submit"
                  loading={isSubmitting || operationLoading}
                >
                  {employee ? 'Update Employee' : 'Create Employee'}
                </Button>
              )}
            </div>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default EmployeeForm;
