// Enhanced Types for Backend Integration
// This file contains comprehensive types that match the backend database schema

// Base Types
export interface BaseModel {
  id: number;
  created_at: string;
  updated_at: string;
  created_by?: number;
  updated_by?: number;
}

export interface SoftDeleteModel extends BaseModel {
  is_deleted: boolean;
  deleted_at?: string;
  deleted_by?: number;
}

// Enhanced User Types
export interface EnhancedUser extends SoftDeleteModel {
  email: string;
  first_name: string;
  last_name: string;
  employee_id: string;
  phone_number?: string;
  profile_picture?: string;
  role: 'admin' | 'hr' | 'supervisor' | 'accountant' | 'employee';
  is_active: boolean;
  date_joined: string;
  last_login?: string;
}

// Department Types
export interface EnhancedDepartment extends BaseModel {
  name: string;
  description?: string;
  supervisor_id?: number;
  supervisor?: EnhancedUser;
  parent_department_id?: number;
  parent_department?: EnhancedDepartment;
  budget?: number;
  location?: string;
  is_active: boolean;
}

// Employee Profile Types
export interface EnhancedEmployeeProfile extends BaseModel {
  user_id: number;
  user?: EnhancedUser;
  department_id?: number;
  department?: EnhancedDepartment;
  job_title: string;
  hire_date: string;
  employment_type: 'full_time' | 'part_time' | 'contract' | 'intern';
  work_location: 'office' | 'remote' | 'hybrid';
  supervisor_id?: number;
  supervisor?: EnhancedUser;
  
  // Government IDs (Kenya specific)
  nssf_number?: string;
  nhif_number?: string;
  kra_pin?: string;
  national_id?: string;
  
  // Personal Information
  date_of_birth?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  marital_status?: 'single' | 'married' | 'divorced' | 'widowed';
  nationality?: string;
  
  // Contact Information
  address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country: string;
  
  // Employment Status
  status: 'active' | 'inactive' | 'terminated' | 'on_leave';
  termination_date?: string;
  termination_reason?: string;
}

// Salary Profile Types
export interface EnhancedSalaryProfile extends BaseModel {
  employee_id: number;
  employee?: EnhancedUser;
  basic_salary: number;
  hourly_rate?: number;
  currency: string;
  pay_frequency: 'monthly' | 'bi_weekly' | 'weekly';
  allowances: number;
  overtime_rate?: number;
  commission_rate?: number;
  tax_exemption_amount: number;
  tax_relief_amount: number;
  effective_from: string;
  effective_to?: string;
  is_active: boolean;
}

// Bank Profile Types
export interface EnhancedBankProfile extends BaseModel {
  employee_id: number;
  employee?: EnhancedUser;
  bank_name: string;
  bank_code?: string;
  branch_name?: string;
  branch_code?: string;
  account_number: string;
  account_name: string;
  account_type?: 'savings' | 'current' | 'fixed_deposit';
  swift_code?: string;
  is_primary: boolean;
  is_active: boolean;
}

// Emergency Contact Types
export interface EnhancedEmergencyContact extends BaseModel {
  employee_id: number;
  employee?: EnhancedUser;
  contact_name: string;
  relationship: string;
  phone_number: string;
  email?: string;
  address?: string;
  priority_order: number;
  is_active: boolean;
}

// Attendance Types
export interface EnhancedAttendanceRecord extends BaseModel {
  employee_id: number;
  employee?: EnhancedUser;
  date: string;
  check_in?: string;
  check_out?: string;
  break_start?: string;
  break_end?: string;
  total_hours?: number;
  regular_hours?: number;
  overtime_hours?: number;
  break_time?: number;
  status: 'present' | 'absent' | 'late' | 'early_out' | 'half_day';
  notes?: string;
  biostar_synced: boolean;
  biostar_event_ids?: string[];
}

// BioStar Integration Types
export interface EnhancedBiostarEvent extends BaseModel {
  biostar_event_id: string;
  employee_id?: number;
  employee?: EnhancedUser;
  device_id?: string;
  device_name?: string;
  event_type: 'ENTRY' | 'EXIT' | 'DENIED';
  event_datetime: string;
  location?: string;
  processed: boolean;
  attendance_record_id?: number;
  attendance_record?: EnhancedAttendanceRecord;
}

export interface EnhancedBiostarDevice extends BaseModel {
  biostar_device_id: string;
  name: string;
  ip_address?: string;
  port?: number;
  location?: string;
  device_type?: string;
  status: 'online' | 'offline' | 'maintenance';
  last_seen?: string;
}

// Leave Management Types
export interface EnhancedLeaveType extends BaseModel {
  name: string;
  description?: string;
  max_days_per_year?: number;
  carry_forward_allowed: boolean;
  max_carry_forward_days?: number;
  requires_approval: boolean;
  is_paid: boolean;
  is_active: boolean;
}

export interface EnhancedLeaveBalance extends BaseModel {
  employee_id: number;
  employee?: EnhancedUser;
  leave_type_id: number;
  leave_type?: EnhancedLeaveType;
  year: number;
  allocated_days: number;
  used_days: number;
  pending_days: number;
  carried_forward_days: number;
  remaining_days: number; // Computed field
}

export interface EnhancedLeaveApplication extends BaseModel {
  employee_id: number;
  employee?: EnhancedUser;
  leave_type_id: number;
  leave_type?: EnhancedLeaveType;
  start_date: string;
  end_date: string;
  days_requested: number;
  reason?: string;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  approved_by?: number;
  approver?: EnhancedUser;
  approved_at?: string;
  reviewer_comments?: string;
  attachment_url?: string;
  applied_date: string;
}

// Company Holiday Types
export interface EnhancedCompanyHoliday extends BaseModel {
  name: string;
  date: string;
  description?: string;
  is_recurring: boolean;
  applies_to_all: boolean;
  department_ids?: number[];
  departments?: EnhancedDepartment[];
  created_by?: number;
  creator?: EnhancedUser;
}

// Payroll Types
export interface EnhancedPayCycle extends BaseModel {
  pay_period: string;
  start_date: string;
  end_date: string;
  pay_date: string;
  status: 'draft' | 'processing' | 'completed' | 'cancelled';
  total_employees: number;
  total_gross_amount: number;
  total_net_amount: number;
  total_deductions: number;
  notes?: string;
  processed_by?: number;
  processor?: EnhancedUser;
  processed_at?: string;
}

export interface EnhancedPayrollRecord extends BaseModel {
  employee_id: number;
  employee?: EnhancedUser;
  pay_cycle_id: number;
  pay_cycle?: EnhancedPayCycle;
  basic_salary: number;
  allowances: number;
  overtime_amount: number;
  bonuses: number;
  gross_salary: number;
  tax_deduction: number;
  nssf_deduction: number;
  nhif_deduction: number;
  housing_levy: number;
  loan_deductions: number;
  other_deductions: number;
  total_deductions: number;
  net_salary: number;
  currency: string;
  exchange_rate: number;
  payment_method: string;
  payment_reference?: string;
  payment_status: 'draft' | 'pending_approval' | 'approved' | 'processing' | 'paid' | 'failed' | 'cancelled' | 'on_hold';
  payment_date?: string;
  working_days?: number;
  days_worked?: number;
  overtime_hours: number;
  leave_days_deducted: number;
}

// API Response Types
export interface EnhancedApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Record<string, string[]>;
  count?: number;
  next?: string;
  previous?: string;
  results?: T[];
}

// Pagination Types
export interface PaginationParams {
  page?: number;
  page_size?: number;
  ordering?: string;
  search?: string;
}

// Filter Types
export interface EmployeeFilters extends PaginationParams {
  department?: number;
  status?: string;
  employment_type?: string;
  role?: string;
  supervisor?: number;
  hire_date_from?: string;
  hire_date_to?: string;
}

export interface AttendanceFilters extends PaginationParams {
  employee?: number;
  date_from?: string;
  date_to?: string;
  status?: string;
}

export interface LeaveFilters extends PaginationParams {
  employee?: number;
  leave_type?: number;
  status?: string;
  start_date_from?: string;
  start_date_to?: string;
}

export interface PayrollFilters extends PaginationParams {
  pay_cycle?: number;
  employee?: number;
  payment_status?: string;
}
