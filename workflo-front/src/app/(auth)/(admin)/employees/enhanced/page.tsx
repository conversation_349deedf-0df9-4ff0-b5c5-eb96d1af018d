'use client';

import React, { useState } from 'react';
import EmployeeManagement from '@/components/enhanced/EmployeeManagement';
import EmployeeForm from '@/components/enhanced/EmployeeForm';
import EnhancedDashboard from '@/components/enhanced/EnhancedDashboard';
import { EnhancedEmployeeProfile } from '@/types/enhanced';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { 
  UserPlusIcon, 
  ChartBarIcon,
  DocumentArrowDownIcon,
  Cog6ToothIcon,
  ExclamationTriangleIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';

const EnhancedEmployeesPage = () => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<EnhancedEmployeeProfile | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [currentView, setCurrentView] = useState<'dashboard' | 'employees'>('dashboard');

  const handleCreateEmployee = () => {
    setIsCreateModalOpen(true);
  };

  const handleEditEmployee = (employee: EnhancedEmployeeProfile) => {
    setSelectedEmployee(employee);
    setIsEditModalOpen(true);
  };

  const handleEmployeeSuccess = (employee: EnhancedEmployeeProfile) => {
    // Trigger refresh of employee list
    setRefreshKey(prev => prev + 1);
    setIsCreateModalOpen(false);
    setIsEditModalOpen(false);
    setSelectedEmployee(null);
  };

  const handleExportEmployees = () => {
    // TODO: Implement export functionality
    console.log('Export employees');
  };

  const handleGenerateReports = () => {
    // TODO: Navigate to reports page
    console.log('Generate reports');
  };

  const handleEmployeeSettings = () => {
    // TODO: Navigate to employee settings
    console.log('Employee settings');
  };

  const handleBackToRegular = () => {
    window.location.href = '/employees';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={handleBackToRegular}
                className="flex items-center gap-2"
              >
                <ArrowLeftIcon className="h-4 w-4" />
                Back to Regular View
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Enhanced Employee Management</h1>
                <p className="text-gray-600">Advanced employee management with backend integration</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant={currentView === 'dashboard' ? 'primary' : 'outline'}
                onClick={() => setCurrentView('dashboard')}
              >
                <ChartBarIcon className="h-4 w-4 mr-2" />
                Dashboard
              </Button>
              <Button
                variant={currentView === 'employees' ? 'primary' : 'outline'}
                onClick={() => setCurrentView('employees')}
              >
                <UserPlusIcon className="h-4 w-4 mr-2" />
                Employees
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Feature Notice */}
        <Card className="mb-6 bg-blue-50 border-blue-200">
          <div className="p-4">
            <div className="flex items-start gap-3">
              <ExclamationTriangleIcon className="h-6 w-6 text-blue-600 mt-0.5" />
              <div>
                <h3 className="text-sm font-medium text-blue-800">Enhanced Features</h3>
                <p className="text-sm text-blue-700 mt-1">
                  This page demonstrates enhanced employee management with full backend integration, 
                  advanced filtering, real-time updates, and comprehensive CRUD operations.
                </p>
                <div className="mt-3 flex flex-wrap gap-2">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Backend Integration
                  </span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Real-time Updates
                  </span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    Advanced Filtering
                  </span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                    Multi-step Forms
                  </span>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={handleCreateEmployee}>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <UserPlusIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Add Employee</h3>
                <p className="text-sm text-gray-600">Create new employee profile</p>
              </div>
            </div>
          </Card>

          <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={handleGenerateReports}>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <ChartBarIcon className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Generate Reports</h3>
                <p className="text-sm text-gray-600">View analytics and insights</p>
              </div>
            </div>
          </Card>

          <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={handleExportEmployees}>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <DocumentArrowDownIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Export Data</h3>
                <p className="text-sm text-gray-600">Download employee data</p>
              </div>
            </div>
          </Card>

          <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={handleEmployeeSettings}>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Cog6ToothIcon className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Settings</h3>
                <p className="text-sm text-gray-600">Configure employee settings</p>
              </div>
            </div>
          </Card>
        </div>

        {/* Main Content Area */}
        {currentView === 'dashboard' ? (
          <EnhancedDashboard key={refreshKey} />
        ) : (
          <EmployeeManagement 
            key={refreshKey}
            className="space-y-6"
          />
        )}

        {/* Modals */}
        <EmployeeForm
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          employee={null}
          onSuccess={handleEmployeeSuccess}
        />

        <EmployeeForm
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setSelectedEmployee(null);
          }}
          employee={selectedEmployee}
          onSuccess={handleEmployeeSuccess}
        />
      </div>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-sm text-gray-600">
              Enhanced Employee Management System - Powered by WorkFlo Backend API
            </div>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <Button variant="ghost" size="sm">
                API Documentation
              </Button>
              <Button variant="ghost" size="sm">
                System Health
              </Button>
              <Button variant="ghost" size="sm">
                Support
              </Button>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default EnhancedEmployeesPage;
