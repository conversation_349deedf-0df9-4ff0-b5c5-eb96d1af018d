# WorkFlo Frontend-Backend Integration Guide

This document provides comprehensive instructions for setting up and configuring the integration between the WorkFlo frontend (Next.js) and backend (Django REST API).

## Overview

The WorkFlo system consists of:
- **Frontend**: Next.js application (`workflo-front`)
- **Backend**: Django REST API (`workflo_back`)
- **Database**: PostgreSQL (production) / SQLite (development)
- **Authentication**: JWT-based authentication
- **Deployment**: Vercel (frontend) / Render (backend)

## Configuration

### Backend Configuration (workflo_back)

#### 1. Environment Variables

Create or update `.env` file in `workflo_back/`:

```env
# Django Settings
SECRET_KEY=django-insecure-)jjp%dfddm@57tc^w%vk0r(5gf=m0v&(ep%j)#s0_ir#dgmj=)
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,testserver,*.onrender.com,workflo-back.onrender.com

# Database (SQLite for local development)
USE_SQLITE=True

# CORS Settings - Include frontend URLs
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,https://workflo-front.vercel.app,https://workflo-front-h7mqp514a-victor-mbuguas-projects.vercel.app
CORS_ALLOW_ALL_ORIGINS=True

# BioStar Integration
BIOSTAR_BASE_URL=https://ns.biostar2.com
BIOSTAR_USERNAME=dev
BIOSTAR_PASSWORD=d3vt3@ms
```

#### 2. Production Environment (Render)

For production deployment on Render, set these environment variables:

```env
DEBUG=False
ALLOWED_HOSTS=*.onrender.com,workflo-back.onrender.com
DATABASE_URL=postgresql://user:password@host:port/database
CORS_ALLOWED_ORIGINS=https://workflo-front-h7mqp514a-victor-mbuguas-projects.vercel.app
```

### Frontend Configuration (workflo-front)

#### 1. Local Development Environment

Update `.env.local` file:

```env
# Backend API URL
NEXT_PUBLIC_API_URL=http://localhost:8000/api

# Use real backend (not mock)
NEXT_PUBLIC_USE_MOCK_API=false

# BioStar Configuration
NEXT_PUBLIC_BIOSTAR_API_URL=https://bs2api.biostar2.com
NEXT_PUBLIC_BIOSTAR_USERNAME=dev
NEXT_PUBLIC_BIOSTAR_PASSWORD=d3vt3@ms
```

#### 2. Production Environment

Update `.env.production` file:

```env
# Backend API URL - Production
NEXT_PUBLIC_API_URL=https://workflo-back.onrender.com/api

# Use real backend
NEXT_PUBLIC_USE_MOCK_API=false

# BioStar Configuration
NEXT_PUBLIC_BIOSTAR_API_URL=https://ns.biostar2.com
NEXT_PUBLIC_BIOSTAR_USERNAME=dev
NEXT_PUBLIC_BIOSTAR_PASSWORD=d3vt3@ms
```

## Authentication Integration

### JWT Token Flow

1. **Login**: Frontend sends credentials to `/api/auth/login/`
2. **Token Response**: Backend returns access and refresh tokens
3. **Token Storage**: Frontend stores tokens in localStorage
4. **API Requests**: Frontend includes `Authorization: Bearer <token>` header
5. **Token Refresh**: Automatic refresh when access token expires

### Authentication Endpoints

- **Login**: `POST /api/auth/login/`
- **Refresh**: `POST /api/auth/refresh/`
- **Current User**: `GET /api/users/me/`

### Default Test Credentials

```javascript
const testCredentials = {
  admin: { email: '<EMAIL>', password: 'admin123' },
  supervisor: { email: '<EMAIL>', password: 'password123' },
  hr: { email: '<EMAIL>', password: 'password123' },
  accountant: { email: '<EMAIL>', password: 'password123' },
  employee: { email: '<EMAIL>', password: 'password123' },
};
```

## API Endpoints

### Core Endpoints

- **Authentication**: `/api/auth/`
- **Users**: `/api/users/`
- **Employees**: `/api/employee-profiles/`
- **Departments**: `/api/departments/`
- **Payroll**: `/api/payroll-records/`
- **Leave**: `/api/leave-applications/`
- **Attendance**: `/api/attendance-records/`

### Health & Monitoring

- **Health Check**: `/api/health/`
- **System Metrics**: `/api/metrics/`
- **API Documentation**: `/api/docs/`

## Testing Integration

### 1. Backend Health Test

```bash
# Test backend health
curl http://localhost:8000/api/health/
```

### 2. Authentication Test

```bash
# Test login
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "admin123"}'
```

### 3. Frontend Integration Tests

```bash
# Run integration tests
cd workflo-front
npm run test:integration

# Verbose output
npm run test:integration:verbose

# Test authentication only
npm run test:integration:auth

# Test all endpoints
npm run test:integration:endpoints
```

## Deployment

### Backend Deployment (Render)

1. **Repository**: Connect to GitHub repository
2. **Build Command**: `pip install -r requirements.txt`
3. **Start Command**: `python server.py`
4. **Environment Variables**: Set production environment variables
5. **Database**: Configure PostgreSQL database

### Frontend Deployment (Vercel)

1. **Repository**: Connect to GitHub repository
2. **Framework**: Next.js
3. **Build Command**: `npm run build`
4. **Environment Variables**: Set production environment variables
5. **Domain**: Configure custom domain if needed

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Ensure frontend URL is in `CORS_ALLOWED_ORIGINS`
   - Check `CORS_ALLOW_ALL_ORIGINS=True` for development

2. **Authentication Failures**
   - Verify backend is running on correct port
   - Check API endpoint URLs match backend routes
   - Ensure JWT tokens are properly stored and sent

3. **API Connection Issues**
   - Verify `NEXT_PUBLIC_API_URL` points to correct backend
   - Check `NEXT_PUBLIC_USE_MOCK_API=false`
   - Test backend health endpoint

### Debug Commands

```bash
# Backend
cd workflo_back
python manage.py runserver 8000

# Frontend
cd workflo-front
npm run dev

# Test integration
npm run test:integration:verbose
```

## Security Considerations

1. **Environment Variables**: Never commit sensitive data to version control
2. **CORS**: Restrict origins in production
3. **JWT Tokens**: Implement proper token expiration and refresh
4. **HTTPS**: Use HTTPS in production
5. **Rate Limiting**: Configure appropriate rate limits

## Monitoring

### Health Checks

- Backend: `https://workflo-back.onrender.com/api/health/`
- Frontend: `https://workflo-front-h7mqp514a-victor-mbuguas-projects.vercel.app/`

### Logs

- Backend: Check Render logs
- Frontend: Check Vercel logs
- Integration: Run test scripts for diagnostics

## Support

For issues with integration:

1. Check this documentation
2. Run integration tests
3. Review logs
4. Check environment configuration
5. Verify network connectivity
