# WorkFlo Backend - Render Web Service Deployment Guide

## 🚀 Quick Deployment Steps

### 1. Create Web Service on Render

1. Go to [Render Dashboard](https://dashboard.render.com/)
2. Click **"New +"** → **"Web Service"**
3. Connect your GitHub repository: `blackswanalpha/workflo_back`
4. Configure the service:

### 2. Service Configuration

**Basic Settings:**
- **Name**: `workflo-backend`
- **Runtime**: `Python 3`
- **Build Command**: `./build.sh`
- **Start Command**: `python server.py`

**Advanced Settings:**
- **Auto-Deploy**: `Yes`
- **Branch**: `main`

### 3. Environment Variables

Set these environment variables in Render dashboard:

```bash
# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=False
ALLOWED_HOSTS=workflo-backend.onrender.com,localhost,127.0.0.1

# Database (PostgreSQL)
DATABASE_URL=postgresql://user:password@host:port/database

# Redis (Optional - for caching)
REDIS_URL=redis://user:password@host:port

# CORS Settings
CORS_ALLOWED_ORIGINS=https://workflo-front.vercel.app,http://localhost:3000

# Security
SECURE_SSL_REDIRECT=True

# Email (Optional)
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# BioStar Integration (Disabled for production)
USE_BIOSTAR_INTEGRATION=False
BIOSTAR_MOCK_MODE=True
```

### 4. Database Setup

**Option A: Render PostgreSQL (Recommended)**
1. Create a PostgreSQL database on Render
2. Copy the `DATABASE_URL` from the database info
3. Add it to your web service environment variables

**Option B: External PostgreSQL**
1. Use any PostgreSQL provider (AWS RDS, Google Cloud SQL, etc.)
2. Set the `DATABASE_URL` environment variable

### 5. Redis Setup (Optional)

**Option A: Render Redis**
1. Create a Redis instance on Render
2. Copy the `REDIS_URL`
3. Add it to environment variables

**Option B: External Redis**
1. Use Redis Cloud, AWS ElastiCache, etc.
2. Set the `REDIS_URL` environment variable

## 🔧 Advanced Configuration

### Health Check
- **Health Check Path**: `/api/health/`
- **Health Check Grace Period**: 300 seconds

### Scaling
- **Instance Type**: Starter (can upgrade later)
- **Auto-scaling**: Available on paid plans

### Custom Domain
1. Go to service settings
2. Add your custom domain
3. Configure DNS records

## 📊 Post-Deployment

### 1. Verify Deployment
- Health Check: `https://your-app.onrender.com/api/health/`
- API Docs: `https://your-app.onrender.com/api/docs/`
- Admin Panel: `https://your-app.onrender.com/admin/`

### 2. Default Credentials
- **Admin**: `<EMAIL>` / `admin123`
- **Test Users**: Various roles created automatically

### 3. API Endpoints
- **Authentication**: `/api/auth/`
- **Employees**: `/api/employees/`
- **Payroll**: `/api/payroll/`
- **Leave**: `/api/leave/`
- **Attendance**: `/api/attendance/`

## 🛠️ Troubleshooting

### Common Issues

**Build Failures:**
- Check build logs in Render dashboard
- Ensure all dependencies are in requirements.txt
- Verify Python version compatibility

**Database Connection:**
- Verify DATABASE_URL format
- Check database server accessibility
- Ensure database exists and user has permissions

**Static Files:**
- Build script runs `collectstatic`
- WhiteNoise serves static files in production
- Check STATIC_ROOT and STATIC_URL settings

**Logging Issues:**
- Logs are written to console (visible in Render logs)
- File logging falls back gracefully if directory not writable

### Performance Optimization

**Database:**
- Use connection pooling (already configured)
- Add database indexes for large datasets
- Consider read replicas for high traffic

**Caching:**
- Enable Redis for better performance
- Configure cache timeouts appropriately
- Use database query optimization

**Monitoring:**
- Use Render's built-in monitoring
- Set up external monitoring (Sentry, etc.)
- Monitor resource usage and scale accordingly

## 🔐 Security Considerations

### Production Settings
- `DEBUG=False` (already configured)
- Strong `SECRET_KEY` (auto-generated by Render)
- HTTPS enforcement (configured)
- Secure headers (configured)

### Database Security
- Use strong database passwords
- Restrict database access to application only
- Regular security updates

### API Security
- JWT authentication (configured)
- Rate limiting (configured)
- CORS properly configured
- Input validation (implemented)

## 📈 Scaling and Maintenance

### Horizontal Scaling
- Upgrade to paid plan for auto-scaling
- Use load balancers for multiple instances
- Consider microservices architecture

### Database Scaling
- Use read replicas for read-heavy workloads
- Implement database sharding for large datasets
- Regular database maintenance and optimization

### Monitoring and Alerts
- Set up health check alerts
- Monitor response times and error rates
- Track resource usage and costs

This deployment guide provides everything needed to successfully deploy WorkFlo Backend as a web service on Render.
