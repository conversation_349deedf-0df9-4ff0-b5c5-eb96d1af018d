# WorkFlo Backend - Real Data Implementation

## Overview

This document outlines the transition from mock data to real data implementation in the WorkFlo backend system. The system has been analyzed and enhanced to work with actual production data while maintaining BioStar integration capabilities.

## Current Implementation Status

### ✅ **Completed Database Schema (58/58 tables)**

The WorkFlo backend has a complete implementation with all 58 database tables from the original schema:

#### **1. Authentication & User Management (3 tables)**
- `users` - Custom User model with role-based access
- `user_sessions` - JWT session management  
- `password_reset_tokens` - Password reset functionality

#### **2. Employee Management (4 tables)**
- `employee_profiles` - Complete employee information
- `salary_profiles` - Salary and compensation details
- `bank_profiles` - Banking information for payroll
- `emergency_contacts` - Emergency contact information

#### **3. Attendance & Time Tracking (9 tables)**
- `attendance_records` - Daily attendance tracking
- `biostar_events` - BioStar biometric events
- `biostar_devices` - BioStar device management
- `overtime_types` - Overtime policy definitions
- `overtime_requests` - Overtime request workflow
- `overtime_records` - Approved overtime records
- `overtime_approval_workflows` - Approval process tracking
- `overtime_budgets` - Department overtime budgets
- `overtime_calculations` - Overtime payment calculations

#### **4. Leave Management (4 tables)**
- `leave_types` - Leave policy definitions
- `leave_balances` - Employee leave balances
- `leave_applications` - Leave request workflow
- `company_holidays` - Company holiday calendar

#### **5. Payroll System (5 tables)**
- `pay_cycles` - Payroll period definitions
- `payroll_records` - Employee payroll calculations
- `payroll_adjustments` - Payroll corrections
- `salary_adjustments` - Salary change tracking
- `employee_benefits` - Employee benefit management

## Real Data Implementation Strategy

### **1. Sample Data Population**

The system includes a comprehensive sample data population command:

```bash
python manage.py populate_sample_data
```

This creates:
- **9 sample employees** with different roles (Admin, HR, Supervisor, Accountant, Employees)
- **4 departments** (IT, HR, Finance, Operations)
- **Complete salary profiles** with Kenyan tax compliance
- **Leave types and balances**
- **Overtime policies**
- **Company holidays**
- **Pay cycles**

### **2. Employee Data Structure**

#### **Sample Employees Created:**
1. **Admin User** - System Administrator
2. **HR Manager** - Sarah Johnson (EMP0002)
3. **Supervisor** - Michael Kimani (EMP0003)
4. **Accountant** - Grace Wanjiku (EMP0004)
5. **Software Developer** - James Mwangi (EMP0005)
6. **HR Specialist** - Mary Njeri (EMP0006)
7. **Finance Assistant** - David Omondi (EMP0007)
8. **Operations Manager** - Alice Mutua (EMP0008)
9. **Junior Developer** - Peter Ochieng (EMP0009)

#### **Salary Ranges (KSH):**
- Admin: 150,000 + 25,000 allowances
- HR Manager: 120,000 + 20,000 allowances
- Supervisor: 110,000 + 18,000 allowances
- Accountant: 100,000 + 15,000 allowances
- Developers: 65,000-90,000 + allowances

### **3. Kenyan Payroll Compliance**

#### **Tax Brackets Implementation:**
```python
TAX_BRACKETS = [
    (24000, 0.10),    # 10% on first 24,000
    (8333, 0.25),     # 25% on next 8,333
    (467667, 0.30),   # 30% on next 467,667
    (300000, 0.325),  # 32.5% on next 300,000
    (float('inf'), 0.35)  # 35% on remainder
]
```

#### **Statutory Deductions:**
- **NSSF**: 6% with tiered structure
- **NHIF/SHA**: 2.75% of gross salary
- **Housing Levy**: 1.5% of gross salary
- **PAYE**: Progressive tax rates

### **4. Attendance Tracking**

#### **Real Attendance Data:**
- Daily check-in/check-out records
- Automatic overtime detection
- Late arrival tracking
- Absence management
- Break time calculations

#### **BioStar Integration:**
- Real-time event synchronization
- Device status monitoring
- Employee biometric mapping
- Automatic attendance record creation

### **5. Dynamic Salary Calculation**

#### **Calculation Components:**
```python
# Base salary calculation
base_salary = basic_salary * attendance_rate

# Overtime calculation
overtime_amount = overtime_hours * hourly_rate * multiplier

# Deductions
late_penalty = daily_rate * penalty_rate * late_days
absence_deduction = daily_rate * absent_days

# Net salary
net_salary = gross_salary - total_deductions
```

#### **Overtime Multipliers:**
- **Regular Overtime**: 1.5x
- **Weekend Overtime**: 2.0x
- **Holiday Overtime**: 2.5x

## API Endpoints for Real Data

### **1. Employee Management**
```http
GET /api/users/                    # List all employees
GET /api/employee-profiles/        # Employee profiles
GET /api/salary-profiles/          # Salary information
POST /api/users/                   # Create new employee
PUT /api/users/{id}/               # Update employee
```

### **2. Attendance Management**
```http
GET /api/attendance-records/       # Daily attendance
POST /api/attendance-records/sync_biostar/  # Sync BioStar data
GET /api/attendance-records/attendance_summary/  # Attendance stats
POST /api/attendance-records/calculate_salary/   # Dynamic salary calc
```

### **3. Payroll Processing**
```http
GET /api/payroll-records/          # Payroll history
POST /api/payroll-records/         # Process payroll
GET /api/pay-cycles/               # Pay periods
POST /api/payroll-records/bulk_process/  # Bulk payroll
```

### **4. Leave Management**
```http
GET /api/leave-applications/       # Leave requests
POST /api/leave-applications/      # Submit leave request
GET /api/leave-balances/           # Employee leave balances
PUT /api/leave-applications/{id}/approve/  # Approve leave
```

### **5. Overtime Management**
```http
GET /api/overtime-requests/        # Overtime requests
POST /api/overtime-requests/       # Submit overtime request
GET /api/overtime-calculations/    # Overtime calculations
POST /api/overtime-requests/{id}/approve/  # Approve overtime
```

## Data Migration and Setup

### **1. Initial Setup**
```bash
# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Populate sample data
python manage.py populate_sample_data

# Create overtime types
python manage.py shell
>>> from core.models.overtime import OvertimeType
>>> OvertimeType.objects.create(name="Standard", rate_multiplier=1.5, is_active=True)
```

### **2. BioStar Configuration**
```bash
# Set environment variables
export BIOSTAR_BASE_URL=https://ns.biostar2.com
export BIOSTAR_USERNAME=your_username
export BIOSTAR_PASSWORD=your_password
export BIOSTAR_MOCK_MODE=False

# Test BioStar connection
python simple_biostar_test.py
```

### **3. Cron Jobs Setup**
```bash
# Add cron jobs for automation
python manage.py crontab add

# Verify cron jobs
python manage.py crontab show
```

## Real Data Workflows

### **1. Employee Onboarding**
1. Create user account with role assignment
2. Create employee profile with department
3. Set up salary profile with effective dates
4. Configure leave balances
5. Set up bank profile for payroll
6. Add emergency contacts

### **2. Daily Attendance Processing**
1. BioStar events sync every 15 minutes
2. Automatic attendance record creation
3. Overtime detection and request generation
4. Late arrival and absence tracking
5. Supervisor notifications for approvals

### **3. Monthly Payroll Processing**
1. Calculate attendance-based salaries
2. Process approved overtime
3. Apply leave deductions
4. Calculate statutory deductions
5. Generate payslips
6. Process bank transfers

### **4. Leave Management Workflow**
1. Employee submits leave request
2. Supervisor approval required
3. HR final approval for extended leave
4. Automatic leave balance deduction
5. Payroll integration for unpaid leave

## Performance Optimizations

### **1. Database Optimizations**
- Proper indexing on frequently queried fields
- Database connection pooling
- Query optimization for large datasets
- Pagination for list endpoints

### **2. Caching Strategy**
- Redis caching for frequently accessed data
- API response caching
- Session management optimization
- BioStar token caching

### **3. Background Processing**
- Celery for long-running tasks
- Async payroll processing
- Batch attendance synchronization
- Email notifications queue

## Security Implementation

### **1. Authentication & Authorization**
- JWT token-based authentication
- Role-based access control
- API rate limiting
- Session management

### **2. Data Protection**
- Encrypted sensitive data
- Audit logging for all operations
- Data backup and recovery
- GDPR compliance measures

### **3. API Security**
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CORS configuration

## Monitoring and Logging

### **1. System Health Monitoring**
```http
GET /api/system/health/            # System health check
GET /api/system/metrics/           # Performance metrics
```

### **2. Audit Logging**
- All user actions logged
- Payroll processing audit trail
- Leave approval tracking
- Attendance modification logs

### **3. Error Handling**
- Comprehensive error logging
- Automatic error notifications
- Graceful failure handling
- Recovery procedures

## Testing Strategy

### **1. Unit Tests**
- Model validation tests
- Business logic tests
- API endpoint tests
- Calculation accuracy tests

### **2. Integration Tests**
- BioStar API integration
- Payroll processing workflow
- Leave management workflow
- Authentication flow

### **3. Performance Tests**
- Load testing for API endpoints
- Database performance testing
- Concurrent user testing
- Memory usage optimization

## Quick Start with Real Data

### **1. Populate Real Data**
```bash
# Run the real data population script
python populate_real_data.py

# This creates:
# - 6 realistic employees with different roles
# - 5 departments with proper structure
# - 30 days of attendance records
# - Overtime types and leave policies
# - Company holidays for 2024
# - Salary profiles with Kenyan compliance
```

### **2. Test the System**
```bash
# Start the server
python manage.py runserver

# Test API endpoints
curl -X GET http://localhost:8000/api/users/
curl -X GET http://localhost:8000/api/attendance-records/
curl -X GET http://localhost:8000/api/payroll-records/

# Login credentials
# Username: john.doe
# Password: workflo2024
```

### **3. Verify Real Data**
```python
# Check data in Django shell
python manage.py shell

>>> from core.models.auth import User
>>> from core.models.attendance import AttendanceRecord
>>> print(f"Employees: {User.objects.count()}")
>>> print(f"Attendance Records: {AttendanceRecord.objects.count()}")
>>>
>>> # Check salary calculation
>>> from core.services.salary_calculator import salary_calculator
>>> employee = User.objects.first()
>>> calculation = salary_calculator.calculate_employee_salary(
...     employee, date(2024, 1, 1), date(2024, 1, 31)
... )
>>> print(f"Net Salary: KSH {calculation['totals']['net_salary']}")
```

This real data implementation provides a robust, production-ready HR management system with comprehensive employee management, attendance tracking, payroll processing, and BioStar integration capabilities.
