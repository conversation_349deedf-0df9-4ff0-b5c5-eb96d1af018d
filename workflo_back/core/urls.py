from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON>ult<PERSON><PERSON><PERSON>
from rest_framework_simplejwt.views import TokenRefreshView

# Import ViewSets
from .views.auth_views import UserViewSet, PasswordResetViewSet, CustomTokenObtainPairView
from .views.employee_views import (
    DepartmentViewSet, EmployeeProfileViewSet, SalaryProfileViewSet
)
from .views.attendance_views import (
    AttendanceRecordViewSet, BiostarEventViewSet, BiostarDeviceViewSet
)
from .views.leave_views import (
    LeaveTypeViewSet, LeaveBalanceViewSet, LeaveApplicationViewSet, CompanyHolidayViewSet
)
from .views.payroll_views import (
    PayCycleViewSet, PayrollRecordViewSet, PayrollAdjustmentViewSet, SalaryAdjustmentViewSet
)
from .views.performance_views import (
    PerformanceReviewTemplateViewSet, PerformanceReviewViewSet, PerformanceGoalViewSet
)
from .views.overtime_views import (
    OvertimeTypeViewSet, OvertimeRequestViewSet, OvertimeRecordViewSet,
    OvertimeApprovalWorkflowViewSet, OvertimeBudgetViewSet, OvertimeCalculationViewSet
)
from .views.training_views import (
    TrainingModuleViewSet, TrainingVenueViewSet, EmployeeTrainingAssignmentViewSet,
    TrainingSessionViewSet, TrainingSessionParticipantViewSet
)
# New imports for enhanced CRUD operations
from .views.engagement_views import (
    EmployeeSurveyViewSet, SurveyResponseViewSet, RecognitionCategoryViewSet,
    EmployeeRecognitionViewSet, WellnessProgramViewSet, WellnessProgramEnrollmentViewSet,
    WellnessActivityLogViewSet, EAPResourceViewSet, EmployeeFeedbackViewSet,
    BankProfileViewSet, EmergencyContactViewSet
)
from .views.events_views import CompanyEventViewSet, EventParticipantViewSet
from .views.document_views import (
    DocumentCategoryViewSet, EmployeeDocumentViewSet, CompanyDocumentViewSet,
    DocumentAcknowledgmentViewSet
)
from .views.workflow_views import (
    WorkflowDefinitionViewSet, WorkflowInstanceViewSet, WorkflowStepExecutionViewSet,
    AutomatedReminderViewSet
)
from .views.notification_views import (
    NotificationTemplateViewSet, NotificationViewSet, EmailLogViewSet, CommunicationViewSet
)
from .views.system_views import (
    AuditLogViewSet, ActivityLogViewSet, SystemSettingViewSet, CompanyInfoViewSet,
    SystemMonitoringViewSet
)
from .views.billing_views import (
    SubscriptionPlanViewSet, CompanySubscriptionViewSet, BillingInvoiceViewSet,
    ModuleSettingViewSet, FeatureToggleViewSet
)
from .views.reports_views import SavedReportViewSet, ReportHistoryViewSet
from .simple_health import SimpleHealthView, SimpleSystemHealthView, SimpleMetricsView

# Create router and register viewsets
router = DefaultRouter()

# Authentication & Users
router.register(r'users', UserViewSet)
router.register(r'password-reset', PasswordResetViewSet, basename='password-reset')

# Organization & Employees
router.register(r'departments', DepartmentViewSet)
router.register(r'employee-profiles', EmployeeProfileViewSet)
router.register(r'salary-profiles', SalaryProfileViewSet)

# Attendance & Time Tracking
router.register(r'attendance-records', AttendanceRecordViewSet)
router.register(r'biostar-events', BiostarEventViewSet)
router.register(r'biostar-devices', BiostarDeviceViewSet)

# Leave Management
router.register(r'leave-types', LeaveTypeViewSet)
router.register(r'leave-balances', LeaveBalanceViewSet)
router.register(r'leave-applications', LeaveApplicationViewSet)
router.register(r'company-holidays', CompanyHolidayViewSet)

# Payroll System
router.register(r'pay-cycles', PayCycleViewSet)
router.register(r'payroll-records', PayrollRecordViewSet)
router.register(r'payroll-adjustments', PayrollAdjustmentViewSet)
router.register(r'salary-adjustments', SalaryAdjustmentViewSet)

# Performance Management
router.register(r'performance-review-templates', PerformanceReviewTemplateViewSet)
router.register(r'performance-reviews', PerformanceReviewViewSet)
router.register(r'performance-goals', PerformanceGoalViewSet)

# Overtime Management
router.register(r'overtime-types', OvertimeTypeViewSet)
router.register(r'overtime-requests', OvertimeRequestViewSet)
router.register(r'overtime-records', OvertimeRecordViewSet)
router.register(r'overtime-approval-workflows', OvertimeApprovalWorkflowViewSet)
router.register(r'overtime-budgets', OvertimeBudgetViewSet)
router.register(r'overtime-calculations', OvertimeCalculationViewSet)

# Training & Development
router.register(r'training-modules', TrainingModuleViewSet)
router.register(r'training-venues', TrainingVenueViewSet)
router.register(r'employee-training-assignments', EmployeeTrainingAssignmentViewSet)
router.register(r'training-sessions', TrainingSessionViewSet)
router.register(r'training-session-participants', TrainingSessionParticipantViewSet)

# Employee Engagement & Wellness
router.register(r'employee-surveys', EmployeeSurveyViewSet)
router.register(r'survey-responses', SurveyResponseViewSet)
router.register(r'recognition-categories', RecognitionCategoryViewSet)
router.register(r'employee-recognitions', EmployeeRecognitionViewSet)
router.register(r'wellness-programs', WellnessProgramViewSet)
router.register(r'wellness-enrollments', WellnessProgramEnrollmentViewSet)
router.register(r'wellness-activity-logs', WellnessActivityLogViewSet)
router.register(r'eap-resources', EAPResourceViewSet)
router.register(r'employee-feedback', EmployeeFeedbackViewSet)

# Bank Profiles & Emergency Contacts (Enhanced CRUD)
router.register(r'bank-profiles', BankProfileViewSet)
router.register(r'emergency-contacts', EmergencyContactViewSet)

# Company Events & Culture
router.register(r'company-events', CompanyEventViewSet)
router.register(r'event-participants', EventParticipantViewSet)

# Document Management (with media capabilities)
router.register(r'document-categories', DocumentCategoryViewSet)
router.register(r'employee-documents', EmployeeDocumentViewSet)
router.register(r'company-documents', CompanyDocumentViewSet)
router.register(r'document-acknowledgments', DocumentAcknowledgmentViewSet)

# Workflow Automation & Approvals
router.register(r'workflow-definitions', WorkflowDefinitionViewSet)
router.register(r'workflow-instances', WorkflowInstanceViewSet)
router.register(r'workflow-step-executions', WorkflowStepExecutionViewSet)
router.register(r'automated-reminders', AutomatedReminderViewSet)

# Notifications & Communication
router.register(r'notification-templates', NotificationTemplateViewSet)
router.register(r'notifications', NotificationViewSet)
router.register(r'email-logs', EmailLogViewSet)
router.register(r'communication', CommunicationViewSet, basename='communication')

# Audit Logs & System Tracking
router.register(r'audit-logs', AuditLogViewSet)
router.register(r'activity-logs', ActivityLogViewSet)
router.register(r'system-settings', SystemSettingViewSet)
router.register(r'company-info', CompanyInfoViewSet)
router.register(r'system-monitoring', SystemMonitoringViewSet, basename='system-monitoring')

# Company Management & Billing
router.register(r'subscription-plans', SubscriptionPlanViewSet)
router.register(r'company-subscriptions', CompanySubscriptionViewSet)
router.register(r'billing-invoices', BillingInvoiceViewSet)
router.register(r'module-settings', ModuleSettingViewSet)
router.register(r'feature-toggles', FeatureToggleViewSet)

# Reports & Analytics
router.register(r'saved-reports', SavedReportViewSet)
router.register(r'report-history', ReportHistoryViewSet)

urlpatterns = [
    # JWT Authentication endpoints
    path('auth/login/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('auth/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # System Monitoring endpoints
    path('health/', SimpleSystemHealthView.as_view(), name='system_health'),
    path('metrics/', SimpleMetricsView.as_view(), name='system_metrics'),
    path('status/', SimpleSystemHealthView.as_view(), name='system_status'),

    # API endpoints (router already includes all the viewsets)
    path('', include(router.urls)),
]
