from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from rest_framework_simplejwt.views import TokenRefreshView

# Import ViewSets
from .views.auth_views import UserViewSet, PasswordResetViewSet, CustomTokenObtainPairView
from .views.employee_views import (
    DepartmentViewSet, EmployeeProfileViewSet, SalaryProfileViewSet
)
from .views.attendance_views import (
    AttendanceRecordViewSet, BiostarEventViewSet, BiostarDeviceViewSet
)
from .views.leave_views import (
    LeaveTypeViewSet, LeaveBalanceViewSet, LeaveApplicationViewSet, CompanyHolidayViewSet
)
from .views.payroll_views import (
    PayCycleViewSet, PayrollRecordViewSet, PayrollAdjustmentViewSet, SalaryAdjustmentViewSet
)
from .views.performance_views import (
    PerformanceReviewTemplateViewSet, PerformanceReviewViewSet, PerformanceGoalViewSet
)
from .views.overtime_views import (
    OvertimeTypeViewSet, OvertimeRequestViewSet, OvertimeRecordViewSet,
    OvertimeApprovalWorkflowViewSet, OvertimeBudgetViewSet, OvertimeCalculationViewSet
)
from .views.training_views import (
    TrainingModuleViewSet, TrainingVenueViewSet, EmployeeTrainingAssignmentViewSet,
    TrainingSessionViewSet, TrainingSessionParticipantViewSet
)
from .monitoring import SystemHealthView, SystemMetricsView

# Create router and register viewsets
router = DefaultRouter()

# Authentication & Users
router.register(r'users', UserViewSet)
router.register(r'password-reset', PasswordResetViewSet, basename='password-reset')

# Organization & Employees
router.register(r'departments', DepartmentViewSet)
router.register(r'employee-profiles', EmployeeProfileViewSet)
router.register(r'salary-profiles', SalaryProfileViewSet)

# Attendance & Time Tracking
router.register(r'attendance-records', AttendanceRecordViewSet)
router.register(r'biostar-events', BiostarEventViewSet)
router.register(r'biostar-devices', BiostarDeviceViewSet)

# Leave Management
router.register(r'leave-types', LeaveTypeViewSet)
router.register(r'leave-balances', LeaveBalanceViewSet)
router.register(r'leave-applications', LeaveApplicationViewSet)
router.register(r'company-holidays', CompanyHolidayViewSet)

# Payroll System
router.register(r'pay-cycles', PayCycleViewSet)
router.register(r'payroll-records', PayrollRecordViewSet)
router.register(r'payroll-adjustments', PayrollAdjustmentViewSet)
router.register(r'salary-adjustments', SalaryAdjustmentViewSet)

# Performance Management
router.register(r'performance-review-templates', PerformanceReviewTemplateViewSet)
router.register(r'performance-reviews', PerformanceReviewViewSet)
router.register(r'performance-goals', PerformanceGoalViewSet)

# Overtime Management
router.register(r'overtime-types', OvertimeTypeViewSet)
router.register(r'overtime-requests', OvertimeRequestViewSet)
router.register(r'overtime-records', OvertimeRecordViewSet)
router.register(r'overtime-approval-workflows', OvertimeApprovalWorkflowViewSet)
router.register(r'overtime-budgets', OvertimeBudgetViewSet)
router.register(r'overtime-calculations', OvertimeCalculationViewSet)

# Training & Development
router.register(r'training-modules', TrainingModuleViewSet)
router.register(r'training-venues', TrainingVenueViewSet)
router.register(r'employee-training-assignments', EmployeeTrainingAssignmentViewSet)
router.register(r'training-sessions', TrainingSessionViewSet)
router.register(r'training-session-participants', TrainingSessionParticipantViewSet)

urlpatterns = [
    # JWT Authentication endpoints
    path('auth/login/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('auth/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # System Monitoring endpoints
    path('health/', SystemHealthView.as_view(), name='system_health'),
    path('metrics/', SystemMetricsView.as_view(), name='system_metrics'),
    path('status/', SystemHealthView.as_view(), name='system_status'),

    # API endpoints (router already includes all the viewsets)
    path('', include(router.urls)),
]
