"""
BioStar 2 API Integration Service
Comprehensive service for integrating with Suprema BioStar 2 API
"""

import requests
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache
import json
import time

logger = logging.getLogger(__name__)


class BioStarAPIException(Exception):
    """Custom exception for BioStar API errors"""
    pass


class BioStarAPI:
    """
    BioStar 2 API Client
    Handles authentication, API calls, and data synchronization
    """
    
    def __init__(self):
        self.config = settings.BIOSTAR_API_CONFIG
        self.base_url = self.config['BASE_URL']
        self.username = self.config['USERNAME']
        self.password = self.config['PASSWORD']
        self.timeout = self.config['TIMEOUT']
        self.max_retries = self.config['MAX_RETRIES']
        # Check environment variable first, then config
        self.mock_mode = os.environ.get('BIOSTAR_MOCK_MODE', '').lower() == 'true' or self.config['MOCK_MODE']
        
        self.session = requests.Session()
        self.session.timeout = self.timeout
        self.access_token = None
        self.token_expires_at = None
        
    def _get_cache_key(self, key: str) -> str:
        """Generate cache key with prefix"""
        return f"biostar_api_{key}"
    
    def _make_request(self, method: str, endpoint: str, data: Dict = None, params: Dict = None) -> Dict:
        """
        Make HTTP request to BioStar API with retry logic
        """
        if self.mock_mode:
            return self._get_mock_response(endpoint, method)
            
        url = f"{self.base_url}/api/{endpoint}"
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        # Add authorization header if we have a token
        if self.access_token:
            headers['Authorization'] = f'Bearer {self.access_token}'
        
        for attempt in range(self.max_retries):
            try:
                response = self.session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=data,
                    params=params,
                    timeout=self.timeout
                )
                
                if response.status_code == 401:
                    # Token expired, try to refresh
                    if self.authenticate():
                        headers['Authorization'] = f'Bearer {self.access_token}'
                        response = self.session.request(
                            method=method,
                            url=url,
                            headers=headers,
                            json=data,
                            params=params,
                            timeout=self.timeout
                        )
                
                response.raise_for_status()
                return response.json()
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"BioStar API request failed (attempt {attempt + 1}): {str(e)}")
                if attempt == self.max_retries - 1:
                    raise BioStarAPIException(f"Failed to connect to BioStar API after {self.max_retries} attempts: {str(e)}")
                time.sleep(2 ** attempt)  # Exponential backoff
        
    def authenticate(self) -> bool:
        """
        Authenticate with BioStar API and get access token
        """
        try:
            # Check if we have a cached valid token
            cached_token = cache.get(self._get_cache_key('access_token'))
            if cached_token:
                self.access_token = cached_token
                return True
            
            if self.mock_mode:
                self.access_token = "mock_token_12345"
                cache.set(self._get_cache_key('access_token'), self.access_token, 3600)
                return True
            
            auth_data = {
                'User': {
                    'login_id': self.username,
                    'password': self.password
                }
            }
            
            response = self._make_request('POST', 'login', data=auth_data)
            
            if response.get('Response'):
                self.access_token = response['Response'].get('access_token')
                if self.access_token:
                    # Cache token for 1 hour
                    cache.set(self._get_cache_key('access_token'), self.access_token, 3600)
                    logger.info("Successfully authenticated with BioStar API")
                    return True
            
            logger.error("Failed to authenticate with BioStar API")
            return False
            
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return False
    
    def get_users(self, limit: int = 100, offset: int = 0) -> List[Dict]:
        """
        Get users from BioStar
        """
        try:
            if not self.access_token and not self.authenticate():
                raise BioStarAPIException("Authentication failed")
            
            params = {
                'limit': limit,
                'offset': offset
            }
            
            response = self._make_request('GET', 'users', params=params)
            return response.get('Response', {}).get('records', [])
            
        except Exception as e:
            logger.error(f"Error fetching users: {str(e)}")
            return []
    
    def get_user_by_id(self, user_id: str) -> Optional[Dict]:
        """
        Get specific user by ID
        """
        try:
            if not self.access_token and not self.authenticate():
                raise BioStarAPIException("Authentication failed")
            
            response = self._make_request('GET', f'users/{user_id}')
            return response.get('Response')
            
        except Exception as e:
            logger.error(f"Error fetching user {user_id}: {str(e)}")
            return None
    
    def get_events(self, start_datetime: datetime = None, end_datetime: datetime = None, 
                   user_id: str = None, limit: int = 1000) -> List[Dict]:
        """
        Get access events from BioStar
        """
        try:
            if not self.access_token and not self.authenticate():
                raise BioStarAPIException("Authentication failed")
            
            # Default to last 24 hours if no time range specified
            if not start_datetime:
                start_datetime = timezone.now() - timedelta(days=1)
            if not end_datetime:
                end_datetime = timezone.now()
            
            params = {
                'start_datetime': start_datetime.strftime('%Y-%m-%d %H:%M:%S'),
                'end_datetime': end_datetime.strftime('%Y-%m-%d %H:%M:%S'),
                'limit': limit
            }
            
            if user_id:
                params['user_id'] = user_id
            
            response = self._make_request('GET', 'events', params=params)
            return response.get('Response', {}).get('records', [])
            
        except Exception as e:
            logger.error(f"Error fetching events: {str(e)}")
            return []
    
    def get_devices(self) -> List[Dict]:
        """
        Get devices from BioStar
        """
        try:
            if not self.access_token and not self.authenticate():
                raise BioStarAPIException("Authentication failed")
            
            response = self._make_request('GET', 'devices')
            return response.get('Response', {}).get('records', [])
            
        except Exception as e:
            logger.error(f"Error fetching devices: {str(e)}")
            return []
    
    def get_realtime_events(self, last_event_id: str = None) -> List[Dict]:
        """
        Get real-time events from BioStar
        """
        try:
            if not self.access_token and not self.authenticate():
                raise BioStarAPIException("Authentication failed")
            
            params = {}
            if last_event_id:
                params['last_event_id'] = last_event_id
            
            response = self._make_request('GET', 'events/realtime', params=params)
            return response.get('Response', {}).get('records', [])
            
        except Exception as e:
            logger.error(f"Error fetching realtime events: {str(e)}")
            return []
    
    def _get_mock_response(self, endpoint: str, method: str) -> Dict:
        """
        Generate mock responses for testing
        """
        mock_responses = {
            'login': {
                'Response': {
                    'access_token': 'mock_token_12345',
                    'expires_in': 3600
                }
            },
            'users': {
                'Response': {
                    'records': [
                        {
                            'id': '1',
                            'login_id': 'emp001',
                            'name': 'John Doe',
                            'email': '<EMAIL>',
                            'department': 'IT',
                            'user_group_id': '1'
                        },
                        {
                            'id': '2',
                            'login_id': 'emp002',
                            'name': 'Jane Smith',
                            'email': '<EMAIL>',
                            'department': 'HR',
                            'user_group_id': '2'
                        }
                    ]
                }
            },
            'events': {
                'Response': {
                    'records': [
                        {
                            'id': '1001',
                            'user_id': '1',
                            'device_id': '101',
                            'event_type': 'entry',
                            'datetime': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                            'location': 'Main Entrance'
                        },
                        {
                            'id': '1002',
                            'user_id': '1',
                            'device_id': '101',
                            'event_type': 'exit',
                            'datetime': (timezone.now() + timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S'),
                            'location': 'Main Entrance'
                        }
                    ]
                }
            },
            'devices': {
                'Response': {
                    'records': [
                        {
                            'id': '101',
                            'name': 'Main Entrance',
                            'ip': '*************',
                            'port': 1470,
                            'status': 'online',
                            'location': 'Building A - Ground Floor'
                        }
                    ]
                }
            }
        }
        
        # Extract endpoint name for mock lookup
        endpoint_key = endpoint.split('/')[0]
        return mock_responses.get(endpoint_key, {'Response': {'records': []}})
    
    def test_connection(self) -> Dict[str, Any]:
        """
        Test BioStar API connection and return status
        """
        try:
            start_time = time.time()
            success = self.authenticate()
            response_time = time.time() - start_time
            
            if success:
                # Try to fetch a small amount of data to verify connection
                devices = self.get_devices()
                return {
                    'connected': True,
                    'response_time': response_time,
                    'device_count': len(devices),
                    'mock_mode': self.mock_mode,
                    'message': 'Successfully connected to BioStar API'
                }
            else:
                return {
                    'connected': False,
                    'response_time': response_time,
                    'mock_mode': self.mock_mode,
                    'message': 'Failed to authenticate with BioStar API'
                }
                
        except Exception as e:
            return {
                'connected': False,
                'response_time': 0,
                'mock_mode': self.mock_mode,
                'message': f'Connection error: {str(e)}'
            }


# Global instance
biostar_api = BioStarAPI()
