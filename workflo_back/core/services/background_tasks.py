"""
Background Task Processor for WorkFlo Backend
Handles asynchronous task processing and queue management
"""

import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any
from django.utils import timezone
from django.db import transaction
from django.core.mail import send_mail
from django.conf import settings

logger = logging.getLogger(__name__)


class BackgroundTaskProcessor:
    """
    Background task processor for handling asynchronous operations
    """
    
    def __init__(self):
        self.task_queue = []
        self.running_tasks = {}
        self.completed_tasks = {}
        self.failed_tasks = {}
        
    def process_pending_tasks(self):
        """Process all pending background tasks"""
        try:
            # Email processing
            self.process_email_queue()
            
            # Notification processing
            self.process_notifications()
            
            # Data cleanup
            self.cleanup_old_data()
            
            # System maintenance
            self.system_maintenance()
            
        except Exception as e:
            logger.error(f"Background task processing error: {str(e)}")
    
    def process_email_queue(self):
        """Process pending emails"""
        try:
            # Try to import EmailQueue model, skip if not available
            try:
                from core.models.notifications import EmailQueue
            except ImportError:
                logger.debug("EmailQueue model not available, skipping email processing")
                return

            # Get pending emails
            try:
                pending_emails = EmailQueue.objects.filter(
                    status='pending',
                    scheduled_at__lte=timezone.now()
                )[:10]  # Process 10 at a time
            except Exception:
                # If EmailQueue doesn't have these fields, skip
                logger.debug("EmailQueue model structure not compatible, skipping")
                return

            for email in pending_emails:
                try:
                    # Send email
                    send_mail(
                        subject=email.subject,
                        message=email.body,
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=[email.recipient],
                        fail_silently=False
                    )

                    # Mark as sent
                    email.status = 'sent'
                    email.sent_at = timezone.now()
                    email.save()

                    logger.debug(f"Email sent to {email.recipient}: {email.subject}")

                except Exception as e:
                    # Mark as failed
                    email.status = 'failed'
                    email.error_message = str(e)
                    email.attempts += 1
                    email.save()

                    logger.error(f"Failed to send email to {email.recipient}: {str(e)}")

        except Exception as e:
            logger.debug(f"Email queue processing skipped: {str(e)}")
    
    def process_notifications(self):
        """Process pending notifications"""
        try:
            # Try to import Notification model, skip if not available
            try:
                from core.models.notifications import Notification
            except ImportError:
                logger.debug("Notification model not available, skipping notification processing")
                return

            # Get pending notifications - use available fields
            try:
                # Try with scheduled_at field first
                pending_notifications = Notification.objects.filter(
                    status='pending',
                    scheduled_at__lte=timezone.now()
                )[:20]  # Process 20 at a time
            except Exception:
                # Fallback to basic filtering if scheduled_at doesn't exist
                try:
                    pending_notifications = Notification.objects.filter(
                        is_read=False
                    )[:20]
                except Exception:
                    logger.debug("Notification model structure not compatible, skipping")
                    return

            for notification in pending_notifications:
                try:
                    # Process notification based on type
                    if hasattr(notification, 'notification_type'):
                        if notification.notification_type == 'email':
                            self.send_email_notification(notification)
                        elif notification.notification_type == 'sms':
                            self.send_sms_notification(notification)
                        elif notification.notification_type == 'push':
                            self.send_push_notification(notification)
                    else:
                        # Default to email notification
                        self.send_email_notification(notification)

                    # Mark as processed
                    if hasattr(notification, 'status'):
                        notification.status = 'sent'
                    if hasattr(notification, 'sent_at'):
                        notification.sent_at = timezone.now()
                    if hasattr(notification, 'is_read'):
                        notification.is_read = True
                    notification.save()

                except Exception as e:
                    if hasattr(notification, 'status'):
                        notification.status = 'failed'
                    if hasattr(notification, 'error_message'):
                        notification.error_message = str(e)
                    notification.save()
                    logger.error(f"Notification processing failed: {str(e)}")

        except Exception as e:
            logger.debug(f"Notification processing skipped: {str(e)}")
    
    def send_email_notification(self, notification):
        """Send email notification"""
        try:
            send_mail(
                subject=notification.title,
                message=notification.message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[notification.recipient.email],
                fail_silently=False
            )
        except Exception as e:
            logger.error(f"Email notification failed: {str(e)}")
            raise
    
    def send_sms_notification(self, notification):
        """Send SMS notification (placeholder)"""
        # Implement SMS sending logic here
        logger.info(f"SMS notification sent to {notification.recipient}")
    
    def send_push_notification(self, notification):
        """Send push notification (placeholder)"""
        # Implement push notification logic here
        logger.info(f"Push notification sent to {notification.recipient}")
    
    def cleanup_old_data(self):
        """Clean up old data"""
        try:
            cutoff_date = timezone.now() - timedelta(days=90)

            # Clean up old logs
            try:
                from core.models.system import SystemLog
                old_logs = SystemLog.objects.filter(created_at__lt=cutoff_date)
                deleted_count = old_logs.count()
                old_logs.delete()

                if deleted_count > 0:
                    logger.info(f"Cleaned up {deleted_count} old system logs")
            except ImportError:
                logger.debug("SystemLog model not available, skipping log cleanup")
            except Exception as e:
                logger.debug(f"Log cleanup skipped: {str(e)}")

            # Clean up old notifications
            try:
                from core.models.notifications import Notification
                old_notifications = Notification.objects.filter(
                    created_at__lt=cutoff_date
                )
                # Only filter by status if the field exists
                if hasattr(Notification, 'status'):
                    old_notifications = old_notifications.filter(status__in=['sent', 'failed'])

                deleted_count = old_notifications.count()
                old_notifications.delete()

                if deleted_count > 0:
                    logger.info(f"Cleaned up {deleted_count} old notifications")
            except ImportError:
                logger.debug("Notification model not available, skipping notification cleanup")
            except Exception as e:
                logger.debug(f"Notification cleanup skipped: {str(e)}")

        except Exception as e:
            logger.debug(f"Data cleanup skipped: {str(e)}")
    
    def system_maintenance(self):
        """Perform system maintenance tasks"""
        try:
            # Update system metrics
            self.update_system_metrics()
            
            # Check for expired sessions
            self.cleanup_expired_sessions()
            
            # Optimize database
            self.optimize_database()
            
        except Exception as e:
            logger.error(f"System maintenance error: {str(e)}")
    
    def update_system_metrics(self):
        """Update system performance metrics"""
        try:
            # Try to import required modules
            try:
                from core.models.system import SystemMetrics
            except ImportError:
                logger.debug("SystemMetrics model not available, skipping metrics update")
                return

            try:
                import psutil
            except ImportError:
                logger.debug("psutil not available, skipping system metrics")
                return

            from django.db import connection

            # Get database stats
            try:
                with connection.cursor() as cursor:
                    cursor.execute("SELECT count(*) FROM django_session")
                    active_sessions = cursor.fetchone()[0]
            except Exception:
                active_sessions = 0

            # Get system stats
            try:
                cpu_usage = psutil.cpu_percent()
                memory_usage = psutil.virtual_memory().percent
                disk_usage = psutil.disk_usage('/').percent
            except Exception:
                cpu_usage = memory_usage = disk_usage = 0

            # Create metrics record
            SystemMetrics.objects.create(
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                disk_usage=disk_usage,
                active_sessions=active_sessions,
                timestamp=timezone.now()
            )

        except Exception as e:
            logger.debug(f"System metrics update skipped: {str(e)}")
    
    def cleanup_expired_sessions(self):
        """Clean up expired Django sessions"""
        try:
            from django.contrib.sessions.models import Session
            Session.objects.filter(expire_date__lt=timezone.now()).delete()
        except Exception as e:
            logger.error(f"Session cleanup error: {str(e)}")
    
    def optimize_database(self):
        """Perform database optimization"""
        try:
            from django.db import connection
            
            # Run VACUUM on PostgreSQL (if applicable)
            if 'postgresql' in settings.DATABASES['default']['ENGINE']:
                with connection.cursor() as cursor:
                    cursor.execute("VACUUM ANALYZE;")
                    
        except Exception as e:
            logger.error(f"Database optimization error: {str(e)}")
    
    def add_task(self, task_type: str, task_data: Dict[str, Any], priority: int = 5):
        """Add a task to the processing queue"""
        task = {
            'id': f"{task_type}_{int(time.time())}",
            'type': task_type,
            'data': task_data,
            'priority': priority,
            'created_at': timezone.now(),
            'status': 'pending'
        }
        
        self.task_queue.append(task)
        logger.info(f"Task added to queue: {task['id']}")
        
        return task['id']
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get the status of a specific task"""
        # Check running tasks
        if task_id in self.running_tasks:
            return self.running_tasks[task_id]
        
        # Check completed tasks
        if task_id in self.completed_tasks:
            return self.completed_tasks[task_id]
        
        # Check failed tasks
        if task_id in self.failed_tasks:
            return self.failed_tasks[task_id]
        
        # Check pending tasks
        for task in self.task_queue:
            if task['id'] == task_id:
                return task
        
        return {'status': 'not_found'}
    
    def get_queue_stats(self) -> Dict[str, int]:
        """Get statistics about the task queue"""
        return {
            'pending': len(self.task_queue),
            'running': len(self.running_tasks),
            'completed': len(self.completed_tasks),
            'failed': len(self.failed_tasks)
        }
