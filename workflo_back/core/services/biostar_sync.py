"""
BioStar Synchronization Service
Handles synchronization of BioStar data with local database
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from django.utils import timezone
from django.db import transaction
from django.core.cache import cache
from ..models.auth import User
from ..models.attendance import AttendanceRecord, BiostarEvent, BiostarDevice
from ..models.overtime import OvertimeRecord, OvertimeRequest
from .biostar_api import biostar_api

logger = logging.getLogger(__name__)


class BioStarSyncService:
    """
    Service for synchronizing BioStar data with local database
    """
    
    def __init__(self):
        self.api = biostar_api
        self.last_sync_key = 'biostar_last_sync'
        self.last_event_id_key = 'biostar_last_event_id'
    
    def sync_all_data(self) -> Dict:
        """
        Perform complete synchronization of all BioStar data
        """
        logger.info("Starting complete BioStar data synchronization")
        
        results = {
            'devices_synced': 0,
            'users_synced': 0,
            'events_synced': 0,
            'attendance_records_created': 0,
            'overtime_detected': 0,
            'errors': []
        }
        
        try:
            # Sync devices first
            devices_result = self.sync_devices()
            results['devices_synced'] = devices_result['synced_count']
            
            # Sync users
            users_result = self.sync_users()
            results['users_synced'] = users_result['synced_count']
            
            # Sync events (last 7 days)
            end_date = timezone.now()
            start_date = end_date - timedelta(days=7)
            events_result = self.sync_events(start_date, end_date)
            results['events_synced'] = events_result['synced_count']
            results['attendance_records_created'] = events_result['attendance_records_created']
            results['overtime_detected'] = events_result['overtime_detected']
            
            # Update last sync time
            cache.set(self.last_sync_key, timezone.now(), None)
            
            logger.info(f"BioStar synchronization completed: {results}")
            
        except Exception as e:
            error_msg = f"BioStar synchronization failed: {str(e)}"
            logger.error(error_msg)
            results['errors'].append(error_msg)
        
        return results
    
    def sync_devices(self) -> Dict:
        """
        Synchronize BioStar devices
        """
        logger.info("Synchronizing BioStar devices")
        
        try:
            devices_data = self.api.get_devices()
            synced_count = 0
            
            for device_data in devices_data:
                device, created = BiostarDevice.objects.update_or_create(
                    biostar_device_id=device_data.get('id'),
                    defaults={
                        'name': device_data.get('name', ''),
                        'ip_address': device_data.get('ip'),
                        'port': device_data.get('port'),
                        'location': device_data.get('location', ''),
                        'device_type': device_data.get('type', ''),
                        'status': self._map_device_status(device_data.get('status')),
                        'last_seen': timezone.now()
                    }
                )
                synced_count += 1
                
                if created:
                    logger.debug(f"Created new device: {device.name}")
                else:
                    logger.debug(f"Updated device: {device.name}")
            
            return {'synced_count': synced_count}
            
        except Exception as e:
            logger.error(f"Error synchronizing devices: {str(e)}")
            return {'synced_count': 0, 'error': str(e)}
    
    def sync_users(self) -> Dict:
        """
        Synchronize BioStar users with local employees
        """
        logger.info("Synchronizing BioStar users")
        
        try:
            users_data = self.api.get_users(limit=1000)
            synced_count = 0
            
            for user_data in users_data:
                # Try to match BioStar user with local employee
                biostar_login_id = user_data.get('login_id')
                biostar_email = user_data.get('email')
                
                # Try to find matching employee by employee_id or email
                employee = None
                if biostar_login_id:
                    try:
                        employee = User.objects.get(employee_id=biostar_login_id)
                    except User.DoesNotExist:
                        pass
                
                if not employee and biostar_email:
                    try:
                        employee = User.objects.get(email=biostar_email)
                    except User.DoesNotExist:
                        pass
                
                if employee:
                    # Update employee with BioStar user ID if not already set
                    if not hasattr(employee, 'biostar_user_id'):
                        # You might want to add this field to the User model
                        pass
                    synced_count += 1
                    logger.debug(f"Matched BioStar user {biostar_login_id} with employee {employee.employee_id}")
                else:
                    logger.warning(f"Could not match BioStar user {biostar_login_id} with any employee")
            
            return {'synced_count': synced_count}
            
        except Exception as e:
            logger.error(f"Error synchronizing users: {str(e)}")
            return {'synced_count': 0, 'error': str(e)}
    
    def sync_events(self, start_datetime: datetime = None, end_datetime: datetime = None) -> Dict:
        """
        Synchronize BioStar events and create attendance records
        """
        logger.info(f"Synchronizing BioStar events from {start_datetime} to {end_datetime}")
        
        if not start_datetime:
            start_datetime = timezone.now() - timedelta(hours=24)
        if not end_datetime:
            end_datetime = timezone.now()
        
        try:
            events_data = self.api.get_events(start_datetime, end_datetime)
            synced_count = 0
            attendance_records_created = 0
            overtime_detected = 0
            
            for event_data in events_data:
                with transaction.atomic():
                    # Create or update BioStar event
                    event, created = BiostarEvent.objects.update_or_create(
                        biostar_event_id=event_data.get('id'),
                        defaults={
                            'device_id': event_data.get('device_id'),
                            'device_name': event_data.get('device_name', ''),
                            'event_type': self._map_event_type(event_data.get('event_type')),
                            'event_datetime': self._parse_datetime(event_data.get('datetime')),
                            'location': event_data.get('location', ''),
                            'processed': False
                        }
                    )
                    
                    if created:
                        synced_count += 1
                        
                        # Try to find matching employee
                        employee = self._find_employee_for_event(event_data)
                        if employee:
                            event.employee = employee
                            event.save()
                            
                            # Process the event to create/update attendance record
                            attendance_result = self._process_attendance_event(event)
                            if attendance_result['created']:
                                attendance_records_created += 1
                            if attendance_result['overtime_detected']:
                                overtime_detected += 1
            
            return {
                'synced_count': synced_count,
                'attendance_records_created': attendance_records_created,
                'overtime_detected': overtime_detected
            }
            
        except Exception as e:
            logger.error(f"Error synchronizing events: {str(e)}")
            return {'synced_count': 0, 'error': str(e)}
    
    def sync_realtime_events(self) -> Dict:
        """
        Synchronize real-time events from BioStar
        """
        try:
            last_event_id = cache.get(self.last_event_id_key)
            events_data = self.api.get_realtime_events(last_event_id)
            
            processed_count = 0
            for event_data in events_data:
                # Process real-time event
                self._process_realtime_event(event_data)
                processed_count += 1
                
                # Update last event ID
                cache.set(self.last_event_id_key, event_data.get('id'), None)
            
            return {'processed_count': processed_count}
            
        except Exception as e:
            logger.error(f"Error synchronizing real-time events: {str(e)}")
            return {'processed_count': 0, 'error': str(e)}
    
    def _map_device_status(self, biostar_status: str) -> str:
        """
        Map BioStar device status to local status
        """
        status_mapping = {
            'online': 'online',
            'offline': 'offline',
            'maintenance': 'maintenance'
        }
        return status_mapping.get(biostar_status, 'offline')
    
    def _map_event_type(self, biostar_event_type: str) -> str:
        """
        Map BioStar event type to local event type
        """
        type_mapping = {
            'entry': 'entry',
            'exit': 'exit',
            'access_granted': 'entry',
            'access_denied': 'denied'
        }
        return type_mapping.get(biostar_event_type, 'unknown')
    
    def _parse_datetime(self, datetime_str: str) -> datetime:
        """
        Parse BioStar datetime string to Django datetime
        """
        try:
            # Assuming BioStar returns datetime in format: "YYYY-MM-DD HH:MM:SS"
            return timezone.datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
        except (ValueError, TypeError):
            return timezone.now()
    
    def _find_employee_for_event(self, event_data: Dict) -> Optional[User]:
        """
        Find employee matching the BioStar event
        """
        user_id = event_data.get('user_id')
        if not user_id:
            return None
        
        # Try to find employee by BioStar user ID
        # This would require adding biostar_user_id field to User model
        # For now, we'll try to match by employee_id
        try:
            return User.objects.get(employee_id=user_id)
        except User.DoesNotExist:
            logger.warning(f"Could not find employee for BioStar user ID: {user_id}")
            return None
    
    def _process_attendance_event(self, event: BiostarEvent) -> Dict:
        """
        Process BioStar event to create or update attendance record
        """
        if not event.employee:
            return {'created': False, 'overtime_detected': False}
        
        try:
            # Get or create attendance record for the date
            attendance_date = event.event_datetime.date()
            attendance_record, created = AttendanceRecord.objects.get_or_create(
                employee=event.employee,
                date=attendance_date,
                defaults={
                    'check_in': None,
                    'check_out': None,
                    'status': 'present'
                }
            )
            
            # Update check-in or check-out based on event type
            if event.event_type == 'entry' and not attendance_record.check_in:
                attendance_record.check_in = event.event_datetime
            elif event.event_type == 'exit':
                attendance_record.check_out = event.event_datetime
            
            # Calculate hours if both check-in and check-out are available
            overtime_detected = False
            if attendance_record.check_in and attendance_record.check_out:
                attendance_record.calculate_hours()
                
                # Check for overtime
                if attendance_record.overtime_hours and attendance_record.overtime_hours > 0:
                    overtime_detected = self._create_overtime_request(attendance_record)
            
            attendance_record.save()
            
            # Mark event as processed
            event.processed = True
            event.attendance_record = attendance_record
            event.save()
            
            return {'created': created, 'overtime_detected': overtime_detected}
            
        except Exception as e:
            logger.error(f"Error processing attendance event {event.biostar_event_id}: {str(e)}")
            return {'created': False, 'overtime_detected': False}
    
    def _create_overtime_request(self, attendance_record: AttendanceRecord) -> bool:
        """
        Create overtime request for detected overtime hours
        """
        try:
            # Check if overtime request already exists
            existing_request = OvertimeRequest.objects.filter(
                employee=attendance_record.employee,
                date=attendance_record.date
            ).first()
            
            if existing_request:
                return False
            
            # Create overtime request
            OvertimeRequest.objects.create(
                employee=attendance_record.employee,
                date=attendance_record.date,
                requested_hours=attendance_record.overtime_hours,
                reason=f"Auto-detected overtime from BioStar attendance",
                status='pending',
                auto_detected=True
            )
            
            logger.info(f"Created overtime request for {attendance_record.employee.employee_id} on {attendance_record.date}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating overtime request: {str(e)}")
            return False
    
    def _process_realtime_event(self, event_data: Dict):
        """
        Process real-time event from BioStar
        """
        # This would handle real-time notifications
        # For now, we'll just log the event
        logger.info(f"Real-time event received: {event_data}")


# Global instance
biostar_sync = BioStarSyncService()
