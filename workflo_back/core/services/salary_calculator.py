"""
Dynamic Salary Calculation Service
Calculates salaries based on BioStar attendance data, overtime, and company policies
"""

import logging
from datetime import datetime, timedelta, date
from decimal import Decimal, ROUND_HALF_UP
from typing import Dict, List, Optional, Tuple
from django.conf import settings
from django.utils import timezone
from django.db.models import Sum, Q
from ..models.auth import User
from ..models.employees import SalaryProfile
from ..models.attendance import AttendanceRecord
from ..models.overtime import OvertimeRecord, OvertimeCalculation
from ..models.leave import LeaveApplication, CompanyHoliday
from ..models.payroll import PayrollRecord

logger = logging.getLogger(__name__)


class SalaryCalculationException(Exception):
    """Custom exception for salary calculation errors"""
    pass


class DynamicSalaryCalculator:
    """
    Dynamic salary calculator that uses BioStar attendance data
    to calculate employee salaries with overtime and deductions
    """
    
    def __init__(self):
        self.config = settings.DYNAMIC_SALARY_CONFIG
        self.enabled = self.config['ENABLED']
        self.standard_work_hours = self.config['STANDARD_WORK_HOURS']
        self.standard_work_days = self.config['STANDARD_WORK_DAYS']
        self.overtime_threshold = self.config['OVERTIME_THRESHOLD']
        self.overtime_multiplier = self.config['OVERTIME_MULTIPLIER']
        self.weekend_multiplier = self.config['WEEKEND_MULTIPLIER']
        self.holiday_multiplier = self.config['HOLIDAY_MULTIPLIER']
        self.late_penalty_enabled = self.config['LATE_PENALTY_ENABLED']
        self.late_penalty_rate = self.config['LATE_PENALTY_RATE']
        self.absence_deduction_enabled = self.config['ABSENCE_DEDUCTION_ENABLED']
    
    def calculate_employee_salary(self, employee: User, start_date: date, end_date: date) -> Dict:
        """
        Calculate employee salary for a given period based on attendance data
        """
        try:
            # Get employee salary profile
            salary_profile = SalaryProfile.objects.get(employee=employee)
            
            # Get attendance records for the period
            attendance_records = AttendanceRecord.objects.filter(
                employee=employee,
                date__range=[start_date, end_date]
            ).order_by('date')
            
            # Calculate working days and hours
            working_days_data = self._calculate_working_days(start_date, end_date)
            attendance_data = self._analyze_attendance_records(attendance_records, working_days_data)
            
            # Calculate overtime
            overtime_data = self._calculate_overtime(employee, start_date, end_date, attendance_records)
            
            # Calculate base salary components
            base_calculation = self._calculate_base_salary(salary_profile, attendance_data, working_days_data)
            
            # Calculate deductions
            deductions = self._calculate_deductions(employee, attendance_data, working_days_data)
            
            # Calculate final amounts
            gross_salary = (
                base_calculation['base_amount'] + 
                base_calculation['allowances'] + 
                overtime_data['total_overtime_amount']
            )
            
            total_deductions = sum(deductions.values())
            net_salary = gross_salary - total_deductions
            
            return {
                'employee_id': employee.id,
                'employee_name': f"{employee.first_name} {employee.last_name}",
                'calculation_period': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'salary_profile': {
                    'basic_salary': salary_profile.basic_salary,
                    'hourly_rate': salary_profile.hourly_rate,
                    'allowances': salary_profile.allowances,
                    'overtime_rate': salary_profile.overtime_rate
                },
                'working_days': working_days_data,
                'attendance_summary': attendance_data,
                'overtime_summary': overtime_data,
                'base_calculation': base_calculation,
                'deductions': deductions,
                'totals': {
                    'gross_salary': gross_salary,
                    'total_deductions': total_deductions,
                    'net_salary': net_salary
                },
                'calculation_method': 'dynamic' if self.enabled else 'static',
                'calculated_at': timezone.now()
            }
            
        except SalaryProfile.DoesNotExist:
            raise SalaryCalculationException(f"Salary profile not found for employee {employee.employee_id}")
        except Exception as e:
            logger.error(f"Error calculating salary for employee {employee.employee_id}: {str(e)}")
            raise SalaryCalculationException(f"Salary calculation failed: {str(e)}")
    
    def _calculate_working_days(self, start_date: date, end_date: date) -> Dict:
        """
        Calculate working days, weekends, and holidays in the period
        """
        total_days = (end_date - start_date).days + 1
        working_days = 0
        weekend_days = 0
        holiday_days = 0
        
        # Get company holidays in the period
        holidays = CompanyHoliday.objects.filter(
            date__range=[start_date, end_date]
        ).values_list('date', flat=True)
        
        current_date = start_date
        while current_date <= end_date:
            if current_date in holidays:
                holiday_days += 1
            elif current_date.weekday() >= 5:  # Saturday = 5, Sunday = 6
                weekend_days += 1
            else:
                working_days += 1
            current_date += timedelta(days=1)
        
        return {
            'total_days': total_days,
            'working_days': working_days,
            'weekend_days': weekend_days,
            'holiday_days': holiday_days,
            'expected_working_days': working_days
        }
    
    def _analyze_attendance_records(self, attendance_records, working_days_data: Dict) -> Dict:
        """
        Analyze attendance records to calculate worked hours and patterns
        """
        total_hours_worked = Decimal('0')
        total_regular_hours = Decimal('0')
        total_overtime_hours = Decimal('0')
        days_present = 0
        days_late = 0
        days_absent = 0
        
        # Create a set of dates with attendance
        attended_dates = set()
        
        for record in attendance_records:
            if record.check_in and record.check_out:
                days_present += 1
                attended_dates.add(record.date)
                
                # Calculate hours
                if record.total_hours:
                    total_hours_worked += record.total_hours
                if record.regular_hours:
                    total_regular_hours += record.regular_hours
                if record.overtime_hours:
                    total_overtime_hours += record.overtime_hours
                
                # Check if late (assuming 9:00 AM is standard start time)
                standard_start = record.check_in.replace(hour=9, minute=0, second=0, microsecond=0)
                if record.check_in > standard_start:
                    days_late += 1
        
        # Calculate absent days (working days without attendance)
        expected_working_days = working_days_data['working_days']
        days_absent = expected_working_days - days_present
        
        return {
            'days_present': days_present,
            'days_late': days_late,
            'days_absent': days_absent,
            'total_hours_worked': total_hours_worked,
            'total_regular_hours': total_regular_hours,
            'total_overtime_hours': total_overtime_hours,
            'attendance_rate': (days_present / expected_working_days * 100) if expected_working_days > 0 else 0,
            'punctuality_rate': ((days_present - days_late) / days_present * 100) if days_present > 0 else 0
        }
    
    def _calculate_overtime(self, employee: User, start_date: date, end_date: date, attendance_records) -> Dict:
        """
        Calculate overtime hours and amounts
        """
        # Get approved overtime records
        overtime_records = OvertimeRecord.objects.filter(
            employee=employee,
            date__range=[start_date, end_date],
            status='approved'
        )
        
        total_overtime_hours = Decimal('0')
        total_overtime_amount = Decimal('0')
        weekend_overtime_hours = Decimal('0')
        holiday_overtime_hours = Decimal('0')
        regular_overtime_hours = Decimal('0')
        
        # Get holidays in the period
        holidays = set(CompanyHoliday.objects.filter(
            date__range=[start_date, end_date]
        ).values_list('date', flat=True))
        
        for record in overtime_records:
            overtime_hours = record.hours_worked
            total_overtime_hours += overtime_hours
            
            # Determine overtime type and rate
            if record.date in holidays:
                holiday_overtime_hours += overtime_hours
                rate_multiplier = self.holiday_multiplier
            elif record.date.weekday() >= 5:  # Weekend
                weekend_overtime_hours += overtime_hours
                rate_multiplier = self.weekend_multiplier
            else:
                regular_overtime_hours += overtime_hours
                rate_multiplier = self.overtime_multiplier
            
            # Calculate overtime amount
            salary_profile = SalaryProfile.objects.get(employee=employee)
            hourly_rate = salary_profile.hourly_rate or (salary_profile.basic_salary / (self.standard_work_days * self.standard_work_hours))
            overtime_amount = overtime_hours * hourly_rate * Decimal(str(rate_multiplier))
            total_overtime_amount += overtime_amount
        
        return {
            'total_overtime_hours': total_overtime_hours,
            'regular_overtime_hours': regular_overtime_hours,
            'weekend_overtime_hours': weekend_overtime_hours,
            'holiday_overtime_hours': holiday_overtime_hours,
            'total_overtime_amount': total_overtime_amount,
            'overtime_records_count': overtime_records.count()
        }
    
    def _calculate_base_salary(self, salary_profile: SalaryProfile, attendance_data: Dict, working_days_data: Dict) -> Dict:
        """
        Calculate base salary based on attendance
        """
        if not self.enabled:
            # Static salary calculation
            return {
                'base_amount': salary_profile.basic_salary,
                'allowances': salary_profile.allowances,
                'calculation_method': 'static'
            }
        
        # Dynamic salary calculation based on attendance
        expected_hours = working_days_data['working_days'] * self.standard_work_hours
        actual_hours = attendance_data['total_regular_hours']
        
        if salary_profile.hourly_rate:
            # Hourly-based calculation
            base_amount = actual_hours * salary_profile.hourly_rate
        else:
            # Salary-based calculation with attendance adjustment
            attendance_rate = attendance_data['attendance_rate'] / 100
            base_amount = salary_profile.basic_salary * Decimal(str(attendance_rate))
        
        return {
            'base_amount': base_amount,
            'allowances': salary_profile.allowances,
            'expected_hours': expected_hours,
            'actual_hours': actual_hours,
            'calculation_method': 'dynamic'
        }
    
    def _calculate_deductions(self, employee: User, attendance_data: Dict, working_days_data: Dict) -> Dict:
        """
        Calculate various deductions
        """
        deductions = {
            'late_penalty': Decimal('0'),
            'absence_deduction': Decimal('0'),
            'leave_deduction': Decimal('0')
        }
        
        salary_profile = SalaryProfile.objects.get(employee=employee)
        daily_rate = salary_profile.basic_salary / self.standard_work_days
        
        # Late penalty
        if self.late_penalty_enabled and attendance_data['days_late'] > 0:
            deductions['late_penalty'] = daily_rate * Decimal(str(self.late_penalty_rate)) * attendance_data['days_late']
        
        # Absence deduction
        if self.absence_deduction_enabled and attendance_data['days_absent'] > 0:
            deductions['absence_deduction'] = daily_rate * attendance_data['days_absent']
        
        return deductions
    
    def calculate_bulk_salaries(self, employees: List[User], start_date: date, end_date: date) -> List[Dict]:
        """
        Calculate salaries for multiple employees
        """
        results = []
        for employee in employees:
            try:
                calculation = self.calculate_employee_salary(employee, start_date, end_date)
                results.append(calculation)
            except Exception as e:
                logger.error(f"Failed to calculate salary for employee {employee.employee_id}: {str(e)}")
                results.append({
                    'employee_id': employee.id,
                    'error': str(e),
                    'calculation_failed': True
                })
        
        return results


# Global instance
salary_calculator = DynamicSalaryCalculator()
