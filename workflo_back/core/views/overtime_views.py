"""
Overtime Management Views
Comprehensive ViewSets for overtime-related operations
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from drf_spectacular.utils import extend_schema, extend_schema_view
from django.utils import timezone
import logging

from ..models.overtime import (
    OvertimeType, OvertimeRequest, OvertimeRecord, 
    OvertimeApprovalWorkflow, OvertimeBudget, OvertimeCalculation
)
from ..serializers.overtime import (
    OvertimeTypeSerializer, OvertimeRequestSerializer, OvertimeRecordSerializer,
    OvertimeApprovalWorkflowSerializer, OvertimeBudgetSerializer, OvertimeCalculationSerializer
)
from ..permissions import IsOwnerOrSupervisor, IsHROrAdmin

logger = logging.getLogger(__name__)


@extend_schema_view(
    list=extend_schema(description="List overtime types"),
    create=extend_schema(description="Create new overtime type"),
    retrieve=extend_schema(description="Get overtime type details"),
    update=extend_schema(description="Update overtime type"),
    destroy=extend_schema(description="Delete overtime type"),
)
class OvertimeTypeViewSet(viewsets.ModelViewSet):
    """ViewSet for managing overtime types"""
    queryset = OvertimeType.objects.all()
    serializer_class = OvertimeTypeSerializer
    permission_classes = [permissions.IsAuthenticated, IsHROrAdmin]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['requires_pre_approval']  # Temporarily removed 'is_active'
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'rate_multiplier', 'created_at']
    ordering = ['-created_at']


@extend_schema_view(
    list=extend_schema(description="List overtime requests"),
    create=extend_schema(description="Create new overtime request"),
    retrieve=extend_schema(description="Get overtime request details"),
    update=extend_schema(description="Update overtime request"),
    destroy=extend_schema(description="Delete overtime request"),
)
class OvertimeRequestViewSet(viewsets.ModelViewSet):
    """ViewSet for managing overtime requests"""
    queryset = OvertimeRequest.objects.all()
    serializer_class = OvertimeRequestSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'overtime_type', 'request_date']
    search_fields = ['reason', 'justification']
    ordering_fields = ['request_date', 'planned_hours', 'created_at']
    ordering = ['-created_at']

    def get_queryset(self):
        """Filter queryset based on user role"""
        user = self.request.user
        if user.role in ['admin', 'hr']:
            return OvertimeRequest.objects.all()
        elif user.role == 'supervisor':
            # Supervisors can see requests from their department
            return OvertimeRequest.objects.filter(
                employee__employee_profile__department__supervisor=user
            )
        else:
            # Employees can only see their own requests
            return OvertimeRequest.objects.filter(employee=user)

    @action(detail=True, methods=['post'], permission_classes=[IsOwnerOrSupervisor])
    def approve(self, request, pk=None):
        """Approve overtime request"""
        overtime_request = self.get_object()
        overtime_request.status = 'approved'
        overtime_request.supervisor_approved_by = request.user
        overtime_request.supervisor_approved_at = timezone.now()
        overtime_request.supervisor_comments = request.data.get('comments', '')
        overtime_request.save()
        
        logger.info(f"Overtime request {pk} approved by {request.user}")
        return Response({'status': 'approved'})

    @action(detail=True, methods=['post'], permission_classes=[IsOwnerOrSupervisor])
    def reject(self, request, pk=None):
        """Reject overtime request"""
        overtime_request = self.get_object()
        overtime_request.status = 'rejected'
        overtime_request.rejection_reason = request.data.get('reason', '')
        overtime_request.save()
        
        logger.info(f"Overtime request {pk} rejected by {request.user}")
        return Response({'status': 'rejected'})


@extend_schema_view(
    list=extend_schema(description="List overtime records"),
    create=extend_schema(description="Create new overtime record"),
    retrieve=extend_schema(description="Get overtime record details"),
    update=extend_schema(description="Update overtime record"),
    destroy=extend_schema(description="Delete overtime record"),
)
class OvertimeRecordViewSet(viewsets.ModelViewSet):
    """ViewSet for managing overtime records"""
    queryset = OvertimeRecord.objects.all()
    serializer_class = OvertimeRecordSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'overtime_type', 'overtime_date', 'is_emergency']
    search_fields = ['employee__first_name', 'employee__last_name']
    ordering_fields = ['overtime_date', 'total_hours', 'total_amount']
    ordering = ['-overtime_date']

    def get_queryset(self):
        """Filter queryset based on user role"""
        user = self.request.user
        if user.role in ['admin', 'hr', 'accountant']:
            return OvertimeRecord.objects.all()
        elif user.role == 'supervisor':
            return OvertimeRecord.objects.filter(
                employee__employee_profile__department__supervisor=user
            )
        else:
            return OvertimeRecord.objects.filter(employee=user)


@extend_schema_view(
    list=extend_schema(description="List overtime approval workflows"),
    create=extend_schema(description="Create new overtime approval workflow"),
    retrieve=extend_schema(description="Get overtime approval workflow details"),
    update=extend_schema(description="Update overtime approval workflow"),
    destroy=extend_schema(description="Delete overtime approval workflow"),
)
class OvertimeApprovalWorkflowViewSet(viewsets.ModelViewSet):
    """ViewSet for managing overtime approval workflows"""
    queryset = OvertimeApprovalWorkflow.objects.all()
    serializer_class = OvertimeApprovalWorkflowSerializer
    permission_classes = [permissions.IsAuthenticated, IsHROrAdmin]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['department']  # Temporarily removed 'is_active'
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['-created_at']


@extend_schema_view(
    list=extend_schema(description="List overtime budgets"),
    create=extend_schema(description="Create new overtime budget"),
    retrieve=extend_schema(description="Get overtime budget details"),
    update=extend_schema(description="Update overtime budget"),
    destroy=extend_schema(description="Delete overtime budget"),
)
class OvertimeBudgetViewSet(viewsets.ModelViewSet):
    """ViewSet for managing overtime budgets"""
    queryset = OvertimeBudget.objects.all()
    serializer_class = OvertimeBudgetSerializer
    permission_classes = [permissions.IsAuthenticated, IsHROrAdmin]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['budget_year', 'budget_month', 'budget_exceeded']  # Temporarily removed 'is_active'
    search_fields = ['department__name', 'employee__first_name', 'employee__last_name']
    ordering_fields = ['budget_year', 'budget_month', 'allocated_amount']
    ordering = ['-budget_year', '-budget_month']

    def get_queryset(self):
        """Filter queryset based on user role"""
        user = self.request.user
        if user.role in ['admin', 'hr']:
            return OvertimeBudget.objects.all()
        elif user.role == 'supervisor':
            return OvertimeBudget.objects.filter(department__supervisor=user)
        else:
            return OvertimeBudget.objects.filter(employee=user)

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get overtime budget summary"""
        queryset = self.get_queryset()
        total_allocated = sum(budget.allocated_amount for budget in queryset)
        total_used = sum(budget.used_amount for budget in queryset)
        total_remaining = total_allocated - total_used
        
        return Response({
            'total_allocated': total_allocated,
            'total_used': total_used,
            'total_remaining': total_remaining,
            'utilization_percentage': (total_used / total_allocated * 100) if total_allocated > 0 else 0
        })


@extend_schema_view(
    list=extend_schema(description="List overtime calculations"),
    create=extend_schema(description="Create new overtime calculation"),
    retrieve=extend_schema(description="Get overtime calculation details"),
    update=extend_schema(description="Update overtime calculation"),
    destroy=extend_schema(description="Delete overtime calculation"),
)
class OvertimeCalculationViewSet(viewsets.ModelViewSet):
    """ViewSet for managing overtime calculations"""
    queryset = OvertimeCalculation.objects.all()
    serializer_class = OvertimeCalculationSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'calculation_method', 'included_in_payroll']
    search_fields = ['employee__first_name', 'employee__last_name']
    ordering_fields = ['calculation_date', 'gross_overtime_amount', 'net_overtime_amount']
    ordering = ['-calculation_date']

    def get_queryset(self):
        """Filter queryset based on user role"""
        user = self.request.user
        if user.role in ['admin', 'hr', 'accountant']:
            return OvertimeCalculation.objects.all()
        elif user.role == 'supervisor':
            return OvertimeCalculation.objects.filter(
                employee__employee_profile__department__supervisor=user
            )
        else:
            return OvertimeCalculation.objects.filter(employee=user)

    @action(detail=True, methods=['post'], permission_classes=[IsHROrAdmin])
    def approve_calculation(self, request, pk=None):
        """Approve overtime calculation"""
        calculation = self.get_object()
        calculation.status = 'approved'
        calculation.approved_by = request.user
        calculation.approved_at = timezone.now()
        calculation.save()
        
        logger.info(f"Overtime calculation {pk} approved by {request.user}")
        return Response({'status': 'approved'})

    @action(detail=False, methods=['get'])
    def pending_approvals(self, request):
        """Get pending overtime calculations for approval"""
        pending = self.get_queryset().filter(status='calculated')
        serializer = self.get_serializer(pending, many=True)
        return Response(serializer.data)
