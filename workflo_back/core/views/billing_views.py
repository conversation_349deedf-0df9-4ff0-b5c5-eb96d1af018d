from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Q, Count, Sum
from django.utils import timezone

from ..models.billing import (
    SubscriptionPlan, CompanySubscription, BillingInvoice,
    ModuleSetting, FeatureToggle
)
from ..serializers.billing_serializers import (
    SubscriptionPlanSerializer, CompanySubscriptionSerializer, BillingInvoiceSerializer,
    ModuleSettingSerializer, FeatureToggleSerializer
)


class SubscriptionPlanViewSet(viewsets.ModelViewSet):
    """ViewSet for Subscription Plans"""
    queryset = SubscriptionPlan.objects.filter(is_active=True)
    serializer_class = SubscriptionPlanSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['billing_cycle']  # Temporarily removed 'is_active'
    search_fields = ['plan_name', 'description']
    ordering = ['plan_name']


class CompanySubscriptionViewSet(viewsets.ModelViewSet):
    """ViewSet for Company Subscriptions"""
    queryset = CompanySubscription.objects.all()
    serializer_class = CompanySubscriptionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['plan', 'status', 'auto_renew']
    ordering = ['-created_at']
    
    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get current active subscription"""
        current_subscription = CompanySubscription.objects.filter(
            status='active',
            start_date__lte=timezone.now().date(),
            end_date__gte=timezone.now().date()
        ).first()
        
        if current_subscription:
            serializer = self.get_serializer(current_subscription)
            return Response(serializer.data)
        else:
            return Response({
                'message': 'No active subscription found'
            }, status=status.HTTP_404_NOT_FOUND)
    
    @action(detail=True, methods=['post'])
    def renew(self, request, pk=None):
        """Renew a subscription"""
        subscription = self.get_object()
        
        # Create new subscription period
        new_end_date = subscription.end_date + timezone.timedelta(days=30)  # Monthly renewal
        
        subscription.end_date = new_end_date
        subscription.save()
        
        serializer = self.get_serializer(subscription)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel a subscription"""
        subscription = self.get_object()
        subscription.status = 'cancelled'
        subscription.auto_renew = False
        subscription.save()
        
        serializer = self.get_serializer(subscription)
        return Response(serializer.data)


class BillingInvoiceViewSet(viewsets.ModelViewSet):
    """ViewSet for Billing Invoices"""
    queryset = BillingInvoice.objects.all()
    serializer_class = BillingInvoiceSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['subscription', 'status', 'currency']
    search_fields = ['invoice_number']
    ordering = ['-created_at']
    
    @action(detail=True, methods=['post'])
    def mark_as_paid(self, request, pk=None):
        """Mark invoice as paid"""
        invoice = self.get_object()
        
        invoice.status = 'paid'
        invoice.paid_date = timezone.now().date()
        invoice.payment_reference = request.data.get('payment_reference', '')
        invoice.payment_method = request.data.get('payment_method', '')
        invoice.save()
        
        serializer = self.get_serializer(invoice)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get billing summary"""
        invoices = self.get_queryset()
        
        summary = {
            'total_invoices': invoices.count(),
            'paid_invoices': invoices.filter(status='paid').count(),
            'pending_invoices': invoices.filter(status='pending').count(),
            'overdue_invoices': invoices.filter(
                status='pending',
                due_date__lt=timezone.now().date()
            ).count(),
            'total_amount': invoices.aggregate(
                total=Sum('total_amount')
            )['total'] or 0,
            'paid_amount': invoices.filter(status='paid').aggregate(
                total=Sum('total_amount')
            )['total'] or 0,
            'outstanding_amount': invoices.filter(status='pending').aggregate(
                total=Sum('total_amount')
            )['total'] or 0
        }
        
        return Response(summary)


class ModuleSettingViewSet(viewsets.ModelViewSet):
    """ViewSet for Module Settings"""
    queryset = ModuleSetting.objects.all()
    serializer_class = ModuleSettingSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['module_name', 'setting_type', 'is_required']
    search_fields = ['setting_key', 'description']
    ordering = ['module_name', 'setting_key']
    
    def perform_update(self, serializer):
        serializer.save(updated_by=self.request.user)
    
    @action(detail=False, methods=['get'])
    def by_module(self, request):
        """Get settings grouped by module"""
        module_name = request.query_params.get('module')
        if module_name:
            settings = self.get_queryset().filter(module_name=module_name)
            serializer = self.get_serializer(settings, many=True)
            return Response(serializer.data)
        
        # Group all settings by module
        modules = {}
        for setting in self.get_queryset():
            if setting.module_name not in modules:
                modules[setting.module_name] = []
            
            serializer = self.get_serializer(setting)
            modules[setting.module_name].append(serializer.data)
        
        return Response(modules)


class FeatureToggleViewSet(viewsets.ModelViewSet):
    """ViewSet for Feature Toggles"""
    queryset = FeatureToggle.objects.all()
    serializer_class = FeatureToggleSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['is_enabled', 'environment']
    search_fields = ['feature_name', 'description']
    ordering = ['feature_name']
    
    def perform_update(self, serializer):
        serializer.save(updated_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def enable(self, request, pk=None):
        """Enable a feature toggle"""
        feature = self.get_object()
        feature.is_enabled = True
        feature.updated_by = request.user
        feature.save()
        
        serializer = self.get_serializer(feature)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def disable(self, request, pk=None):
        """Disable a feature toggle"""
        feature = self.get_object()
        feature.is_enabled = False
        feature.updated_by = request.user
        feature.save()
        
        serializer = self.get_serializer(feature)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def enabled_features(self, request):
        """Get all enabled features"""
        enabled_features = self.get_queryset().filter(is_enabled=True)
        serializer = self.get_serializer(enabled_features, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def bulk_update(self, request):
        """Bulk update feature toggles"""
        updates = request.data.get('updates', [])
        updated_count = 0
        
        for update in updates:
            feature_id = update.get('id')
            is_enabled = update.get('is_enabled')
            
            if feature_id is not None and is_enabled is not None:
                try:
                    feature = FeatureToggle.objects.get(id=feature_id)
                    feature.is_enabled = is_enabled
                    feature.updated_by = request.user
                    feature.save()
                    updated_count += 1
                except FeatureToggle.DoesNotExist:
                    continue
        
        return Response({
            'message': f'Updated {updated_count} feature toggles'
        })
