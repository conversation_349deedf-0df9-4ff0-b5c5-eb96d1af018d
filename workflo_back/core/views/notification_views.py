from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Q, Count
from django.utils import timezone

from ..models.notifications import NotificationTemplate, Notification, EmailLog
from ..models.auth import User
from ..serializers.notification_serializers import (
    NotificationTemplateSerializer, NotificationSerializer, EmailLogSerializer,
    BulkNotificationSerializer, CommunicationPreferencesSerializer
)


class NotificationTemplateViewSet(viewsets.ModelViewSet):
    """ViewSet for Notification Templates"""
    queryset = NotificationTemplate.objects.all()
    serializer_class = NotificationTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['notification_type']  # Temporarily removed 'is_active'
    search_fields = ['name', 'subject', 'body']
    ordering = ['name']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class NotificationViewSet(viewsets.ModelViewSet):
    """ViewSet for Notifications with enhanced communication features"""
    queryset = Notification.objects.all()
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['notification_type', 'priority', 'is_read', 'recipient', 'sender']
    search_fields = ['title', 'message']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """Filter notifications based on user permissions"""
        queryset = super().get_queryset()
        
        # Filter by user context
        filter_type = self.request.query_params.get('filter')
        if filter_type == 'my_notifications':
            queryset = queryset.filter(recipient=self.request.user)
        elif filter_type == 'unread':
            queryset = queryset.filter(recipient=self.request.user, is_read=False)
        elif filter_type == 'sent_by_me':
            queryset = queryset.filter(sender=self.request.user)
        
        return queryset
    
    @action(detail=True, methods=['post'])
    def mark_as_read(self, request, pk=None):
        """Mark notification as read"""
        notification = self.get_object()
        
        if notification.recipient != request.user:
            return Response(
                {'error': 'You can only mark your own notifications as read'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        notification.is_read = True
        notification.read_at = timezone.now()
        notification.save()
        
        serializer = self.get_serializer(notification)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def mark_as_unread(self, request, pk=None):
        """Mark notification as unread"""
        notification = self.get_object()
        
        if notification.recipient != request.user:
            return Response(
                {'error': 'You can only mark your own notifications as unread'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        notification.is_read = False
        notification.read_at = None
        notification.save()
        
        serializer = self.get_serializer(notification)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def mark_all_as_read(self, request):
        """Mark all user's notifications as read"""
        updated_count = Notification.objects.filter(
            recipient=request.user,
            is_read=False
        ).update(
            is_read=True,
            read_at=timezone.now()
        )
        
        return Response({
            'message': f'Marked {updated_count} notifications as read'
        })
    
    @action(detail=False, methods=['get'])
    def unread_count(self, request):
        """Get count of unread notifications"""
        count = Notification.objects.filter(
            recipient=request.user,
            is_read=False
        ).count()
        
        return Response({'unread_count': count})
    
    @action(detail=False, methods=['post'])
    def send_bulk(self, request):
        """Send bulk notifications"""
        serializer = BulkNotificationSerializer(data=request.data)
        if serializer.is_valid():
            data = serializer.validated_data
            
            # Determine recipients
            recipients = []
            recipient_type = data['recipient_type']
            
            if recipient_type == 'all':
                recipients = User.objects.filter(is_active=True)
            elif recipient_type == 'department':
                from ..models.employees import EmployeeProfile
                recipients = User.objects.filter(
                    employee_profile__department_id__in=data['department_ids']
                )
            elif recipient_type == 'role':
                recipients = User.objects.filter(
                    role__in=data['role_names']
                )
            elif recipient_type == 'custom':
                recipients = User.objects.filter(
                    id__in=data['recipient_ids']
                )
            
            # Create notifications
            notifications_created = []
            for recipient in recipients:
                notification = Notification.objects.create(
                    recipient=recipient,
                    sender=request.user,
                    title=data['title'],
                    message=data['message'],
                    notification_type=data['notification_type'],
                    priority=data['priority']
                )
                notifications_created.append(notification)
            
            return Response({
                'message': f'Sent {len(notifications_created)} notifications',
                'recipients_count': len(notifications_created)
            }, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class EmailLogViewSet(viewsets.ModelViewSet):
    """ViewSet for Email Logs"""
    queryset = EmailLog.objects.all()
    serializer_class = EmailLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['recipient', 'template', 'status']
    search_fields = ['subject', 'recipient_email']
    ordering = ['-sent_at']
    
    @action(detail=False, methods=['get'])
    def delivery_stats(self, request):
        """Get email delivery statistics"""
        from django.db.models import Count
        
        stats = EmailLog.objects.aggregate(
            total_sent=Count('id'),
            delivered=Count('id', filter=Q(status='delivered')),
            failed=Count('id', filter=Q(status='failed')),
            pending=Count('id', filter=Q(status='pending'))
        )
        
        # Calculate delivery rate
        if stats['total_sent'] > 0:
            stats['delivery_rate'] = (stats['delivered'] / stats['total_sent']) * 100
        else:
            stats['delivery_rate'] = 0
        
        return Response(stats)


# Additional views for communication features
class CommunicationViewSet(viewsets.ViewSet):
    """ViewSet for communication-related actions"""
    permission_classes = [permissions.IsAuthenticated]
    
    @action(detail=False, methods=['get', 'post'])
    def preferences(self, request):
        """Get or update user communication preferences"""
        if request.method == 'GET':
            # Get current preferences (you might store these in a UserProfile model)
            # For now, return default preferences
            preferences = {
                'email_notifications': True,
                'sms_notifications': False,
                'push_notifications': True,
                'notification_types': ['system', 'workflow', 'reminder'],
                'quiet_hours_start': None,
                'quiet_hours_end': None,
                'weekend_notifications': False
            }
            return Response(preferences)
        
        elif request.method == 'POST':
            serializer = CommunicationPreferencesSerializer(data=request.data)
            if serializer.is_valid():
                # Save preferences (implement based on your user profile model)
                # For now, just return the validated data
                return Response(serializer.validated_data)
            
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['post'])
    def send_test_notification(self, request):
        """Send a test notification to the current user"""
        notification = Notification.objects.create(
            recipient=request.user,
            sender=request.user,
            title="Test Notification",
            message="This is a test notification to verify your notification settings.",
            notification_type='system',
            priority='low'
        )
        
        serializer = NotificationSerializer(notification)
        return Response({
            'message': 'Test notification sent successfully',
            'notification': serializer.data
        })
    
    @action(detail=False, methods=['get'])
    def notification_summary(self, request):
        """Get notification summary for the current user"""
        user_notifications = Notification.objects.filter(recipient=request.user)
        
        summary = {
            'total_notifications': user_notifications.count(),
            'unread_notifications': user_notifications.filter(is_read=False).count(),
            'notifications_today': user_notifications.filter(
                created_at__date=timezone.now().date()
            ).count(),
            'notifications_this_week': user_notifications.filter(
                created_at__gte=timezone.now() - timezone.timedelta(days=7)
            ).count(),
            'by_type': user_notifications.values('notification_type').annotate(
                count=Count('id')
            ),
            'by_priority': user_notifications.values('priority').annotate(
                count=Count('id')
            )
        }
        
        return Response(summary)
