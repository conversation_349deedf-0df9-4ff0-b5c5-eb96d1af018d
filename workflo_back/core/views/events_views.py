from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Q, Count
from django.utils import timezone

from ..models.events import CompanyEvent, EventParticipant
from ..serializers.events_serializers import CompanyEventSerializer, EventParticipantSerializer


class CompanyEventViewSet(viewsets.ModelViewSet):
    """ViewSet for Company Events with full CRUD operations"""
    queryset = CompanyEvent.objects.all()
    serializer_class = CompanyEventSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['event_type', 'status', 'is_virtual', 'is_mandatory', 'organizer']
    search_fields = ['title', 'description', 'location']
    ordering_fields = ['start_datetime', 'end_datetime', 'created_at']
    ordering = ['-start_datetime']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    def get_queryset(self):
        """Filter events based on user permissions"""
        queryset = super().get_queryset()
        
        # Add filters for upcoming, past, etc.
        filter_type = self.request.query_params.get('filter')
        if filter_type == 'upcoming':
            queryset = queryset.filter(start_datetime__gte=timezone.now())
        elif filter_type == 'past':
            queryset = queryset.filter(end_datetime__lt=timezone.now())
        elif filter_type == 'today':
            today = timezone.now().date()
            queryset = queryset.filter(
                start_datetime__date__lte=today,
                end_datetime__date__gte=today
            )
        
        return queryset
    
    @action(detail=True, methods=['post'])
    def register(self, request, pk=None):
        """Register for an event"""
        event = self.get_object()
        
        # Check if registration is required
        if not event.registration_required:
            return Response(
                {'error': 'Registration is not required for this event'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check registration deadline
        if event.registration_deadline and timezone.now() > event.registration_deadline:
            return Response(
                {'error': 'Registration deadline has passed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if already registered
        existing_registration = EventParticipant.objects.filter(
            event=event, employee=request.user
        ).first()
        
        if existing_registration:
            return Response(
                {'error': 'Already registered for this event'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check capacity
        if event.max_participants and event.current_participants >= event.max_participants:
            return Response(
                {'error': 'Event is at full capacity'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Create registration
        participant_data = {
            'event': event.id,
            'employee': request.user.id,
            'registration_status': 'registered'
        }
        
        serializer = EventParticipantSerializer(data=participant_data)
        if serializer.is_valid():
            serializer.save()
            
            # Update participant count
            event.current_participants += 1
            event.save()
            
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def cancel_registration(self, request, pk=None):
        """Cancel event registration"""
        event = self.get_object()
        
        try:
            participant = EventParticipant.objects.get(
                event=event, employee=request.user
            )
            participant.registration_status = 'cancelled'
            participant.save()
            
            # Update participant count
            event.current_participants = max(0, event.current_participants - 1)
            event.save()
            
            return Response({'message': 'Registration cancelled successfully'})
        
        except EventParticipant.DoesNotExist:
            return Response(
                {'error': 'Not registered for this event'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['get'])
    def participants(self, request, pk=None):
        """Get event participants"""
        event = self.get_object()
        participants = EventParticipant.objects.filter(event=event)
        serializer = EventParticipantSerializer(participants, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def mark_attendance(self, request, pk=None):
        """Mark attendance for event participants"""
        event = self.get_object()
        attendance_data = request.data.get('attendance', [])
        
        updated_count = 0
        for item in attendance_data:
            try:
                participant = EventParticipant.objects.get(
                    event=event,
                    employee_id=item.get('employee_id')
                )
                participant.attendance_status = item.get('attendance_status', 'present')
                participant.save()
                updated_count += 1
            except EventParticipant.DoesNotExist:
                continue
        
        return Response({
            'message': f'Attendance updated for {updated_count} participants'
        })
    
    @action(detail=False, methods=['get'])
    def my_events(self, request):
        """Get events for the current user"""
        # Events where user is a participant
        participant_events = CompanyEvent.objects.filter(
            participants__employee=request.user
        ).distinct()
        
        # Events organized by user
        organized_events = CompanyEvent.objects.filter(
            organizer=request.user
        )
        
        # Combine and remove duplicates
        all_events = (participant_events | organized_events).distinct()
        
        serializer = self.get_serializer(all_events, many=True)
        return Response(serializer.data)


class EventParticipantViewSet(viewsets.ModelViewSet):
    """ViewSet for Event Participants"""
    queryset = EventParticipant.objects.all()
    serializer_class = EventParticipantSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['event', 'employee', 'registration_status', 'attendance_status']
    search_fields = ['employee__first_name', 'employee__last_name']
    ordering = ['-registered_at']
    
    @action(detail=True, methods=['post'])
    def submit_feedback(self, request, pk=None):
        """Submit feedback for an event"""
        participant = self.get_object()
        
        # Check if event has ended
        if participant.event.end_datetime > timezone.now():
            return Response(
                {'error': 'Cannot submit feedback before event ends'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update feedback
        participant.feedback_rating = request.data.get('rating')
        participant.feedback_comments = request.data.get('comments')
        participant.save()
        
        serializer = self.get_serializer(participant)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def my_participations(self, request):
        """Get current user's event participations"""
        participations = self.get_queryset().filter(employee=request.user)
        serializer = self.get_serializer(participations, many=True)
        return Response(serializer.data)
