from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Q, Count, Avg
from django.utils import timezone

from ..models.engagement import (
    EmployeeSurvey, SurveyResponse, RecognitionCategory, EmployeeRecognition,
    WellnessProgram, WellnessProgramEnrollment, WellnessActivityLog,
    EAPResource, EAPAccessLog, EmployeeFeedback
)
from ..models.employees import BankProfile, EmergencyContact
from ..serializers.engagement_serializers import (
    EmployeeSurveySerializer, SurveyResponseSerializer, RecognitionCategorySerializer,
    EmployeeRecognitionSerializer, WellnessProgramSerializer, WellnessProgramEnrollmentSerializer,
    WellnessActivityLogSerializer, EAPResourceSerializer, EAPAccessLogSerializer,
    EmployeeFeedbackSerializer, BankProfileSerializer, EmergencyContactSerializer
)


class EmployeeSurveyViewSet(viewsets.ModelViewSet):
    """ViewSet for Employee Surveys with full CRUD operations"""
    queryset = EmployeeSurvey.objects.all()
    serializer_class = EmployeeSurveySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['survey_type', 'status', 'is_anonymous']
    search_fields = ['title', 'description']
    ordering_fields = ['created_at', 'start_date', 'end_date']
    ordering = ['-created_at']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def submit_response(self, request, pk=None):
        """Submit a response to a survey"""
        survey = self.get_object()
        
        # Check if survey is active
        if survey.status != 'active':
            return Response(
                {'error': 'Survey is not active'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if user already responded (unless anonymous)
        if not survey.is_anonymous:
            existing_response = SurveyResponse.objects.filter(
                survey=survey, employee=request.user
            ).first()
            if existing_response:
                return Response(
                    {'error': 'You have already responded to this survey'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        # Create response
        response_data = {
            'survey': survey.id,
            'employee': request.user.id if not survey.is_anonymous else None,
            'responses': request.data.get('responses', {}),
            'completion_time_seconds': request.data.get('completion_time_seconds'),
            'ip_address': request.META.get('REMOTE_ADDR')
        }
        
        serializer = SurveyResponseSerializer(data=response_data)
        if serializer.is_valid():
            serializer.save()
            
            # Update survey response count
            survey.response_count += 1
            survey.save()
            
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['get'])
    def responses(self, request, pk=None):
        """Get all responses for a survey"""
        survey = self.get_object()
        responses = SurveyResponse.objects.filter(survey=survey)
        serializer = SurveyResponseSerializer(responses, many=True)
        return Response(serializer.data)


class SurveyResponseViewSet(viewsets.ModelViewSet):
    """ViewSet for Survey Responses"""
    queryset = SurveyResponse.objects.all()
    serializer_class = SurveyResponseSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['survey', 'employee']
    ordering = ['-submitted_at']


class RecognitionCategoryViewSet(viewsets.ModelViewSet):
    """ViewSet for Recognition Categories"""
    queryset = RecognitionCategory.objects.all()
    serializer_class = RecognitionCategorySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [SearchFilter, OrderingFilter]
    search_fields = ['name', 'description']
    ordering = ['name']


class EmployeeRecognitionViewSet(viewsets.ModelViewSet):
    """ViewSet for Employee Recognition"""
    queryset = EmployeeRecognition.objects.all()
    serializer_class = EmployeeRecognitionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['recipient', 'nominator', 'category', 'recognition_type', 'status']
    search_fields = ['title', 'description']
    ordering = ['-created_at']
    
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve a recognition"""
        recognition = self.get_object()
        recognition.status = 'approved'
        recognition.approved_by = request.user
        recognition.approved_at = timezone.now()
        recognition.save()
        
        serializer = self.get_serializer(recognition)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """Reject a recognition"""
        recognition = self.get_object()
        recognition.status = 'rejected'
        recognition.approved_by = request.user
        recognition.approved_at = timezone.now()
        recognition.save()
        
        serializer = self.get_serializer(recognition)
        return Response(serializer.data)


class WellnessProgramViewSet(viewsets.ModelViewSet):
    """ViewSet for Wellness Programs"""
    queryset = WellnessProgram.objects.all()
    serializer_class = WellnessProgramSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['program_type', 'status', 'is_team_based']
    search_fields = ['title', 'description']
    ordering = ['-created_at']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def enroll(self, request, pk=None):
        """Enroll in a wellness program"""
        program = self.get_object()
        
        # Check if already enrolled
        existing_enrollment = WellnessProgramEnrollment.objects.filter(
            program=program, employee=request.user
        ).first()
        
        if existing_enrollment:
            return Response(
                {'error': 'Already enrolled in this program'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check capacity
        if program.max_participants and program.current_participants >= program.max_participants:
            return Response(
                {'error': 'Program is at full capacity'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Create enrollment
        enrollment_data = {
            'program': program.id,
            'employee': request.user.id,
            'team_name': request.data.get('team_name')
        }
        
        serializer = WellnessProgramEnrollmentSerializer(data=enrollment_data)
        if serializer.is_valid():
            serializer.save()
            
            # Update participant count
            program.current_participants += 1
            program.save()
            
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class WellnessProgramEnrollmentViewSet(viewsets.ModelViewSet):
    """ViewSet for Wellness Program Enrollments"""
    queryset = WellnessProgramEnrollment.objects.all()
    serializer_class = WellnessProgramEnrollmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['program', 'employee', 'goal_achieved']
    ordering = ['-joined_at']


class WellnessActivityLogViewSet(viewsets.ModelViewSet):
    """ViewSet for Wellness Activity Logs"""
    queryset = WellnessActivityLog.objects.all()
    serializer_class = WellnessActivityLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['participant', 'activity_date', 'data_source']
    ordering = ['-activity_date']


class EAPResourceViewSet(viewsets.ModelViewSet):
    """ViewSet for EAP Resources"""
    queryset = EAPResource.objects.filter(is_active=True)
    serializer_class = EAPResourceSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['resource_type', 'category', 'is_confidential']
    search_fields = ['title', 'description']
    ordering = ['title']
    
    @action(detail=True, methods=['post'])
    def access(self, request, pk=None):
        """Log access to an EAP resource"""
        resource = self.get_object()
        
        # Create access log
        access_log_data = {
            'resource': resource.id,
            'employee': request.user.id,
            'access_type': request.data.get('access_type', 'view'),
            'ip_address': request.META.get('REMOTE_ADDR'),
            'user_agent': request.META.get('HTTP_USER_AGENT')
        }
        
        serializer = EAPAccessLogSerializer(data=access_log_data)
        if serializer.is_valid():
            serializer.save()
            
            # Update access count
            resource.access_count += 1
            resource.save()
            
            return Response({'message': 'Access logged successfully'})
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class EmployeeFeedbackViewSet(viewsets.ModelViewSet):
    """ViewSet for Employee Feedback"""
    queryset = EmployeeFeedback.objects.all()
    serializer_class = EmployeeFeedbackSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['feedback_type', 'status', 'is_anonymous']
    search_fields = ['subject', 'message']
    ordering = ['-created_at']
    
    def perform_create(self, serializer):
        serializer.save(employee=self.request.user)


# Bank Profile and Emergency Contact ViewSets
class BankProfileViewSet(viewsets.ModelViewSet):
    """ViewSet for Bank Profiles with full CRUD operations"""
    queryset = BankProfile.objects.all()
    serializer_class = BankProfileSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['employee', 'bank_name', 'is_primary', 'account_type']  # Temporarily removed 'is_active'
    search_fields = ['account_number', 'account_name', 'bank_name', 'branch_name']
    ordering = ['-created_at']

    def get_queryset(self):
        """Filter queryset based on user permissions"""
        queryset = super().get_queryset()
        user = self.request.user

        # Admin and HR can see all bank profiles
        if user.role in ['admin', 'hr']:
            return queryset
        # Employees can only see their own bank profiles
        elif user.role == 'employee':
            return queryset.filter(employee=user)
        # Supervisors can see their department employees' bank profiles
        elif user.role == 'supervisor':
            return queryset.filter(employee__department__supervisor=user)

        return queryset.none()

    def perform_create(self, serializer):
        """Set created_by when creating bank profile"""
        serializer.save(created_by=self.request.user)

    def perform_update(self, serializer):
        """Set updated_by when updating bank profile"""
        serializer.save(updated_by=self.request.user)

    @action(detail=False, methods=['get'])
    def my_accounts(self, request):
        """Get current user's bank accounts"""
        accounts = self.get_queryset().filter(employee=request.user)
        serializer = self.get_serializer(accounts, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def set_primary(self, request, pk=None):
        """Set bank account as primary"""
        bank_profile = self.get_object()

        # Remove primary flag from other accounts
        BankProfile.objects.filter(
            employee=bank_profile.employee,
            is_primary=True
        ).update(is_primary=False)

        # Set this account as primary
        bank_profile.is_primary = True
        bank_profile.updated_by = request.user
        bank_profile.save()

        return Response({
            'message': 'Bank account set as primary successfully',
            'account': self.get_serializer(bank_profile).data
        })

    @action(detail=False, methods=['get'])
    def employee_accounts(self, request):
        """Get bank accounts for a specific employee (admin/HR only)"""
        if request.user.role not in ['admin', 'hr']:
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        employee_id = request.query_params.get('employee_id')
        if not employee_id:
            return Response({
                'error': 'employee_id parameter is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        accounts = self.get_queryset().filter(employee_id=employee_id)
        serializer = self.get_serializer(accounts, many=True)
        return Response(serializer.data)


class EmergencyContactViewSet(viewsets.ModelViewSet):
    """ViewSet for Emergency Contacts with full CRUD operations"""
    queryset = EmergencyContact.objects.all()
    serializer_class = EmergencyContactSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['employee', 'relationship']  # Temporarily removed 'is_active'
    search_fields = ['contact_name', 'phone_number', 'email', 'relationship']
    ordering = ['priority_order']

    def get_queryset(self):
        """Filter queryset based on user permissions"""
        queryset = super().get_queryset()
        user = self.request.user

        # Admin and HR can see all emergency contacts
        if user.role in ['admin', 'hr']:
            return queryset
        # Employees can only see their own emergency contacts
        elif user.role == 'employee':
            return queryset.filter(employee=user)
        # Supervisors can see their department employees' emergency contacts
        elif user.role == 'supervisor':
            return queryset.filter(employee__department__supervisor=user)

        return queryset.none()

    def perform_create(self, serializer):
        """Set created_by when creating emergency contact"""
        serializer.save(created_by=self.request.user)

    def perform_update(self, serializer):
        """Set updated_by when updating emergency contact"""
        serializer.save(updated_by=self.request.user)

    @action(detail=False, methods=['get'])
    def my_contacts(self, request):
        """Get current user's emergency contacts"""
        contacts = self.get_queryset().filter(employee=request.user)
        serializer = self.get_serializer(contacts, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def update_priority(self, request, pk=None):
        """Update priority order of emergency contact"""
        contact = self.get_object()
        new_priority = request.data.get('priority_order')

        if not new_priority or new_priority < 1:
            return Response({
                'error': 'Valid priority_order is required (minimum 1)'
            }, status=status.HTTP_400_BAD_REQUEST)

        contact.priority_order = new_priority
        contact.updated_by = request.user
        contact.save()

        return Response({
            'message': 'Priority updated successfully',
            'contact': self.get_serializer(contact).data
        })

    @action(detail=False, methods=['get'])
    def employee_contacts(self, request):
        """Get emergency contacts for a specific employee (admin/HR only)"""
        if request.user.role not in ['admin', 'hr']:
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        employee_id = request.query_params.get('employee_id')
        if not employee_id:
            return Response({
                'error': 'employee_id parameter is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        contacts = self.get_queryset().filter(employee_id=employee_id)
        serializer = self.get_serializer(contacts, many=True)
        return Response(serializer.data)
