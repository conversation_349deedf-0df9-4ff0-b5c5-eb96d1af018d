"""
Attendance & Time Tracking Views
Enhanced views with BioStar integration and dynamic salary calculation
"""

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from datetime import datetime, timedelta, date
from django.db.models import Q, Sum, Count
from ..models.attendance import AttendanceRecord, BiostarEvent, BiostarDevice
from ..models.overtime import OvertimeRequest, OvertimeRecord
# Temporarily comment out serializers until they are created
# from ..serializers.attendance_serializers import (
#     AttendanceRecordSerializer, BiostarEventSerializer, BiostarDeviceSerializer
# )
from ..services.biostar_api import biostar_api
from ..services.biostar_sync import biostar_sync
from ..services.salary_calculator import salary_calculator


class AttendanceRecordViewSet(viewsets.ModelViewSet):
    """
    Enhanced AttendanceRecord ViewSet with BioStar integration and salary calculation
    """
    queryset = AttendanceRecord.objects.all()
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter based on user role and permissions"""
        user = self.request.user
        queryset = self.queryset.select_related('employee')

        # Check if user is authenticated and has role attribute
        if not user.is_authenticated or not hasattr(user, 'role'):
            return queryset.none()

        # Business logic: Filter based on user role
        if user.role == 'employee':
            # Employees can only see their own attendance
            queryset = queryset.filter(employee=user)
        elif user.role == 'supervisor':
            # Supervisors can see their department's attendance
            if hasattr(user, 'employee_profile'):
                department = user.employee_profile.department
                queryset = queryset.filter(employee__employee_profile__department=department)
        # Admin, HR can see all attendance records

        return queryset

    @action(detail=False, methods=['post'])
    def sync_biostar(self, request):
        """Sync attendance data from BioStar"""
        try:
            # Get date range from request
            start_date = request.data.get('start_date')
            end_date = request.data.get('end_date')

            if start_date:
                start_date = datetime.strptime(start_date, '%Y-%m-%d')
            else:
                start_date = timezone.now() - timedelta(days=1)

            if end_date:
                end_date = datetime.strptime(end_date, '%Y-%m-%d')
            else:
                end_date = timezone.now()

            # Perform synchronization
            result = biostar_sync.sync_events(start_date, end_date)

            return Response({
                'success': True,
                'message': 'BioStar synchronization completed',
                'data': result
            })

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Synchronization failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def attendance_summary(self, request):
        """Get attendance summary for a period"""
        try:
            # Get parameters
            employee_id = request.query_params.get('employee_id')
            start_date = request.query_params.get('start_date')
            end_date = request.query_params.get('end_date')

            # Default to current month if no dates provided
            if not start_date or not end_date:
                today = date.today()
                start_date = today.replace(day=1)
                end_date = today
            else:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

            # Filter queryset
            queryset = self.get_queryset().filter(date__range=[start_date, end_date])

            if employee_id:
                queryset = queryset.filter(employee_id=employee_id)

            # Calculate summary statistics
            summary = queryset.aggregate(
                total_days=Count('id'),
                total_hours=Sum('total_hours'),
                total_regular_hours=Sum('regular_hours'),
                total_overtime_hours=Sum('overtime_hours')
            )

            # Calculate attendance rate
            working_days = self._calculate_working_days(start_date, end_date)
            attendance_rate = (summary['total_days'] / working_days * 100) if working_days > 0 else 0

            return Response({
                'period': {
                    'start_date': start_date,
                    'end_date': end_date,
                    'working_days': working_days
                },
                'summary': {
                    **summary,
                    'attendance_rate': round(attendance_rate, 2)
                }
            })

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def calculate_salary(self, request):
        """Calculate dynamic salary based on attendance"""
        try:
            employee_id = request.data.get('employee_id')
            start_date = request.data.get('start_date')
            end_date = request.data.get('end_date')

            if not all([employee_id, start_date, end_date]):
                return Response({
                    'error': 'employee_id, start_date, and end_date are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Convert dates
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

            # Get employee
            from ..models.auth import User
            employee = User.objects.get(id=employee_id)

            # Calculate salary
            calculation = salary_calculator.calculate_employee_salary(employee, start_date, end_date)

            return Response({
                'success': True,
                'calculation': calculation
            })

        except User.DoesNotExist:
            return Response({
                'error': 'Employee not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _calculate_working_days(self, start_date: date, end_date: date) -> int:
        """Calculate working days in a period (excluding weekends and holidays)"""
        working_days = 0
        current_date = start_date

        # Get holidays
        from ..models.leave import CompanyHoliday
        holidays = set(CompanyHoliday.objects.filter(
            date__range=[start_date, end_date]
        ).values_list('date', flat=True))

        while current_date <= end_date:
            if current_date.weekday() < 5 and current_date not in holidays:
                working_days += 1
            current_date += timedelta(days=1)

        return working_days


class BiostarEventViewSet(viewsets.ModelViewSet):
    """
    Enhanced BiostarEvent ViewSet with real-time synchronization
    """
    queryset = BiostarEvent.objects.all()
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter events based on user permissions"""
        user = self.request.user
        queryset = self.queryset.select_related('employee', 'attendance_record')

        if user.role == 'employee':
            queryset = queryset.filter(employee=user)
        elif user.role == 'supervisor':
            if hasattr(user, 'employee_profile'):
                department = user.employee_profile.department
                queryset = queryset.filter(employee__employee_profile__department=department)

        return queryset

    @action(detail=False, methods=['get'])
    def realtime_events(self, request):
        """Get real-time events from BioStar"""
        try:
            result = biostar_sync.sync_realtime_events()
            return Response({
                'success': True,
                'data': result
            })
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def unprocessed_events(self, request):
        """Get unprocessed BioStar events"""
        unprocessed = self.get_queryset().filter(processed=False)
        return Response({
            'count': unprocessed.count(),
            'events': [{'id': e.id, 'event_type': e.event_type, 'processed': e.processed} for e in unprocessed[:50]]
        })

    @action(detail=True, methods=['post'])
    def process_event(self, request, pk=None):
        """Manually process a BioStar event"""
        try:
            event = self.get_object()
            if event.processed:
                return Response({
                    'error': 'Event already processed'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Process the event
            result = biostar_sync._process_attendance_event(event)

            return Response({
                'success': True,
                'message': 'Event processed successfully',
                'result': result
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class BiostarDeviceViewSet(viewsets.ModelViewSet):
    """
    Enhanced BiostarDevice ViewSet with connection testing
    """
    queryset = BiostarDevice.objects.all()
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'])
    def connection_status(self, request):
        """Test BioStar API connection and get device status"""
        try:
            # Test API connection
            connection_test = biostar_api.test_connection()

            # Get device status
            devices = self.get_queryset()
            device_status = []

            for device in devices:
                device_status.append({
                    'id': device.id,
                    'name': device.name,
                    'status': device.status,
                    'last_seen': device.last_seen,
                    'location': device.location
                })

            return Response({
                'api_connection': connection_test,
                'devices': device_status
            })

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def sync_devices(self, request):
        """Sync devices from BioStar"""
        try:
            result = biostar_sync.sync_devices()
            return Response({
                'success': True,
                'message': 'Device synchronization completed',
                'data': result
            })
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
