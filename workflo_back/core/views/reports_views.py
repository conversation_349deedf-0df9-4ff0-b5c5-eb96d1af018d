from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from django.db.models import Q, Count, Sum
from django.utils import timezone
from django.http import HttpResponse
import json
import csv
import io

from ..models.reports import SavedReport, ReportHistory
from ..serializers.reports_serializers import SavedReportSerializer, ReportHistorySerializer


class SavedReportViewSet(viewsets.ModelViewSet):
    """ViewSet for Saved Reports"""
    queryset = SavedReport.objects.all()
    serializer_class = SavedReportSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['report_type', 'schedule_frequency']  # Temporarily removed 'is_active'
    search_fields = ['name', 'description']
    ordering = ['-created_at']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def generate(self, request, pk=None):
        """Generate a report"""
        saved_report = self.get_object()
        
        # Start report generation
        report_history = ReportHistory.objects.create(
            saved_report=saved_report,
            generated_by=request.user,
            parameters_used=saved_report.parameters,
            status='generating'
        )
        
        try:
            # Generate report based on type
            report_data = self._generate_report_data(saved_report)
            
            # Save report file (simplified - in production, you'd save to storage)
            file_path = f"reports/{saved_report.name}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            # Update report history
            report_history.status = 'completed'
            report_history.file_path = file_path
            report_history.file_size = len(json.dumps(report_data))
            report_history.generation_time_seconds = 5  # Simplified
            report_history.save()
            
            # Update saved report
            saved_report.last_generated = timezone.now()
            saved_report.save()
            
            return Response({
                'message': 'Report generated successfully',
                'report_history_id': report_history.id,
                'data': report_data
            })
        
        except Exception as e:
            report_history.status = 'failed'
            report_history.error_message = str(e)
            report_history.save()
            
            return Response({
                'error': f'Report generation failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _generate_report_data(self, saved_report):
        """Generate report data based on report type"""
        report_type = saved_report.report_type
        parameters = saved_report.parameters or {}
        
        if report_type == 'payroll':
            return self._generate_payroll_report(parameters)
        elif report_type == 'attendance':
            return self._generate_attendance_report(parameters)
        elif report_type == 'leave':
            return self._generate_leave_report(parameters)
        elif report_type == 'performance':
            return self._generate_performance_report(parameters)
        else:
            return {'message': 'Custom report data would be generated here'}
    
    def _generate_payroll_report(self, parameters):
        """Generate payroll report"""
        from ..models.payroll import PayrollRecord
        
        # Get date range from parameters
        start_date = parameters.get('start_date')
        end_date = parameters.get('end_date')
        
        queryset = PayrollRecord.objects.all()
        if start_date:
            queryset = queryset.filter(pay_period_start__gte=start_date)
        if end_date:
            queryset = queryset.filter(pay_period_end__lte=end_date)
        
        # Generate summary data
        summary = queryset.aggregate(
            total_gross_pay=Sum('gross_pay'),
            total_net_pay=Sum('net_pay'),
            total_deductions=Sum('total_deductions'),
            record_count=Count('id')
        )
        
        return {
            'report_type': 'payroll',
            'summary': summary,
            'records_count': summary['record_count'],
            'generated_at': timezone.now().isoformat()
        }
    
    def _generate_attendance_report(self, parameters):
        """Generate attendance report"""
        from ..models.attendance import AttendanceRecord
        
        start_date = parameters.get('start_date')
        end_date = parameters.get('end_date')
        
        queryset = AttendanceRecord.objects.all()
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date__lte=end_date)
        
        summary = {
            'total_records': queryset.count(),
            'present_count': queryset.filter(status='present').count(),
            'absent_count': queryset.filter(status='absent').count(),
            'late_count': queryset.filter(status='late').count()
        }
        
        return {
            'report_type': 'attendance',
            'summary': summary,
            'generated_at': timezone.now().isoformat()
        }
    
    def _generate_leave_report(self, parameters):
        """Generate leave report"""
        from ..models.leave import LeaveApplication
        
        start_date = parameters.get('start_date')
        end_date = parameters.get('end_date')
        
        queryset = LeaveApplication.objects.all()
        if start_date:
            queryset = queryset.filter(start_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(end_date__lte=end_date)
        
        summary = queryset.aggregate(
            total_applications=Count('id'),
            approved_applications=Count('id', filter=Q(status='approved')),
            pending_applications=Count('id', filter=Q(status='pending')),
            rejected_applications=Count('id', filter=Q(status='rejected'))
        )
        
        return {
            'report_type': 'leave',
            'summary': summary,
            'generated_at': timezone.now().isoformat()
        }
    
    def _generate_performance_report(self, parameters):
        """Generate performance report"""
        from ..models.performance import PerformanceReview
        
        summary = PerformanceReview.objects.aggregate(
            total_reviews=Count('id'),
            completed_reviews=Count('id', filter=Q(status='completed')),
            pending_reviews=Count('id', filter=Q(status='pending'))
        )
        
        return {
            'report_type': 'performance',
            'summary': summary,
            'generated_at': timezone.now().isoformat()
        }
    
    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """Download report as CSV"""
        saved_report = self.get_object()
        
        # Generate report data
        report_data = self._generate_report_data(saved_report)
        
        # Create CSV response
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="{saved_report.name}.csv"'
        
        writer = csv.writer(response)
        
        # Write headers and data based on report type
        if saved_report.report_type == 'payroll':
            writer.writerow(['Metric', 'Value'])
            summary = report_data.get('summary', {})
            for key, value in summary.items():
                writer.writerow([key.replace('_', ' ').title(), value])
        else:
            # Generic format for other report types
            writer.writerow(['Report Type', saved_report.report_type])
            writer.writerow(['Generated At', report_data.get('generated_at', '')])
            
            summary = report_data.get('summary', {})
            writer.writerow(['Metric', 'Value'])
            for key, value in summary.items():
                writer.writerow([key.replace('_', ' ').title(), value])
        
        return response


class ReportHistoryViewSet(viewsets.ModelViewSet):
    """ViewSet for Report History"""
    queryset = ReportHistory.objects.all()
    serializer_class = ReportHistorySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['saved_report', 'generated_by', 'status']
    search_fields = ['saved_report__name']
    ordering = ['-created_at']
    
    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """Download generated report file"""
        report_history = self.get_object()
        
        if not report_history.file_path or report_history.status != 'completed':
            return Response({
                'error': 'Report file not available'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # In a real implementation, you would read the file from storage
        # For now, return a JSON response
        return Response({
            'message': 'Report download would be implemented here',
            'file_path': report_history.file_path,
            'file_size': report_history.file_size
        })
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get report generation statistics"""
        history = self.get_queryset()
        
        stats = {
            'total_reports_generated': history.count(),
            'successful_reports': history.filter(status='completed').count(),
            'failed_reports': history.filter(status='failed').count(),
            'reports_today': history.filter(
                created_at__date=timezone.now().date()
            ).count(),
            'reports_this_week': history.filter(
                created_at__gte=timezone.now() - timezone.timedelta(days=7)
            ).count(),
            'by_type': history.values('saved_report__report_type').annotate(
                count=Count('id')
            ).order_by('-count'),
            'by_user': history.values(
                'generated_by__first_name', 
                'generated_by__last_name'
            ).annotate(
                count=Count('id')
            ).order_by('-count')[:10]
        }
        
        return Response(stats)
