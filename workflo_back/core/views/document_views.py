from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.http import HttpResponse, Http404
from django.core.files.storage import default_storage
from django.utils import timezone
import os
import mimetypes

from ..models.documents import (
    DocumentCategory, EmployeeDocument, CompanyDocument, DocumentAcknowledgment
)
from ..serializers.document_serializers import (
    DocumentCategorySerializer, EmployeeDocumentSerializer, CompanyDocumentSerializer,
    DocumentAcknowledgmentSerializer, BulkDocumentUploadSerializer
)


class DocumentCategoryViewSet(viewsets.ModelViewSet):
    """ViewSet for Document Categories"""
    queryset = DocumentCategory.objects.all()
    serializer_class = DocumentCategorySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [SearchFilter, OrderingFilter]
    search_fields = ['name', 'description']
    ordering = ['name']


class EmployeeDocumentViewSet(viewsets.ModelViewSet):
    """ViewSet for Employee Documents with media capabilities"""
    queryset = EmployeeDocument.objects.all()
    serializer_class = EmployeeDocumentSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['employee', 'category', 'is_confidential', 'uploaded_by']
    search_fields = ['title', 'description']
    ordering = ['-created_at']
    
    def perform_create(self, serializer):
        # Handle file upload
        file = self.request.FILES.get('file_path')
        if file:
            # Save file with unique name
            file_name = f"employee_docs/{timezone.now().strftime('%Y/%m/%d')}/{file.name}"
            file_path = default_storage.save(file_name, file)
            serializer.save(
                uploaded_by=self.request.user,
                file_path=file_path,
                file_size=file.size
            )
        else:
            serializer.save(uploaded_by=self.request.user)
    
    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """Download document file"""
        document = self.get_object()
        
        if not document.file_path:
            raise Http404("File not found")
        
        try:
            file_path = document.file_path
            if default_storage.exists(file_path):
                file_content = default_storage.open(file_path).read()
                
                # Determine content type
                content_type, _ = mimetypes.guess_type(file_path)
                if not content_type:
                    content_type = 'application/octet-stream'
                
                response = HttpResponse(file_content, content_type=content_type)
                response['Content-Disposition'] = f'attachment; filename="{os.path.basename(file_path)}"'
                response['Content-Length'] = len(file_content)
                
                return response
            else:
                raise Http404("File not found")
        
        except Exception as e:
            return Response(
                {'error': f'Error downloading file: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['get'])
    def preview(self, request, pk=None):
        """Preview document (for images and PDFs)"""
        document = self.get_object()
        
        if not document.file_path:
            raise Http404("File not found")
        
        try:
            file_path = document.file_path
            file_extension = os.path.splitext(file_path)[1].lower()
            
            # Only allow preview for certain file types
            previewable_types = ['.jpg', '.jpeg', '.png', '.gif', '.pdf']
            if file_extension not in previewable_types:
                return Response(
                    {'error': 'File type not previewable'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            if default_storage.exists(file_path):
                file_content = default_storage.open(file_path).read()
                
                content_type, _ = mimetypes.guess_type(file_path)
                if not content_type:
                    content_type = 'application/octet-stream'
                
                response = HttpResponse(file_content, content_type=content_type)
                response['Content-Length'] = len(file_content)
                
                return response
            else:
                raise Http404("File not found")
        
        except Exception as e:
            return Response(
                {'error': f'Error previewing file: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['post'])
    def bulk_upload(self, request):
        """Bulk upload multiple documents"""
        serializer = BulkDocumentUploadSerializer(data=request.data)
        if serializer.is_valid():
            files = serializer.validated_data['files']
            category = serializer.validated_data['category']
            employee = serializer.validated_data.get('employee')
            is_confidential = serializer.validated_data.get('is_confidential', False)
            
            created_documents = []
            
            for file in files:
                # Save file
                file_name = f"employee_docs/{timezone.now().strftime('%Y/%m/%d')}/{file.name}"
                file_path = default_storage.save(file_name, file)
                
                # Create document record
                document = EmployeeDocument.objects.create(
                    title=os.path.splitext(file.name)[0],
                    category=category,
                    employee=employee,
                    file_path=file_path,
                    file_size=file.size,
                    is_confidential=is_confidential,
                    uploaded_by=request.user
                )
                created_documents.append(document)
            
            # Serialize and return created documents
            response_serializer = EmployeeDocumentSerializer(created_documents, many=True)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CompanyDocumentViewSet(viewsets.ModelViewSet):
    """ViewSet for Company Documents with media capabilities"""
    queryset = CompanyDocument.objects.all()
    serializer_class = CompanyDocumentSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['category', 'is_confidential', 'requires_acknowledgment', 'uploaded_by']
    search_fields = ['title', 'description']
    ordering = ['-created_at']
    
    def perform_create(self, serializer):
        # Handle file upload
        file = self.request.FILES.get('file_path')
        if file:
            # Save file with unique name
            file_name = f"company_docs/{timezone.now().strftime('%Y/%m/%d')}/{file.name}"
            file_path = default_storage.save(file_name, file)
            serializer.save(
                uploaded_by=self.request.user,
                file_path=file_path,
                file_size=file.size
            )
        else:
            serializer.save(uploaded_by=self.request.user)
    
    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """Download company document file"""
        document = self.get_object()
        
        if not document.file_path:
            raise Http404("File not found")
        
        try:
            file_path = document.file_path
            if default_storage.exists(file_path):
                file_content = default_storage.open(file_path).read()
                
                # Log access if required
                if document.requires_acknowledgment:
                    DocumentAcknowledgment.objects.get_or_create(
                        document=document,
                        employee=request.user,
                        defaults={'acknowledged_at': timezone.now()}
                    )
                
                # Determine content type
                content_type, _ = mimetypes.guess_type(file_path)
                if not content_type:
                    content_type = 'application/octet-stream'
                
                response = HttpResponse(file_content, content_type=content_type)
                response['Content-Disposition'] = f'attachment; filename="{os.path.basename(file_path)}"'
                response['Content-Length'] = len(file_content)
                
                return response
            else:
                raise Http404("File not found")
        
        except Exception as e:
            return Response(
                {'error': f'Error downloading file: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def acknowledge(self, request, pk=None):
        """Acknowledge reading a company document"""
        document = self.get_object()
        
        acknowledgment, created = DocumentAcknowledgment.objects.get_or_create(
            document=document,
            employee=request.user,
            defaults={'acknowledged_at': timezone.now()}
        )
        
        if created:
            message = "Document acknowledged successfully"
        else:
            message = "Document already acknowledged"
        
        serializer = DocumentAcknowledgmentSerializer(acknowledgment)
        return Response({
            'message': message,
            'acknowledgment': serializer.data
        })
    
    @action(detail=True, methods=['get'])
    def acknowledgments(self, request, pk=None):
        """Get all acknowledgments for a document"""
        document = self.get_object()
        acknowledgments = DocumentAcknowledgment.objects.filter(document=document)
        serializer = DocumentAcknowledgmentSerializer(acknowledgments, many=True)
        return Response(serializer.data)


class DocumentAcknowledgmentViewSet(viewsets.ModelViewSet):
    """ViewSet for Document Acknowledgments"""
    queryset = DocumentAcknowledgment.objects.all()
    serializer_class = DocumentAcknowledgmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['document', 'employee']
    ordering = ['-acknowledged_at']
