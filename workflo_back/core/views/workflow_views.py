from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Q, Count
from django.utils import timezone

from ..models.workflow import (
    WorkflowDefinition, WorkflowInstance, WorkflowStepExecution, AutomatedReminder
)
from ..serializers.workflow_serializers import (
    WorkflowDefinitionSerializer, WorkflowInstanceSerializer,
    WorkflowStepExecutionSerializer, AutomatedReminderSerializer
)


class WorkflowDefinitionViewSet(viewsets.ModelViewSet):
    """ViewSet for Workflow Definitions"""
    queryset = WorkflowDefinition.objects.all()
    serializer_class = WorkflowDefinitionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['workflow_type']  # Temporarily removed 'is_active'
    search_fields = ['name', 'description']
    ordering = ['name']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate a workflow definition"""
        workflow = self.get_object()
        workflow.is_active = True
        workflow.save()
        
        serializer = self.get_serializer(workflow)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """Deactivate a workflow definition"""
        workflow = self.get_object()
        workflow.is_active = False
        workflow.save()
        
        serializer = self.get_serializer(workflow)
        return Response(serializer.data)


class WorkflowInstanceViewSet(viewsets.ModelViewSet):
    """ViewSet for Workflow Instances"""
    queryset = WorkflowInstance.objects.all()
    serializer_class = WorkflowInstanceSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['workflow', 'status', 'priority', 'initiated_by', 'current_assignee']
    search_fields = ['subject', 'description']
    ordering = ['-created_at']
    
    def perform_create(self, serializer):
        serializer.save(initiated_by=self.request.user)
    
    def get_queryset(self):
        """Filter workflows based on user permissions"""
        queryset = super().get_queryset()
        
        # Filter by assignment
        filter_type = self.request.query_params.get('filter')
        if filter_type == 'assigned_to_me':
            queryset = queryset.filter(current_assignee=self.request.user)
        elif filter_type == 'initiated_by_me':
            queryset = queryset.filter(initiated_by=self.request.user)
        elif filter_type == 'pending':
            queryset = queryset.filter(status='pending')
        elif filter_type == 'in_progress':
            queryset = queryset.filter(status='in_progress')
        
        return queryset
    
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve a workflow step"""
        workflow_instance = self.get_object()
        
        # Check if user is assigned to current step
        if workflow_instance.current_assignee != request.user:
            return Response(
                {'error': 'You are not assigned to this workflow step'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Create step execution record
        step_execution = WorkflowStepExecution.objects.create(
            workflow_instance=workflow_instance,
            step_number=workflow_instance.current_step,
            step_name=f"Step {workflow_instance.current_step}",
            assigned_to=request.user,
            action_type='approval',
            status='completed',
            action_taken='approved',
            comments=request.data.get('comments', ''),
            completed_at=timezone.now()
        )
        
        # Move to next step or complete workflow
        workflow_instance.current_step += 1
        
        # Check if workflow is complete
        if workflow_instance.current_step > workflow_instance.total_steps:
            workflow_instance.status = 'completed'
            workflow_instance.completed_at = timezone.now()
            workflow_instance.current_assignee = None
        else:
            # Assign to next approver (simplified logic)
            next_assignee = workflow_instance.get_next_assignee()
            workflow_instance.current_assignee = next_assignee
        
        workflow_instance.save()
        
        serializer = self.get_serializer(workflow_instance)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """Reject a workflow step"""
        workflow_instance = self.get_object()
        
        # Check if user is assigned to current step
        if workflow_instance.current_assignee != request.user:
            return Response(
                {'error': 'You are not assigned to this workflow step'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Create step execution record
        step_execution = WorkflowStepExecution.objects.create(
            workflow_instance=workflow_instance,
            step_number=workflow_instance.current_step,
            step_name=f"Step {workflow_instance.current_step}",
            assigned_to=request.user,
            action_type='approval',
            status='completed',
            action_taken='rejected',
            comments=request.data.get('comments', ''),
            completed_at=timezone.now()
        )
        
        # Reject workflow
        workflow_instance.status = 'rejected'
        workflow_instance.completed_at = timezone.now()
        workflow_instance.current_assignee = None
        workflow_instance.save()
        
        serializer = self.get_serializer(workflow_instance)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def escalate(self, request, pk=None):
        """Escalate a workflow step"""
        workflow_instance = self.get_object()
        escalate_to_id = request.data.get('escalate_to')
        
        if not escalate_to_id:
            return Response(
                {'error': 'escalate_to user ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Create step execution record
        step_execution = WorkflowStepExecution.objects.create(
            workflow_instance=workflow_instance,
            step_number=workflow_instance.current_step,
            step_name=f"Step {workflow_instance.current_step}",
            assigned_to=request.user,
            action_type='approval',
            status='completed',
            action_taken='escalated',
            comments=request.data.get('comments', ''),
            escalated_to_id=escalate_to_id,
            escalated_at=timezone.now(),
            completed_at=timezone.now()
        )
        
        # Update workflow assignment
        workflow_instance.current_assignee_id = escalate_to_id
        workflow_instance.save()
        
        serializer = self.get_serializer(workflow_instance)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def history(self, request, pk=None):
        """Get workflow execution history"""
        workflow_instance = self.get_object()
        step_executions = WorkflowStepExecution.objects.filter(
            workflow_instance=workflow_instance
        ).order_by('step_number')
        
        serializer = WorkflowStepExecutionSerializer(step_executions, many=True)
        return Response(serializer.data)


class WorkflowStepExecutionViewSet(viewsets.ModelViewSet):
    """ViewSet for Workflow Step Executions"""
    queryset = WorkflowStepExecution.objects.all()
    serializer_class = WorkflowStepExecutionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['workflow_instance', 'assigned_to', 'action_type', 'status', 'action_taken']
    ordering = ['workflow_instance', 'step_number']


class AutomatedReminderViewSet(viewsets.ModelViewSet):
    """ViewSet for Automated Reminders"""
    queryset = AutomatedReminder.objects.all()
    serializer_class = AutomatedReminderSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['reminder_type', 'recipient', 'status']
    search_fields = ['title', 'message']
    ordering = ['reminder_datetime']
    
    @action(detail=True, methods=['post'])
    def send_now(self, request, pk=None):
        """Send reminder immediately"""
        reminder = self.get_object()
        
        # Logic to send reminder (email, notification, etc.)
        # This would integrate with your notification system
        
        reminder.status = 'sent'
        reminder.sent_count += 1
        reminder.last_sent_at = timezone.now()
        reminder.save()
        
        serializer = self.get_serializer(reminder)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel a reminder"""
        reminder = self.get_object()
        reminder.status = 'cancelled'
        reminder.save()
        
        serializer = self.get_serializer(reminder)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def pending(self, request):
        """Get pending reminders"""
        pending_reminders = self.get_queryset().filter(
            status='pending',
            reminder_datetime__lte=timezone.now()
        )
        
        serializer = self.get_serializer(pending_reminders, many=True)
        return Response(serializer.data)
