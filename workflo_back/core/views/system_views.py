from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Count
from django.utils import timezone

# Try to import psutil, but don't fail if it's not available
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None

from ..models.system import AuditLog, ActivityLog, SystemSetting, CompanyInfo
from ..serializers.system_serializers import (
    AuditLogSerializer, ActivityLogSerializer, SystemSettingSerializer,
    CompanyInfoSerializer, SystemHealthSerializer, SystemStatisticsSerializer
)


class AuditLogViewSet(viewsets.ModelViewSet):
    """ViewSet for Audit Logs with enhanced tracking"""
    queryset = AuditLog.objects.all()
    serializer_class = AuditLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['user', 'action', 'table_name']
    search_fields = ['action', 'table_name']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """Filter audit logs based on date range and other criteria"""
        queryset = super().get_queryset()
        
        # Date range filtering
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')

        if start_date:
            queryset = queryset.filter(created_at__date__gte=start_date)
        if end_date:
            queryset = queryset.filter(created_at__date__lte=end_date)
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get audit log summary statistics"""
        queryset = self.get_queryset()
        
        summary = {
            'total_actions': queryset.count(),
            'actions_today': queryset.filter(
                created_at__date=timezone.now().date()
            ).count(),
            'actions_this_week': queryset.filter(
                created_at__gte=timezone.now() - timezone.timedelta(days=7)
            ).count(),
            'by_action': queryset.values('action').annotate(
                count=Count('id')
            ).order_by('-count'),
            'by_table': queryset.values('table_name').annotate(
                count=Count('id')
            ).order_by('-count'),
            'by_user': queryset.values('user__first_name', 'user__last_name').annotate(
                count=Count('id')
            ).order_by('-count')[:10]
        }
        
        return Response(summary)
    
    @action(detail=False, methods=['get'])
    def export(self, request):
        """Export audit logs to CSV"""
        # This would implement CSV export functionality
        # For now, return a message
        return Response({
            'message': 'Audit log export functionality would be implemented here',
            'note': 'This would generate a CSV file with filtered audit logs'
        })


class ActivityLogViewSet(viewsets.ModelViewSet):
    """ViewSet for Activity Logs"""
    queryset = ActivityLog.objects.all()
    serializer_class = ActivityLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['user', 'activity_type']
    search_fields = ['activity_type', 'activity_description']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """Filter activity logs"""
        queryset = super().get_queryset()
        
        # Filter by current user if requested
        if self.request.query_params.get('my_activities') == 'true':
            queryset = queryset.filter(user=self.request.user)
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def user_activity(self, request):
        """Get activity summary for current user"""
        user_activities = ActivityLog.objects.filter(user=request.user)
        
        summary = {
            'total_activities': user_activities.count(),
            'activities_today': user_activities.filter(
                created_at__date=timezone.now().date()
            ).count(),
            'last_login': user_activities.filter(
                activity_type='login'
            ).first().created_at if user_activities.filter(
                activity_type='login'
            ).exists() else None,
            'most_common_activities': user_activities.values('activity_type').annotate(
                count=Count('id')
            ).order_by('-count')[:5]
        }
        
        return Response(summary)


class SystemSettingViewSet(viewsets.ModelViewSet):
    """ViewSet for System Settings"""
    queryset = SystemSetting.objects.all()
    serializer_class = SystemSettingSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['setting_type']
    search_fields = ['setting_key', 'description']
    ordering = ['setting_key']
    
    def perform_update(self, serializer):
        serializer.save(updated_by=self.request.user)
    
    @action(detail=False, methods=['get'])
    def by_category(self, request):
        """Get settings grouped by category"""
        settings = self.get_queryset()
        
        # Group settings by a category prefix (e.g., 'email.', 'payroll.', etc.)
        categories = {}
        for setting in settings:
            category = setting.setting_key.split('.')[0] if '.' in setting.setting_key else 'general'
            if category not in categories:
                categories[category] = []
            
            serializer = self.get_serializer(setting)
            categories[category].append(serializer.data)
        
        return Response(categories)


class CompanyInfoViewSet(viewsets.ModelViewSet):
    """ViewSet for Company Information with full CRUD operations (Admin only)"""
    queryset = CompanyInfo.objects.all()
    serializer_class = CompanyInfoSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        """Set permissions based on action"""
        if self.action in ['list', 'retrieve', 'current']:
            # All authenticated users can view company info
            permission_classes = [permissions.IsAuthenticated]
        else:
            # Only admins can create, update, delete company info
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    def check_admin_permission(self, request):
        """Check if user has admin permissions"""
        if not hasattr(request.user, 'role') or request.user.role != 'admin':
            return Response({
                'error': 'Admin permissions required for this action'
            }, status=status.HTTP_403_FORBIDDEN)
        return None

    def create(self, request, *args, **kwargs):
        """Create company information (admin only)"""
        admin_check = self.check_admin_permission(request)
        if admin_check:
            return admin_check

        # Check if company info already exists
        if CompanyInfo.objects.exists():
            return Response({
                'error': 'Company information already exists. Use update instead.'
            }, status=status.HTTP_400_BAD_REQUEST)

        return super().create(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        """Update company information (admin only)"""
        admin_check = self.check_admin_permission(request)
        if admin_check:
            return admin_check
        return super().update(request, *args, **kwargs)

    def partial_update(self, request, *args, **kwargs):
        """Partially update company information (admin only)"""
        admin_check = self.check_admin_permission(request)
        if admin_check:
            return admin_check
        return super().partial_update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        """Delete company information (admin only)"""
        admin_check = self.check_admin_permission(request)
        if admin_check:
            return admin_check
        return super().destroy(request, *args, **kwargs)

    def perform_create(self, serializer):
        """Set updated_by when creating company info"""
        serializer.save(updated_by=self.request.user)

    def perform_update(self, serializer):
        """Set updated_by when updating company info"""
        serializer.save(updated_by=self.request.user)

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get current company information (accessible to all authenticated users)"""
        company_info = CompanyInfo.objects.first()
        if company_info:
            serializer = self.get_serializer(company_info)
            return Response(serializer.data)
        else:
            return Response({
                'message': 'No company information found',
                'note': 'Please create company information first'
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def upload_logo(self, request, pk=None):
        """Upload company logo (admin only)"""
        admin_check = self.check_admin_permission(request)
        if admin_check:
            return admin_check

        company_info = self.get_object()
        logo_file = request.FILES.get('logo')

        if not logo_file:
            return Response({
                'error': 'No logo file provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate file
        serializer = self.get_serializer()
        try:
            validated_logo = serializer.validate_logo(logo_file)
            company_info.logo = validated_logo
            company_info.updated_by = request.user
            company_info.save()

            return Response({
                'message': 'Logo uploaded successfully',
                'logo_url': serializer.get_logo_url(company_info)
            })
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['delete'])
    def remove_logo(self, request, pk=None):
        """Remove company logo (admin only)"""
        admin_check = self.check_admin_permission(request)
        if admin_check:
            return admin_check

        company_info = self.get_object()
        if company_info.logo:
            company_info.logo.delete()
            company_info.updated_by = request.user
            company_info.save()

            return Response({
                'message': 'Logo removed successfully'
            })
        else:
            return Response({
                'message': 'No logo to remove'
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def basic_info(self, request):
        """Get basic company information (name, logo, contact) for public display"""
        company_info = CompanyInfo.objects.first()
        if company_info:
            basic_data = {
                'company_name': company_info.company_name,
                'logo_url': self.get_serializer().get_logo_url(company_info),
                'phone_number': company_info.phone_number,
                'email': company_info.email,
                'website': company_info.website,
                'address': company_info.address,
                'city': company_info.city,
                'country': company_info.country
            }
            return Response(basic_data)
        else:
            return Response({
                'message': 'No company information found'
            }, status=status.HTTP_404_NOT_FOUND)


class SystemMonitoringViewSet(viewsets.ViewSet):
    """ViewSet for system monitoring and health checks"""
    permission_classes = [permissions.IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def health(self, request):
        """Get system health status"""
        try:
            # Database check
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                database_status = "healthy"
        except Exception:
            database_status = "unhealthy"
        
        # Memory and disk usage
        if PSUTIL_AVAILABLE:
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            memory_percent = memory.percent
            disk_percent = disk.percent
        else:
            memory_percent = 0
            disk_percent = 0
        
        # Active users (simplified)
        from ..models.auth import User
        active_users_count = User.objects.filter(is_active=True).count()
        total_employees = User.objects.count()
        
        health_data = {
            'database_status': database_status,
            'cache_status': 'healthy',  # Implement cache check
            'storage_status': 'healthy' if disk_percent < 90 else 'warning',
            'email_service_status': 'healthy',  # Implement email service check
            'background_tasks_status': 'healthy',  # Implement background tasks check
            'last_backup': None,  # Implement backup status
            'disk_usage_percentage': disk_percent,
            'memory_usage_percentage': memory_percent,
            'active_users_count': active_users_count,
            'total_employees': total_employees
        }
        
        serializer = SystemHealthSerializer(health_data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get system statistics"""
        from ..models.auth import User
        from ..models.documents import EmployeeDocument, CompanyDocument
        from ..models.notifications import Notification
        from ..models.workflow import WorkflowInstance
        
        # Calculate statistics
        today = timezone.now().date()
        
        # Active users today (users who have activity logs today)
        active_users_today = ActivityLog.objects.filter(
            created_at__date=today
        ).values('user').distinct().count()
        
        # Storage used (simplified calculation)
        total_documents = EmployeeDocument.objects.count() + CompanyDocument.objects.count()
        storage_used_mb = sum([
            doc.file_size or 0 for doc in EmployeeDocument.objects.all()
        ]) + sum([
            doc.file_size or 0 for doc in CompanyDocument.objects.all()
        ])
        storage_used_mb = storage_used_mb / (1024 * 1024)  # Convert to MB
        
        stats_data = {
            'total_users': User.objects.count(),
            'active_users_today': active_users_today,
            'total_documents': total_documents,
            'total_notifications': Notification.objects.count(),
            'pending_workflows': WorkflowInstance.objects.filter(status='pending').count(),
            'completed_workflows': WorkflowInstance.objects.filter(status='completed').count(),
            'storage_used_mb': round(storage_used_mb, 2),
            'average_response_time_ms': 150.0  # This would be calculated from actual metrics
        }
        
        serializer = SystemStatisticsSerializer(stats_data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def clear_logs(self, request):
        """Clear old logs (admin only)"""
        # Check if user has admin permissions
        if not request.user.is_superuser:
            return Response(
                {'error': 'Admin permissions required'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        days_to_keep = request.data.get('days_to_keep', 90)
        cutoff_date = timezone.now() - timezone.timedelta(days=days_to_keep)
        
        # Delete old logs
        audit_deleted = AuditLog.objects.filter(created_at__lt=cutoff_date).delete()[0]
        activity_deleted = ActivityLog.objects.filter(created_at__lt=cutoff_date).delete()[0]
        
        return Response({
            'message': f'Cleared logs older than {days_to_keep} days',
            'audit_logs_deleted': audit_deleted,
            'activity_logs_deleted': activity_deleted
        })
