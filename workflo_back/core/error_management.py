"""
Enhanced Error Management and Debugging System
Provides detailed error reporting for schema generation and API issues
"""

import logging
import traceback
import sys
from django.conf import settings
from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
import json

# Configure detailed logging
logger = logging.getLogger(__name__)

class DetailedErrorHandler:
    """Enhanced error handler with detailed debugging information"""
    
    @staticmethod
    def format_error_details(exc, context=None):
        """Format detailed error information for debugging"""
        error_details = {
            'error_type': type(exc).__name__,
            'error_message': str(exc),
            'traceback': traceback.format_exc(),
            'context': context or {},
            'python_version': sys.version,
            'django_debug': settings.DEBUG
        }
        
        # Add request context if available
        if context and 'request' in context:
            request = context['request']
            error_details['request_info'] = {
                'method': request.method,
                'path': request.path,
                'user': str(request.user) if hasattr(request, 'user') else 'Anonymous',
                'query_params': dict(request.GET),
                'headers': dict(request.headers) if hasattr(request, 'headers') else {}
            }
        
        return error_details
    
    @staticmethod
    def log_detailed_error(exc, context=None, level=logging.ERROR):
        """Log detailed error information"""
        error_details = DetailedErrorHandler.format_error_details(exc, context)
        
        logger.log(level, f"""
================================================================================
🚨 DETAILED ERROR REPORT
================================================================================
Error Type: {error_details['error_type']}
Error Message: {error_details['error_message']}

Request Info:
{json.dumps(error_details.get('request_info', {}), indent=2)}

Context:
{json.dumps(error_details.get('context', {}), indent=2)}

Full Traceback:
{error_details['traceback']}
================================================================================
        """)
        
        return error_details


def custom_exception_handler(exc, context):
    """Custom exception handler with detailed error reporting"""
    
    # Log detailed error information
    error_details = DetailedErrorHandler.log_detailed_error(exc, context)
    
    # Call REST framework's default exception handler first
    response = exception_handler(exc, context)
    
    if response is not None:
        # Enhance the response with detailed error information in debug mode
        if settings.DEBUG:
            response.data['debug_info'] = {
                'error_type': error_details['error_type'],
                'error_message': error_details['error_message'],
                'request_path': error_details.get('request_info', {}).get('path'),
                'traceback_summary': error_details['traceback'].split('\n')[-3:-1]
            }
    else:
        # Handle non-DRF exceptions
        if settings.DEBUG:
            response = Response({
                'error': 'Internal Server Error',
                'debug_info': error_details
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            response = Response({
                'error': 'Internal Server Error'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    return response


class SchemaErrorDiagnostics:
    """Specialized diagnostics for schema generation errors"""
    
    @staticmethod
    def diagnose_filterset_error(error_message):
        """Diagnose filterset field errors"""
        diagnostics = {
            'error_type': 'filterset_field_error',
            'problematic_fields': [],
            'suggested_fixes': []
        }
        
        # Extract field names from error message
        if "'Meta.fields' must not contain non-model field names:" in error_message:
            # Extract the field names after the colon
            fields_part = error_message.split("'Meta.fields' must not contain non-model field names:")[-1].strip()
            problematic_fields = [field.strip() for field in fields_part.split(',')]
            diagnostics['problematic_fields'] = problematic_fields
            
            # Generate suggested fixes
            for field in problematic_fields:
                diagnostics['suggested_fixes'].append({
                    'field': field,
                    'suggestion': f"Remove '{field}' from filterset_fields or verify it exists in the model",
                    'search_command': f"grep -r 'filterset_fields.*{field}' --include='*.py' ."
                })
        
        return diagnostics
    
    @staticmethod
    def find_problematic_views(field_names):
        """Find views that contain problematic fields"""
        import subprocess
        import os
        
        problematic_views = []
        
        for field in field_names:
            try:
                # Search for views containing this field
                result = subprocess.run(
                    ['grep', '-r', f'filterset_fields.*{field}', '--include=*.py', '.'],
                    capture_output=True,
                    text=True,
                    cwd=os.path.dirname(os.path.dirname(__file__))
                )
                
                if result.stdout:
                    for line in result.stdout.strip().split('\n'):
                        if line:
                            file_path, content = line.split(':', 1)
                            problematic_views.append({
                                'field': field,
                                'file': file_path,
                                'line': content.strip()
                            })
            except Exception as e:
                logger.error(f"Error searching for field {field}: {e}")
        
        return problematic_views


@method_decorator(csrf_exempt, name='dispatch')
class ErrorDiagnosticsView(View):
    """View for testing and diagnosing errors"""
    
    def get(self, request):
        """Get error diagnostics"""
        try:
            # Test schema generation
            from drf_spectacular.openapi import AutoSchema
            from drf_spectacular.generators import SchemaGenerator
            
            generator = SchemaGenerator()
            schema = generator.get_schema(request=request, public=True)
            
            return JsonResponse({
                'status': 'success',
                'message': 'Schema generation successful',
                'schema_info': {
                    'paths_count': len(schema.get('paths', {})),
                    'components_count': len(schema.get('components', {}).get('schemas', {}))
                }
            })
            
        except Exception as exc:
            # Diagnose the error
            error_details = DetailedErrorHandler.format_error_details(exc, {'request': request})
            
            # Special handling for schema errors
            if "'Meta.fields' must not contain non-model field names:" in str(exc):
                schema_diagnostics = SchemaErrorDiagnostics.diagnose_filterset_error(str(exc))
                problematic_views = SchemaErrorDiagnostics.find_problematic_views(
                    schema_diagnostics['problematic_fields']
                )
                
                return JsonResponse({
                    'status': 'error',
                    'error_type': 'schema_generation_error',
                    'error_details': error_details,
                    'schema_diagnostics': schema_diagnostics,
                    'problematic_views': problematic_views,
                    'fix_instructions': [
                        "1. Check the problematic_views to find which files need fixing",
                        "2. Remove the problematic fields from filterset_fields",
                        "3. Verify the fields exist in the corresponding model",
                        "4. Test schema generation again"
                    ]
                }, status=500)
            
            return JsonResponse({
                'status': 'error',
                'error_details': error_details
            }, status=500)


# Middleware for enhanced error logging
class DetailedErrorLoggingMiddleware:
    """Middleware to log detailed error information"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        try:
            response = self.get_response(request)
            return response
        except Exception as exc:
            # Log detailed error
            DetailedErrorHandler.log_detailed_error(exc, {'request': request})
            raise
    
    def process_exception(self, request, exception):
        """Process exceptions with detailed logging"""
        DetailedErrorHandler.log_detailed_error(exception, {'request': request})
        return None
