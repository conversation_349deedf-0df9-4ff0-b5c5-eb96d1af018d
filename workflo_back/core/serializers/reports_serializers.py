from rest_framework import serializers
from ..models.reports import SavedReport, ReportHistory


class SavedReportSerializer(serializers.ModelSerializer):
    """Serializer for SavedReport model"""
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = SavedReport
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at', 'last_generated', 'next_generation']
    
    def validate_schedule_day(self, value):
        """Validate schedule day"""
        if value is not None:
            schedule_frequency = self.initial_data.get('schedule_frequency')
            if schedule_frequency == 'weekly' and (value < 1 or value > 7):
                raise serializers.ValidationError("For weekly frequency, day must be between 1-7.")
            elif schedule_frequency == 'monthly' and (value < 1 or value > 31):
                raise serializers.ValidationError("For monthly frequency, day must be between 1-31.")
        return value


class ReportHistorySerializer(serializers.ModelSerializer):
    """Serializer for ReportHistory model"""
    saved_report_name = serializers.CharField(source='saved_report.name', read_only=True)
    generated_by_name = serializers.CharField(source='generated_by.get_full_name', read_only=True)
    
    class Meta:
        model = ReportHistory
        fields = '__all__'
        read_only_fields = ['id', 'created_at']
