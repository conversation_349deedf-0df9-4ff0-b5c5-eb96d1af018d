from rest_framework import serializers
from ..models.events import CompanyEvent, EventParticipant


class CompanyEventSerializer(serializers.ModelSerializer):
    """Serializer for CompanyEvent model"""
    organizer_name = serializers.CharField(source='organizer.get_full_name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    participant_count = serializers.SerializerMethodField()
    
    class Meta:
        model = CompanyEvent
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at', 'current_participants']
    
    def get_participant_count(self, obj):
        return obj.participants.count()
    
    def validate(self, data):
        """Validate event data"""
        start_datetime = data.get('start_datetime')
        end_datetime = data.get('end_datetime')
        
        if start_datetime and end_datetime and start_datetime >= end_datetime:
            raise serializers.ValidationError(
                "End datetime must be after start datetime."
            )
        
        registration_deadline = data.get('registration_deadline')
        if registration_deadline and start_datetime and registration_deadline >= start_datetime:
            raise serializers.ValidationError(
                "Registration deadline must be before event start time."
            )
        
        return data


class EventParticipantSerializer(serializers.ModelSerializer):
    """Serializer for EventParticipant model"""
    employee_name = serializers.CharField(source='employee.get_full_name', read_only=True)
    event_title = serializers.CharField(source='event.title', read_only=True)
    event_start_datetime = serializers.DateTimeField(source='event.start_datetime', read_only=True)
    
    class Meta:
        model = EventParticipant
        fields = '__all__'
        read_only_fields = ['id', 'registered_at']
    
    def validate_feedback_rating(self, value):
        """Validate feedback rating"""
        if value is not None and (value < 0 or value > 5):
            raise serializers.ValidationError("Rating must be between 0 and 5.")
        return value
