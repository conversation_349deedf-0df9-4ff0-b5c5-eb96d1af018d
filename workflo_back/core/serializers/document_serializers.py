from rest_framework import serializers
from django.core.files.uploadedfile import InMemoryUploadedFile, TemporaryUploadedFile
from ..models.documents import (
    DocumentCategory, EmployeeDocument, CompanyDocument, DocumentAcknowledgment
)
import os


class DocumentCategorySerializer(serializers.ModelSerializer):
    """Serializer for DocumentCategory model"""
    document_count = serializers.SerializerMethodField()
    
    class Meta:
        model = DocumentCategory
        fields = '__all__'
        read_only_fields = ['id', 'created_at']
    
    def get_document_count(self, obj):
        return obj.employee_documents.count() + obj.company_documents.count()


class EmployeeDocumentSerializer(serializers.ModelSerializer):
    """Enhanced serializer for EmployeeDocument model with media capabilities"""
    employee_name = serializers.CharField(source='employee.get_full_name', read_only=True)
    category_name = serializers.CharField(source='category.name', read_only=True)
    uploaded_by_name = serializers.CharField(source='uploaded_by.get_full_name', read_only=True)
    file_size_mb = serializers.SerializerMethodField()
    file_extension = serializers.SerializerMethodField()
    download_url = serializers.SerializerMethodField()
    
    class Meta:
        model = EmployeeDocument
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at', 'file_size']
    
    def get_file_size_mb(self, obj):
        """Get file size in MB"""
        if obj.file_size:
            return round(obj.file_size / (1024 * 1024), 2)
        return None
    
    def get_file_extension(self, obj):
        """Get file extension"""
        if obj.file_path:
            return os.path.splitext(obj.file_path)[1].lower()
        return None
    
    def get_download_url(self, obj):
        """Get download URL for the document"""
        if obj.file_path:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(f'/api/documents/employee/{obj.id}/download/')
        return None
    
    def validate_file_path(self, value):
        """Validate uploaded file"""
        if value:
            # Check file size (max 10MB)
            if hasattr(value, 'size') and value.size > 10 * 1024 * 1024:
                raise serializers.ValidationError("File size cannot exceed 10MB.")
            
            # Check file extension
            allowed_extensions = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png', '.txt']
            file_extension = os.path.splitext(value.name)[1].lower()
            if file_extension not in allowed_extensions:
                raise serializers.ValidationError(
                    f"File type {file_extension} not allowed. "
                    f"Allowed types: {', '.join(allowed_extensions)}"
                )
        
        return value


class CompanyDocumentSerializer(serializers.ModelSerializer):
    """Enhanced serializer for CompanyDocument model with media capabilities"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    uploaded_by_name = serializers.CharField(source='uploaded_by.get_full_name', read_only=True)
    file_size_mb = serializers.SerializerMethodField()
    file_extension = serializers.SerializerMethodField()
    download_url = serializers.SerializerMethodField()
    acknowledgment_count = serializers.SerializerMethodField()
    
    class Meta:
        model = CompanyDocument
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at', 'file_size']
    
    def get_file_size_mb(self, obj):
        """Get file size in MB"""
        if obj.file_size:
            return round(obj.file_size / (1024 * 1024), 2)
        return None
    
    def get_file_extension(self, obj):
        """Get file extension"""
        if obj.file_path:
            return os.path.splitext(obj.file_path)[1].lower()
        return None
    
    def get_download_url(self, obj):
        """Get download URL for the document"""
        if obj.file_path:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(f'/api/documents/company/{obj.id}/download/')
        return None
    
    def get_acknowledgment_count(self, obj):
        """Get count of acknowledgments"""
        return obj.acknowledgments.count()
    
    def validate_file_path(self, value):
        """Validate uploaded file"""
        if value:
            # Check file size (max 50MB for company documents)
            if hasattr(value, 'size') and value.size > 50 * 1024 * 1024:
                raise serializers.ValidationError("File size cannot exceed 50MB.")
            
            # Check file extension
            allowed_extensions = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png', '.txt', '.xlsx', '.xls']
            file_extension = os.path.splitext(value.name)[1].lower()
            if file_extension not in allowed_extensions:
                raise serializers.ValidationError(
                    f"File type {file_extension} not allowed. "
                    f"Allowed types: {', '.join(allowed_extensions)}"
                )
        
        return value


class DocumentAcknowledgmentSerializer(serializers.ModelSerializer):
    """Serializer for DocumentAcknowledgment model"""
    employee_name = serializers.CharField(source='employee.get_full_name', read_only=True)
    document_title = serializers.CharField(source='document.title', read_only=True)
    
    class Meta:
        model = DocumentAcknowledgment
        fields = '__all__'
        read_only_fields = ['id', 'acknowledged_at']


# Bulk upload serializer for multiple documents
class BulkDocumentUploadSerializer(serializers.Serializer):
    """Serializer for bulk document upload"""
    files = serializers.ListField(
        child=serializers.FileField(),
        allow_empty=False,
        max_length=10
    )
    category = serializers.PrimaryKeyRelatedField(
        queryset=DocumentCategory.objects.all(),
        required=True
    )
    employee = serializers.IntegerField(required=False, allow_null=True)
    is_confidential = serializers.BooleanField(default=False)
    requires_acknowledgment = serializers.BooleanField(default=False)
    
    def validate_files(self, value):
        """Validate uploaded files"""
        for file in value:
            # Check file size
            if file.size > 10 * 1024 * 1024:
                raise serializers.ValidationError(f"File {file.name} exceeds 10MB limit.")
            
            # Check file extension
            allowed_extensions = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png', '.txt']
            file_extension = os.path.splitext(file.name)[1].lower()
            if file_extension not in allowed_extensions:
                raise serializers.ValidationError(
                    f"File {file.name} has unsupported type {file_extension}."
                )
        
        return value
