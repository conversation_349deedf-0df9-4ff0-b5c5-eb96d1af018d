from rest_framework import serializers
from ..models.workflow import (
    WorkflowDefinition, WorkflowInstance, WorkflowStepExecution, AutomatedReminder
)


class WorkflowDefinitionSerializer(serializers.ModelSerializer):
    """Serializer for WorkflowDefinition model"""
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = WorkflowDefinition
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at']


class WorkflowInstanceSerializer(serializers.ModelSerializer):
    """Serializer for WorkflowInstance model"""
    workflow_name = serializers.CharField(source='workflow.name', read_only=True)
    initiated_by_name = serializers.CharField(source='initiated_by.get_full_name', read_only=True)
    current_assignee_name = serializers.CharField(source='current_assignee.get_full_name', read_only=True)
    
    class Meta:
        model = WorkflowInstance
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at']


class WorkflowStepExecutionSerializer(serializers.ModelSerializer):
    """Serializer for WorkflowStepExecution model"""
    workflow_subject = serializers.CharField(source='workflow_instance.subject', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.get_full_name', read_only=True)
    escalated_to_name = serializers.CharField(source='escalated_to.get_full_name', read_only=True)
    
    class Meta:
        model = WorkflowStepExecution
        fields = '__all__'
        read_only_fields = ['id']


class AutomatedReminderSerializer(serializers.ModelSerializer):
    """Serializer for AutomatedReminder model"""
    recipient_name = serializers.CharField(source='recipient.get_full_name', read_only=True)
    
    class Meta:
        model = AutomatedReminder
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'sent_count', 'last_sent_at']
    
    def validate_repeat_count(self, value):
        """Validate repeat count"""
        if value < 1:
            raise serializers.ValidationError("Repeat count must be at least 1.")
        return value
