from rest_framework import serializers
from ..models.billing import (
    SubscriptionPlan, CompanySubscription, BillingInvoice,
    ModuleSetting, FeatureToggle
)


class SubscriptionPlanSerializer(serializers.ModelSerializer):
    """Serializer for SubscriptionPlan model"""
    
    class Meta:
        model = SubscriptionPlan
        fields = '__all__'
        read_only_fields = ['id', 'created_at']


class CompanySubscriptionSerializer(serializers.ModelSerializer):
    """Serializer for CompanySubscription model"""
    plan_name = serializers.CharField(source='plan.plan_name', read_only=True)
    billing_contact_name = serializers.CharField(source='billing_contact.get_full_name', read_only=True)
    
    class Meta:
        model = CompanySubscription
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate(self, data):
        """Validate subscription data"""
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        if start_date and end_date and start_date >= end_date:
            raise serializers.ValidationError(
                "End date must be after start date."
            )
        
        return data


class BillingInvoiceSerializer(serializers.ModelSerializer):
    """Serializer for BillingInvoice model"""
    subscription_plan_name = serializers.CharField(source='subscription.plan.plan_name', read_only=True)
    
    class Meta:
        model = BillingInvoice
        fields = '__all__'
        read_only_fields = ['id', 'created_at']
    
    def validate(self, data):
        """Validate invoice data"""
        billing_period_start = data.get('billing_period_start')
        billing_period_end = data.get('billing_period_end')
        
        if billing_period_start and billing_period_end and billing_period_start >= billing_period_end:
            raise serializers.ValidationError(
                "Billing period end must be after start."
            )
        
        subtotal = data.get('subtotal', 0)
        tax_amount = data.get('tax_amount', 0)
        total_amount = data.get('total_amount', 0)
        
        if total_amount != subtotal + tax_amount:
            raise serializers.ValidationError(
                "Total amount must equal subtotal plus tax amount."
            )
        
        return data


class ModuleSettingSerializer(serializers.ModelSerializer):
    """Serializer for ModuleSetting model"""
    updated_by_name = serializers.CharField(source='updated_by.get_full_name', read_only=True)
    
    class Meta:
        model = ModuleSetting
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at']


class FeatureToggleSerializer(serializers.ModelSerializer):
    """Serializer for FeatureToggle model"""
    updated_by_name = serializers.CharField(source='updated_by.get_full_name', read_only=True)
    
    class Meta:
        model = FeatureToggle
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_rollout_percentage(self, value):
        """Validate rollout percentage"""
        if value < 0 or value > 100:
            raise serializers.ValidationError("Rollout percentage must be between 0 and 100.")
        return value
