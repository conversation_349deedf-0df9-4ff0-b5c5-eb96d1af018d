from rest_framework import serializers
from ..models.system import AuditLog, ActivityLog, SystemSetting, CompanyInfo


class AuditLogSerializer(serializers.ModelSerializer):
    """Enhanced serializer for AuditLog model"""
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    time_since_action = serializers.SerializerMethodField()
    
    class Meta:
        model = AuditLog
        fields = '__all__'
        read_only_fields = ['id', 'timestamp']
    
    def get_time_since_action(self, obj):
        """Get human-readable time since action"""
        from django.utils import timezone
        from datetime import timedelta
        
        now = timezone.now()
        diff = now - obj.timestamp
        
        if diff < timedelta(minutes=1):
            return "Just now"
        elif diff < timedelta(hours=1):
            minutes = int(diff.total_seconds() / 60)
            return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
        elif diff < timedelta(days=1):
            hours = int(diff.total_seconds() / 3600)
            return f"{hours} hour{'s' if hours != 1 else ''} ago"
        elif diff < timedelta(days=7):
            days = diff.days
            return f"{days} day{'s' if days != 1 else ''} ago"
        else:
            return obj.timestamp.strftime('%Y-%m-%d %H:%M')


class ActivityLogSerializer(serializers.ModelSerializer):
    """Enhanced serializer for ActivityLog model"""
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    time_since_activity = serializers.SerializerMethodField()
    
    class Meta:
        model = ActivityLog
        fields = '__all__'
        read_only_fields = ['id', 'timestamp']
    
    def get_time_since_activity(self, obj):
        """Get human-readable time since activity"""
        from django.utils import timezone
        from datetime import timedelta
        
        now = timezone.now()
        diff = now - obj.timestamp
        
        if diff < timedelta(minutes=1):
            return "Just now"
        elif diff < timedelta(hours=1):
            minutes = int(diff.total_seconds() / 60)
            return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
        elif diff < timedelta(days=1):
            hours = int(diff.total_seconds() / 3600)
            return f"{hours} hour{'s' if hours != 1 else ''} ago"
        else:
            return obj.timestamp.strftime('%Y-%m-%d %H:%M')


class SystemSettingSerializer(serializers.ModelSerializer):
    """Enhanced serializer for SystemSetting model"""
    updated_by_name = serializers.CharField(source='updated_by.get_full_name', read_only=True)
    
    class Meta:
        model = SystemSetting
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_setting_value(self, value):
        """Validate setting value based on setting type"""
        setting_type = self.initial_data.get('setting_type')
        
        if setting_type == 'boolean':
            if value.lower() not in ['true', 'false', '1', '0']:
                raise serializers.ValidationError(
                    "Boolean setting must be 'true', 'false', '1', or '0'."
                )
        elif setting_type == 'integer':
            try:
                int(value)
            except ValueError:
                raise serializers.ValidationError("Integer setting must be a valid number.")
        elif setting_type == 'decimal':
            try:
                float(value)
            except ValueError:
                raise serializers.ValidationError("Decimal setting must be a valid number.")
        
        return value


class CompanyInfoSerializer(serializers.ModelSerializer):
    """Enhanced serializer for CompanyInfo model with full CRUD operations"""
    updated_by_name = serializers.CharField(source='updated_by.get_full_name', read_only=True)
    logo_url = serializers.SerializerMethodField()
    
    class Meta:
        model = CompanyInfo
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_logo_url(self, obj):
        """Get company logo URL"""
        if obj.logo:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.logo.url)
        return None
    
    def validate_email(self, value):
        """Validate company email"""
        if value:
            from django.core.validators import validate_email
            from django.core.exceptions import ValidationError
            try:
                validate_email(value)
            except ValidationError:
                raise serializers.ValidationError("Enter a valid email address.")
        return value
    
    def validate_phone(self, value):
        """Validate company phone number"""
        if value:
            import re
            # Kenyan phone number pattern
            pattern = r'^(\+254|0)[17]\d{8}$'
            if not re.match(pattern, value):
                raise serializers.ValidationError(
                    "Enter a valid Kenyan phone number (e.g., +254712345678 or 0712345678)."
                )
        return value
    
    def validate_logo(self, value):
        """Validate company logo"""
        if value:
            # Check file size (max 2MB)
            if value.size > 2 * 1024 * 1024:
                raise serializers.ValidationError("Logo file size cannot exceed 2MB.")
            
            # Check file type
            allowed_types = ['image/jpeg', 'image/png', 'image/gif']
            if value.content_type not in allowed_types:
                raise serializers.ValidationError(
                    "Logo must be a JPEG, PNG, or GIF image."
                )
        
        return value


# System health serializer
class SystemHealthSerializer(serializers.Serializer):
    """Serializer for system health status"""
    database_status = serializers.CharField(read_only=True)
    cache_status = serializers.CharField(read_only=True)
    storage_status = serializers.CharField(read_only=True)
    email_service_status = serializers.CharField(read_only=True)
    background_tasks_status = serializers.CharField(read_only=True)
    last_backup = serializers.DateTimeField(read_only=True)
    disk_usage_percentage = serializers.FloatField(read_only=True)
    memory_usage_percentage = serializers.FloatField(read_only=True)
    active_users_count = serializers.IntegerField(read_only=True)
    total_employees = serializers.IntegerField(read_only=True)


# System statistics serializer
class SystemStatisticsSerializer(serializers.Serializer):
    """Serializer for system statistics"""
    total_users = serializers.IntegerField(read_only=True)
    active_users_today = serializers.IntegerField(read_only=True)
    total_documents = serializers.IntegerField(read_only=True)
    total_notifications = serializers.IntegerField(read_only=True)
    pending_workflows = serializers.IntegerField(read_only=True)
    completed_workflows = serializers.IntegerField(read_only=True)
    storage_used_mb = serializers.FloatField(read_only=True)
    average_response_time_ms = serializers.FloatField(read_only=True)
