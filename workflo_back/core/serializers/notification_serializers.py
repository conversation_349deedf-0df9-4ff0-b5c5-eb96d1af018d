from rest_framework import serializers
from ..models.notifications import NotificationTemplate, Notification, EmailLog


class NotificationTemplateSerializer(serializers.ModelSerializer):
    """Enhanced serializer for NotificationTemplate model"""
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    usage_count = serializers.SerializerMethodField()
    
    class Meta:
        model = NotificationTemplate
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_usage_count(self, obj):
        """Get count of notifications using this template"""
        return obj.notifications.count()
    
    def validate_template_variables(self, value):
        """Validate template variables JSON"""
        if value:
            if not isinstance(value, list):
                raise serializers.ValidationError("Template variables must be a list.")
            
            for variable in value:
                if not isinstance(variable, str):
                    raise serializers.ValidationError("Each template variable must be a string.")
        
        return value


class NotificationSerializer(serializers.ModelSerializer):
    """Enhanced serializer for Notification model"""
    recipient_name = serializers.CharField(source='recipient.get_full_name', read_only=True)
    sender_name = serializers.CharField(source='sender.get_full_name', read_only=True)
    template_name = serializers.CharField(source='template.name', read_only=True)
    time_since_created = serializers.SerializerMethodField()
    
    class Meta:
        model = Notification
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'sent_at', 'read_at']
    
    def get_time_since_created(self, obj):
        """Get human-readable time since notification was created"""
        from django.utils import timezone
        from datetime import timedelta
        
        now = timezone.now()
        diff = now - obj.created_at
        
        if diff < timedelta(minutes=1):
            return "Just now"
        elif diff < timedelta(hours=1):
            minutes = int(diff.total_seconds() / 60)
            return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
        elif diff < timedelta(days=1):
            hours = int(diff.total_seconds() / 3600)
            return f"{hours} hour{'s' if hours != 1 else ''} ago"
        elif diff < timedelta(days=7):
            days = diff.days
            return f"{days} day{'s' if days != 1 else ''} ago"
        else:
            return obj.created_at.strftime('%Y-%m-%d')


class EmailLogSerializer(serializers.ModelSerializer):
    """Enhanced serializer for EmailLog model"""
    recipient_name = serializers.CharField(source='recipient.get_full_name', read_only=True)
    template_name = serializers.CharField(source='template.name', read_only=True)
    
    class Meta:
        model = EmailLog
        fields = '__all__'
        read_only_fields = ['id', 'sent_at']


# Bulk notification serializer
class BulkNotificationSerializer(serializers.Serializer):
    """Serializer for sending bulk notifications"""
    RECIPIENT_TYPE_CHOICES = [
        ('all', 'All Employees'),
        ('department', 'Department'),
        ('role', 'Role'),
        ('custom', 'Custom List'),
    ]
    
    title = serializers.CharField(max_length=255)
    message = serializers.CharField()
    notification_type = serializers.ChoiceField(choices=Notification.NOTIFICATION_TYPE_CHOICES)
    priority = serializers.ChoiceField(choices=Notification.PRIORITY_CHOICES, default='medium')
    recipient_type = serializers.ChoiceField(choices=RECIPIENT_TYPE_CHOICES)
    department_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        allow_empty=True
    )
    role_names = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        allow_empty=True
    )
    recipient_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        allow_empty=True
    )
    send_email = serializers.BooleanField(default=False)
    send_sms = serializers.BooleanField(default=False)
    schedule_datetime = serializers.DateTimeField(required=False)
    
    def validate(self, data):
        """Validate bulk notification data"""
        recipient_type = data.get('recipient_type')
        
        if recipient_type == 'department' and not data.get('department_ids'):
            raise serializers.ValidationError(
                "Department IDs are required when recipient type is 'department'."
            )
        
        if recipient_type == 'role' and not data.get('role_names'):
            raise serializers.ValidationError(
                "Role names are required when recipient type is 'role'."
            )
        
        if recipient_type == 'custom' and not data.get('recipient_ids'):
            raise serializers.ValidationError(
                "Recipient IDs are required when recipient type is 'custom'."
            )
        
        return data


# Communication preferences serializer
class CommunicationPreferencesSerializer(serializers.Serializer):
    """Serializer for user communication preferences"""
    email_notifications = serializers.BooleanField(default=True)
    sms_notifications = serializers.BooleanField(default=False)
    push_notifications = serializers.BooleanField(default=True)
    notification_types = serializers.MultipleChoiceField(
        choices=Notification.NOTIFICATION_TYPE_CHOICES,
        default=['system', 'workflow', 'reminder']
    )
    quiet_hours_start = serializers.TimeField(required=False)
    quiet_hours_end = serializers.TimeField(required=False)
    weekend_notifications = serializers.BooleanField(default=False)
    
    def validate(self, data):
        """Validate communication preferences"""
        quiet_start = data.get('quiet_hours_start')
        quiet_end = data.get('quiet_hours_end')
        
        if quiet_start and not quiet_end:
            raise serializers.ValidationError(
                "Quiet hours end time is required when start time is provided."
            )
        
        if quiet_end and not quiet_start:
            raise serializers.ValidationError(
                "Quiet hours start time is required when end time is provided."
            )
        
        return data
