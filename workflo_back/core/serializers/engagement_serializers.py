from rest_framework import serializers
from ..models.engagement import (
    EmployeeSurvey, SurveyResponse, RecognitionCategory, EmployeeRecognition,
    WellnessProgram, WellnessProgramEnrollment, WellnessActivityLog,
    EAPResource, EAPAccessLog, EmployeeFeedback
)
from ..models.auth import User


class EmployeeSurveySerializer(serializers.ModelSerializer):
    """Serializer for EmployeeSurvey model"""
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = EmployeeSurvey
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at', 'response_count']


class SurveyResponseSerializer(serializers.ModelSerializer):
    """Serializer for SurveyResponse model"""
    employee_name = serializers.CharField(source='employee.get_full_name', read_only=True)
    survey_title = serializers.CharField(source='survey.title', read_only=True)
    
    class Meta:
        model = SurveyResponse
        fields = '__all__'
        read_only_fields = ['id', 'submitted_at']


class RecognitionCategorySerializer(serializers.ModelSerializer):
    """Serializer for RecognitionCategory model"""
    
    class Meta:
        model = RecognitionCategory
        fields = '__all__'
        read_only_fields = ['id', 'created_at']


class EmployeeRecognitionSerializer(serializers.ModelSerializer):
    """Serializer for EmployeeRecognition model"""
    recipient_name = serializers.CharField(source='recipient.get_full_name', read_only=True)
    nominator_name = serializers.CharField(source='nominator.get_full_name', read_only=True)
    category_name = serializers.CharField(source='category.name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)
    
    class Meta:
        model = EmployeeRecognition
        fields = '__all__'
        read_only_fields = ['id', 'created_at']


class WellnessProgramSerializer(serializers.ModelSerializer):
    """Serializer for WellnessProgram model"""
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    participant_count = serializers.SerializerMethodField()
    
    class Meta:
        model = WellnessProgram
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'current_participants']
    
    def get_participant_count(self, obj):
        return obj.participants.count()


class WellnessProgramEnrollmentSerializer(serializers.ModelSerializer):
    """Serializer for WellnessProgramEnrollment model"""
    employee_name = serializers.CharField(source='employee.get_full_name', read_only=True)
    program_title = serializers.CharField(source='program.title', read_only=True)
    
    class Meta:
        model = WellnessProgramEnrollment
        fields = '__all__'
        read_only_fields = ['id', 'joined_at']


class WellnessActivityLogSerializer(serializers.ModelSerializer):
    """Serializer for WellnessActivityLog model"""
    employee_name = serializers.CharField(source='participant.employee.get_full_name', read_only=True)
    program_title = serializers.CharField(source='participant.program.title', read_only=True)
    
    class Meta:
        model = WellnessActivityLog
        fields = '__all__'
        read_only_fields = ['id', 'created_at']


class EAPResourceSerializer(serializers.ModelSerializer):
    """Serializer for EAPResource model"""
    
    class Meta:
        model = EAPResource
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at', 'access_count']


class EAPAccessLogSerializer(serializers.ModelSerializer):
    """Serializer for EAPAccessLog model"""
    employee_name = serializers.CharField(source='employee.get_full_name', read_only=True)
    resource_title = serializers.CharField(source='resource.title', read_only=True)
    
    class Meta:
        model = EAPAccessLog
        fields = '__all__'
        read_only_fields = ['id', 'accessed_at']


class EmployeeFeedbackSerializer(serializers.ModelSerializer):
    """Serializer for EmployeeFeedback model"""
    employee_name = serializers.CharField(source='employee.get_full_name', read_only=True)
    reviewed_by_name = serializers.CharField(source='reviewed_by.get_full_name', read_only=True)
    
    class Meta:
        model = EmployeeFeedback
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at']


# Bank Profile and Emergency Contact Serializers (for CRUD operations)
from ..models.employees import BankProfile, EmergencyContact


class BankProfileSerializer(serializers.ModelSerializer):
    """Serializer for BankProfile model with full CRUD operations"""
    employee_name = serializers.CharField(source='employee.get_full_name', read_only=True)
    
    class Meta:
        model = BankProfile
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate(self, data):
        """Validate bank profile data"""
        # Ensure only one primary account per employee
        if data.get('is_primary', False):
            employee = data.get('employee')
            if employee and BankProfile.objects.filter(
                employee=employee, 
                is_primary=True
            ).exclude(id=self.instance.id if self.instance else None).exists():
                raise serializers.ValidationError(
                    "Employee can only have one primary bank account."
                )
        return data


class EmergencyContactSerializer(serializers.ModelSerializer):
    """Serializer for EmergencyContact model with full CRUD operations"""
    employee_name = serializers.CharField(source='employee.get_full_name', read_only=True)
    
    class Meta:
        model = EmergencyContact
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_priority_order(self, value):
        """Validate priority order"""
        if value < 1:
            raise serializers.ValidationError("Priority order must be at least 1.")
        return value
