# Generated by Django 5.2 on 2025-06-01 17:16

import datetime
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_alter_trainingmodule_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='EAPResource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('category', models.CharField(blank=True, max_length=100, null=True)),
                ('resource_type', models.CharField(choices=[('article', 'Article'), ('video', 'Video'), ('webinar', 'Webinar'), ('contact', 'Contact'), ('external_link', 'External Link'), ('document', 'Document')], max_length=50)),
                ('content_url', models.TextField(blank=True, null=True)),
                ('contact_info', models.TextField(blank=True, null=True)),
                ('is_confidential', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('access_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'EAP Resource',
                'verbose_name_plural': 'EAP Resources',
                'db_table': 'eap_resources',
                'ordering': ['title'],
            },
        ),
        migrations.CreateModel(
            name='SubscriptionPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('plan_name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('max_employees', models.IntegerField(blank=True, null=True)),
                ('features', models.JSONField()),
                ('price_per_employee', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('price_per_month', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('billing_cycle', models.CharField(choices=[('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('yearly', 'Yearly')], max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'verbose_name': 'Subscription Plan',
                'verbose_name_plural': 'Subscription Plans',
                'db_table': 'subscription_plans',
                'ordering': ['plan_name'],
            },
        ),
        migrations.AlterModelOptions(
            name='emaillog',
            options={'ordering': ['-created_at']},
        ),
        migrations.AlterModelOptions(
            name='employeesurvey',
            options={'ordering': ['-created_at'], 'verbose_name': 'Employee Survey', 'verbose_name_plural': 'Employee Surveys'},
        ),
        migrations.AlterModelOptions(
            name='notification',
            options={'ordering': ['-created_at']},
        ),
        migrations.AlterModelOptions(
            name='notificationtemplate',
            options={'ordering': ['name']},
        ),
        migrations.AlterModelOptions(
            name='recognitioncategory',
            options={'ordering': ['name'], 'verbose_name': 'Recognition Category', 'verbose_name_plural': 'Recognition Categories'},
        ),
        migrations.AlterModelOptions(
            name='surveyresponse',
            options={'ordering': ['-submitted_at'], 'verbose_name': 'Survey Response', 'verbose_name_plural': 'Survey Responses'},
        ),
        migrations.AlterModelOptions(
            name='wellnessprogramenrollment',
            options={'ordering': ['-joined_at'], 'verbose_name': 'Wellness Program Enrollment', 'verbose_name_plural': 'Wellness Program Enrollments'},
        ),
        migrations.RenameField(
            model_name='employeefeedback',
            old_name='resolved_date',
            new_name='reviewed_at',
        ),
        migrations.RenameField(
            model_name='notificationtemplate',
            old_name='body_template',
            new_name='body',
        ),
        migrations.RenameField(
            model_name='wellnessprogram',
            old_name='benefits',
            new_name='reward_description',
        ),
        migrations.RenameField(
            model_name='wellnessprogramenrollment',
            old_name='progress_percentage',
            new_name='current_progress',
        ),
        migrations.RenameField(
            model_name='wellnessprogramenrollment',
            old_name='enrollment_date',
            new_name='joined_at',
        ),
        migrations.RemoveField(
            model_name='emaillog',
            name='notification',
        ),
        migrations.RemoveField(
            model_name='employeefeedback',
            name='assigned_to',
        ),
        migrations.RemoveField(
            model_name='employeefeedback',
            name='category',
        ),
        migrations.RemoveField(
            model_name='employeefeedback',
            name='priority',
        ),
        migrations.RemoveField(
            model_name='employeefeedback',
            name='response_date',
        ),
        migrations.RemoveField(
            model_name='notificationtemplate',
            name='event_type',
        ),
        migrations.RemoveField(
            model_name='notificationtemplate',
            name='subject_template',
        ),
        migrations.RemoveField(
            model_name='wellnessprogram',
            name='company_contribution',
        ),
        migrations.RemoveField(
            model_name='wellnessprogram',
            name='cost_per_participant',
        ),
        migrations.RemoveField(
            model_name='wellnessprogram',
            name='employee_contribution',
        ),
        migrations.RemoveField(
            model_name='wellnessprogram',
            name='enrollment_deadline',
        ),
        migrations.RemoveField(
            model_name='wellnessprogram',
            name='name',
        ),
        migrations.RemoveField(
            model_name='wellnessprogram',
            name='provider',
        ),
        migrations.RemoveField(
            model_name='wellnessprogram',
            name='requirements',
        ),
        migrations.RemoveField(
            model_name='wellnessprogram',
            name='updated_at',
        ),
        migrations.RemoveField(
            model_name='wellnessprogramenrollment',
            name='completion_date',
        ),
        migrations.RemoveField(
            model_name='wellnessprogramenrollment',
            name='feedback',
        ),
        migrations.RemoveField(
            model_name='wellnessprogramenrollment',
            name='satisfaction_rating',
        ),
        migrations.RemoveField(
            model_name='wellnessprogramenrollment',
            name='start_date',
        ),
        migrations.RemoveField(
            model_name='wellnessprogramenrollment',
            name='status',
        ),
        migrations.AddField(
            model_name='emaillog',
            name='body',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='emaillog',
            name='delivered_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='emaillog',
            name='error_message',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='emaillog',
            name='recipient',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='email_logs', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='emaillog',
            name='sender_email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='emaillog',
            name='sent_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='emaillog',
            name='template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='email_logs', to='core.notificationtemplate'),
        ),
        migrations.AddField(
            model_name='employeefeedback',
            name='reviewed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='feedback_reviewed', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='employeerecognition',
            name='approved_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='employeerecognition',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_recognitions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='employeerecognition',
            name='milestone_type',
            field=models.CharField(blank=True, choices=[('work_anniversary', 'Work Anniversary'), ('birthday', 'Birthday'), ('project_completion', 'Project Completion'), ('goal_achievement', 'Goal Achievement')], max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='employeerecognition',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='approved', max_length=20),
        ),
        migrations.AddField(
            model_name='employeesurvey',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_surveys', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='employeesurvey',
            name='department_ids',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='employeesurvey',
            name='employee_ids',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='employeesurvey',
            name='is_anonymous',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='employeesurvey',
            name='questions',
            field=models.JSONField(default=[]),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='employeesurvey',
            name='response_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='employeesurvey',
            name='role_filters',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='employeesurvey',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('active', 'Active'), ('paused', 'Paused'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='draft', max_length=20),
        ),
        migrations.AddField(
            model_name='employeesurvey',
            name='target_audience',
            field=models.CharField(choices=[('all', 'All'), ('department', 'Department'), ('role', 'Role'), ('custom', 'Custom')], default='all', max_length=20),
        ),
        migrations.AddField(
            model_name='employeesurvey',
            name='target_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='employeesurvey',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='notification',
            name='notification_type',
            field=models.CharField(choices=[('system', 'System'), ('workflow', 'Workflow'), ('reminder', 'Reminder'), ('announcement', 'Announcement'), ('alert', 'Alert')], default='system', max_length=50),
        ),
        migrations.AddField(
            model_name='notification',
            name='priority',
            field=models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=20),
        ),
        migrations.AddField(
            model_name='notification',
            name='read_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='notification',
            name='sent_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='notification',
            name='template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='notifications', to='core.notificationtemplate'),
        ),
        migrations.AddField(
            model_name='notificationtemplate',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_notification_templates', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='notificationtemplate',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='notificationtemplate',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='notificationtemplate',
            name='notification_type',
            field=models.CharField(choices=[('system', 'System'), ('workflow', 'Workflow'), ('reminder', 'Reminder'), ('announcement', 'Announcement'), ('alert', 'Alert')], default='system', max_length=50),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='notificationtemplate',
            name='subject',
            field=models.CharField(default='Default Subject', max_length=255),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='notificationtemplate',
            name='template_variables',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AddField(
            model_name='notificationtemplate',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='overtimerequest',
            name='attendance_record',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='overtime_requests', to='core.attendancerecord'),
        ),
        migrations.AddField(
            model_name='overtimerequest',
            name='auto_detected',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='overtimerequest',
            name='biostar_event_id',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='recognitioncategory',
            name='icon',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='recognitioncategory',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='surveyresponse',
            name='completion_time_seconds',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='surveyresponse',
            name='ip_address',
            field=models.GenericIPAddressField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='wellnessprogram',
            name='goal_target',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='wellnessprogram',
            name='goal_type',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='wellnessprogram',
            name='is_team_based',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='wellnessprogram',
            name='reward_points',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='wellnessprogram',
            name='title',
            field=models.CharField(default='Default Wellness Program', max_length=255),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='wellnessprogramenrollment',
            name='completed_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='wellnessprogramenrollment',
            name='goal_achieved',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='wellnessprogramenrollment',
            name='points_earned',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='wellnessprogramenrollment',
            name='team_name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='emaillog',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('delivered', 'Delivered'), ('failed', 'Failed'), ('bounced', 'Bounced')], default='pending', max_length=20),
        ),
        migrations.AlterField(
            model_name='employeefeedback',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feedback_given', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='employeefeedback',
            name='feedback_type',
            field=models.CharField(choices=[('suggestion', 'Suggestion'), ('complaint', 'Complaint'), ('compliment', 'Compliment'), ('general', 'General')], max_length=20),
        ),
        migrations.AlterField(
            model_name='employeefeedback',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('reviewed', 'Reviewed'), ('resolved', 'Resolved'), ('closed', 'Closed')], default='pending', max_length=20),
        ),
        migrations.AlterField(
            model_name='employeerecognition',
            name='achievement_date',
            field=models.DateField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='employeerecognition',
            name='recognition_type',
            field=models.CharField(choices=[('peer_to_peer', 'Peer to Peer'), ('manager_to_employee', 'Manager to Employee'), ('milestone', 'Milestone'), ('achievement', 'Achievement'), ('system', 'System')], default='peer_to_peer', max_length=50),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='employeesurvey',
            name='survey_type',
            field=models.CharField(choices=[('pulse', 'Pulse'), ('engagement', 'Engagement'), ('satisfaction', 'Satisfaction'), ('exit', 'Exit'), ('feedback', 'Feedback'), ('culture', 'Culture')], max_length=50),
        ),
        migrations.AlterField(
            model_name='surveyresponse',
            name='employee',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='survey_responses', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='surveyresponse',
            name='responses',
            field=models.JSONField(),
        ),
        migrations.AlterField(
            model_name='surveyresponse',
            name='survey',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='core.employeesurvey'),
        ),
        migrations.AlterField(
            model_name='wellnessprogram',
            name='end_date',
            field=models.DateField(default=datetime.date(2025, 6, 1)),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='wellnessprogram',
            name='program_type',
            field=models.CharField(choices=[('fitness_challenge', 'Fitness Challenge'), ('mental_health', 'Mental Health'), ('nutrition', 'Nutrition'), ('step_counter', 'Step Counter'), ('meditation', 'Meditation'), ('health_screening', 'Health Screening')], max_length=50),
        ),
        migrations.AlterField(
            model_name='wellnessprogram',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('active', 'Active'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='active', max_length=20),
        ),
        migrations.AlterField(
            model_name='wellnessprogramenrollment',
            name='program',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='participants', to='core.wellnessprogram'),
        ),
        migrations.AlterUniqueTogether(
            name='surveyresponse',
            unique_together={('survey', 'employee')},
        ),
        migrations.AlterModelTable(
            name='wellnessprogramenrollment',
            table='wellness_participants',
        ),
        migrations.CreateModel(
            name='AutomatedReminder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reminder_type', models.CharField(choices=[('workflow_due', 'Workflow Due'), ('review_due', 'Review Due'), ('document_expiry', 'Document Expiry'), ('birthday', 'Birthday'), ('anniversary', 'Anniversary')], max_length=50)),
                ('entity_type', models.CharField(blank=True, max_length=50, null=True)),
                ('entity_id', models.IntegerField(blank=True, null=True)),
                ('title', models.CharField(max_length=255)),
                ('message', models.TextField()),
                ('reminder_datetime', models.DateTimeField()),
                ('repeat_interval', models.CharField(blank=True, choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('yearly', 'Yearly')], max_length=20, null=True)),
                ('repeat_count', models.IntegerField(default=1)),
                ('sent_count', models.IntegerField(default=0)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('cancelled', 'Cancelled'), ('completed', 'Completed')], default='pending', max_length=20)),
                ('last_sent_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='automated_reminders', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Automated Reminder',
                'verbose_name_plural': 'Automated Reminders',
                'db_table': 'automated_reminders',
                'ordering': ['reminder_datetime'],
            },
        ),
        migrations.CreateModel(
            name='CompanyEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('event_type', models.CharField(choices=[('meeting', 'Meeting'), ('training', 'Training'), ('social', 'Social'), ('team_building', 'Team Building'), ('celebration', 'Celebration'), ('announcement', 'Announcement'), ('wellness', 'Wellness')], max_length=50)),
                ('start_datetime', models.DateTimeField()),
                ('end_datetime', models.DateTimeField()),
                ('location', models.CharField(blank=True, max_length=255, null=True)),
                ('is_virtual', models.BooleanField(default=False)),
                ('meeting_link', models.TextField(blank=True, null=True)),
                ('max_participants', models.IntegerField(blank=True, null=True)),
                ('current_participants', models.IntegerField(default=0)),
                ('is_mandatory', models.BooleanField(default=False)),
                ('department_ids', models.TextField(blank=True, null=True)),
                ('role_filters', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('scheduled', 'Scheduled'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='scheduled', max_length=20)),
                ('registration_required', models.BooleanField(default=False)),
                ('registration_deadline', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_events', to=settings.AUTH_USER_MODEL)),
                ('organizer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='organized_events', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Company Event',
                'verbose_name_plural': 'Company Events',
                'db_table': 'company_events',
                'ordering': ['-start_datetime'],
            },
        ),
        migrations.CreateModel(
            name='CompanySubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('employee_count', models.IntegerField()),
                ('monthly_cost', models.DecimalField(decimal_places=2, max_digits=10)),
                ('status', models.CharField(choices=[('trial', 'Trial'), ('active', 'Active'), ('suspended', 'Suspended'), ('cancelled', 'Cancelled'), ('expired', 'Expired')], default='active', max_length=20)),
                ('auto_renew', models.BooleanField(default=True)),
                ('payment_method', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('billing_contact', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='billing_subscriptions', to=settings.AUTH_USER_MODEL)),
                ('plan', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='subscriptions', to='core.subscriptionplan')),
            ],
            options={
                'verbose_name': 'Company Subscription',
                'verbose_name_plural': 'Company Subscriptions',
                'db_table': 'company_subscriptions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BillingInvoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=50, unique=True)),
                ('billing_period_start', models.DateField()),
                ('billing_period_end', models.DateField()),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=10)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='KSH', max_length=3)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('paid', 'Paid'), ('overdue', 'Overdue'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('due_date', models.DateField()),
                ('paid_date', models.DateField(blank=True, null=True)),
                ('payment_reference', models.CharField(blank=True, max_length=100, null=True)),
                ('payment_method', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invoices', to='core.companysubscription')),
            ],
            options={
                'verbose_name': 'Billing Invoice',
                'verbose_name_plural': 'Billing Invoices',
                'db_table': 'billing_invoices',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='EAPAccessLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('access_type', models.CharField(choices=[('view', 'View'), ('download', 'Download'), ('contact', 'Contact')], max_length=20)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('accessed_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('employee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='eap_accesses', to=settings.AUTH_USER_MODEL)),
                ('resource', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='access_logs', to='core.eapresource')),
            ],
            options={
                'verbose_name': 'EAP Access Log',
                'verbose_name_plural': 'EAP Access Logs',
                'db_table': 'eap_access_logs',
                'ordering': ['-accessed_at'],
            },
        ),
        migrations.CreateModel(
            name='FeatureToggle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('feature_name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_enabled', models.BooleanField(default=False)),
                ('rollout_percentage', models.IntegerField(default=0)),
                ('target_roles', models.TextField(blank=True, null=True)),
                ('target_departments', models.TextField(blank=True, null=True)),
                ('environment', models.CharField(choices=[('development', 'Development'), ('staging', 'Staging'), ('production', 'Production')], default='production', max_length=20)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_feature_toggles', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Feature Toggle',
                'verbose_name_plural': 'Feature Toggles',
                'db_table': 'feature_toggles',
                'ordering': ['feature_name'],
            },
        ),
        migrations.CreateModel(
            name='SavedReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('report_type', models.CharField(choices=[('payroll', 'Payroll'), ('attendance', 'Attendance'), ('leave', 'Leave'), ('performance', 'Performance'), ('custom', 'Custom')], max_length=50)),
                ('parameters', models.JSONField(blank=True, null=True)),
                ('schedule_frequency', models.CharField(blank=True, choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('yearly', 'Yearly')], max_length=20, null=True)),
                ('schedule_day', models.IntegerField(blank=True, null=True)),
                ('schedule_time', models.TimeField(blank=True, null=True)),
                ('recipients', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('last_generated', models.DateTimeField(blank=True, null=True)),
                ('next_generation', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_reports', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Saved Report',
                'verbose_name_plural': 'Saved Reports',
                'db_table': 'saved_reports',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReportHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_path', models.TextField(blank=True, null=True)),
                ('file_size', models.BigIntegerField(blank=True, null=True)),
                ('generation_time_seconds', models.IntegerField(blank=True, null=True)),
                ('parameters_used', models.JSONField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('generating', 'Generating'), ('completed', 'Completed'), ('failed', 'Failed')], default='completed', max_length=20)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('generated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='generated_reports', to=settings.AUTH_USER_MODEL)),
                ('saved_report', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='history', to='core.savedreport')),
            ],
            options={
                'verbose_name': 'Report History',
                'verbose_name_plural': 'Report History',
                'db_table': 'report_history',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WellnessActivityLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_date', models.DateField()),
                ('activity_value', models.IntegerField()),
                ('notes', models.TextField(blank=True, null=True)),
                ('data_source', models.CharField(choices=[('manual', 'Manual'), ('fitness_app', 'Fitness App'), ('device_sync', 'Device Sync')], default='manual', max_length=50)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('participant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activity_logs', to='core.wellnessprogramenrollment')),
            ],
            options={
                'verbose_name': 'Wellness Activity Log',
                'verbose_name_plural': 'Wellness Activity Logs',
                'db_table': 'wellness_activity_logs',
                'ordering': ['-activity_date'],
            },
        ),
        migrations.CreateModel(
            name='WorkflowStepExecution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('step_number', models.IntegerField()),
                ('step_name', models.CharField(max_length=255)),
                ('action_type', models.CharField(choices=[('approval', 'Approval'), ('review', 'Review'), ('notification', 'Notification'), ('auto_action', 'Auto Action')], max_length=50)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('skipped', 'Skipped'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('action_taken', models.CharField(blank=True, choices=[('approved', 'Approved'), ('rejected', 'Rejected'), ('escalated', 'Escalated'), ('delegated', 'Delegated')], max_length=50, null=True)),
                ('comments', models.TextField(blank=True, null=True)),
                ('due_date', models.DateTimeField(blank=True, null=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('escalated_at', models.DateTimeField(blank=True, null=True)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_workflow_steps', to=settings.AUTH_USER_MODEL)),
                ('escalated_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='escalated_workflow_steps', to=settings.AUTH_USER_MODEL)),
                ('workflow_instance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='step_executions', to='core.workflowinstance')),
            ],
            options={
                'verbose_name': 'Workflow Step Execution',
                'verbose_name_plural': 'Workflow Step Executions',
                'db_table': 'workflow_step_executions',
                'ordering': ['workflow_instance', 'step_number'],
            },
        ),
        migrations.CreateModel(
            name='EventParticipant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('registration_status', models.CharField(choices=[('registered', 'Registered'), ('confirmed', 'Confirmed'), ('attended', 'Attended'), ('absent', 'Absent'), ('cancelled', 'Cancelled')], default='registered', max_length=20)),
                ('attendance_status', models.CharField(choices=[('pending', 'Pending'), ('present', 'Present'), ('absent', 'Absent'), ('late', 'Late')], default='pending', max_length=20)),
                ('feedback_rating', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('feedback_comments', models.TextField(blank=True, null=True)),
                ('registered_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='event_participations', to=settings.AUTH_USER_MODEL)),
                ('event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='participants', to='core.companyevent')),
            ],
            options={
                'verbose_name': 'Event Participant',
                'verbose_name_plural': 'Event Participants',
                'db_table': 'event_participants',
                'ordering': ['-registered_at'],
                'unique_together': {('event', 'employee')},
            },
        ),
        migrations.CreateModel(
            name='ModuleSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('module_name', models.CharField(max_length=50)),
                ('setting_key', models.CharField(max_length=100)),
                ('setting_value', models.TextField(blank=True, null=True)),
                ('setting_type', models.CharField(choices=[('string', 'String'), ('integer', 'Integer'), ('boolean', 'Boolean'), ('json', 'JSON'), ('decimal', 'Decimal')], default='string', max_length=20)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_required', models.BooleanField(default=False)),
                ('default_value', models.TextField(blank=True, null=True)),
                ('validation_rules', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_module_settings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Module Setting',
                'verbose_name_plural': 'Module Settings',
                'db_table': 'module_settings',
                'ordering': ['module_name', 'setting_key'],
                'unique_together': {('module_name', 'setting_key')},
            },
        ),
    ]
