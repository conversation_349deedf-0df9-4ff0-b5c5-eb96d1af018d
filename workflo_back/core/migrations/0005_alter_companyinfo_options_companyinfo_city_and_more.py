# Generated by Django 5.2 on 2025-06-01 17:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0004_eapresource_subscriptionplan_alter_emaillog_options_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='companyinfo',
            options={'verbose_name': 'Company Information', 'verbose_name_plural': 'Company Information'},
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='city',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='company_size',
            field=models.CharField(blank=True, choices=[('1-10', '1-10 employees'), ('11-50', '11-50 employees'), ('51-200', '51-200 employees'), ('201-500', '201-500 employees'), ('501-1000', '501-1000 employees'), ('1000+', '1000+ employees')], max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='country',
            field=models.CharField(default='Kenya', max_length=100),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='fiscal_year_start_month',
            field=models.IntegerField(default=1),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='founded_year',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='industry',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='logo',
            field=models.ImageField(blank=True, null=True, upload_to='company/logos/'),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='mission_statement',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='postal_code',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='registration_number',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='state',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='tax_id',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_company_info', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='values',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='vision_statement',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='website',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='working_days_per_week',
            field=models.IntegerField(default=5),
        ),
    ]
