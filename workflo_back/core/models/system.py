# System & Audit Models
# Based on system modules from database.txt

from django.db import models
from django.utils import timezone as django_timezone
from .auth import User

# Placeholder for system models
# Will be implemented with full CRUD operations

class AuditLog(models.Model):
    """Audit logs placeholder"""
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    action = models.CharField(max_length=50)
    table_name = models.CharField(max_length=100)
    record_id = models.IntegerField(null=True, blank=True)
    old_values = models.JSONField(null=True, blank=True)
    new_values = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(default=django_timezone.now)
    
    class Meta:
        db_table = 'audit_logs'
    
    def __str__(self):
        return f"{self.user} - {self.action} - {self.table_name}"


class ActivityLog(models.Model):
    """Activity logs placeholder"""
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    activity_type = models.CharField(max_length=50)
    activity_description = models.TextField()
    module = models.CharField(max_length=50, blank=True, null=True)
    created_at = models.DateTimeField(default=django_timezone.now)
    
    class Meta:
        db_table = 'activity_logs'
    
    def __str__(self):
        return f"{self.user} - {self.activity_type}"


class SystemSetting(models.Model):
    """System settings placeholder"""
    setting_key = models.CharField(max_length=100, unique=True)
    setting_value = models.TextField(blank=True, null=True)
    setting_type = models.CharField(max_length=20, default='string')
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(default=django_timezone.now)
    
    class Meta:
        db_table = 'system_settings'
    
    def __str__(self):
        return self.setting_key


class CompanyInfo(models.Model):
    """Enhanced Company Information model with full CRUD capabilities"""

    COMPANY_SIZE_CHOICES = [
        ('1-10', '1-10 employees'),
        ('11-50', '11-50 employees'),
        ('51-200', '51-200 employees'),
        ('201-500', '201-500 employees'),
        ('501-1000', '501-1000 employees'),
        ('1000+', '1000+ employees'),
    ]

    # Basic Information
    company_name = models.CharField(max_length=255)
    logo = models.ImageField(upload_to='company/logos/', blank=True, null=True)
    description = models.TextField(blank=True, null=True)

    # Address Information
    address = models.TextField(blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    state = models.CharField(max_length=100, blank=True, null=True)
    postal_code = models.CharField(max_length=20, blank=True, null=True)
    country = models.CharField(max_length=100, default='Kenya')

    # Contact Information
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    website = models.URLField(blank=True, null=True)

    # Legal Information
    tax_id = models.CharField(max_length=50, blank=True, null=True)
    registration_number = models.CharField(max_length=50, blank=True, null=True)

    # Business Information
    industry = models.CharField(max_length=100, blank=True, null=True)
    company_size = models.CharField(max_length=20, choices=COMPANY_SIZE_CHOICES, blank=True, null=True)
    founded_year = models.IntegerField(blank=True, null=True)

    # Mission & Vision
    mission_statement = models.TextField(blank=True, null=True)
    vision_statement = models.TextField(blank=True, null=True)
    values = models.TextField(blank=True, null=True)

    # System Configuration
    timezone = models.CharField(max_length=50, default='Africa/Nairobi')
    currency = models.CharField(max_length=3, default='KSH')
    fiscal_year_start_month = models.IntegerField(default=1)
    working_days_per_week = models.IntegerField(default=5)

    # Audit fields
    created_at = models.DateTimeField(default=django_timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_company_info')

    class Meta:
        db_table = 'company_info'
        verbose_name = 'Company Information'
        verbose_name_plural = 'Company Information'

    def __str__(self):
        return self.company_name
