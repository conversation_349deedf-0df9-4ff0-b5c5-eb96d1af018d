# Employee Engagement & Wellness Models
# Based on engagement modules from database.txt

from django.db import models
from django.utils import timezone
from .auth import User


class Employee<PERSON><PERSON><PERSON>(models.Model):
    """
    Employee surveys and feedback
    Based on employee_surveys table from database.txt
    """
    SURVEY_TYPE_CHOICES = [
        ('pulse', 'Pulse'),
        ('engagement', 'Engagement'),
        ('satisfaction', 'Satisfaction'),
        ('exit', 'Exit'),
        ('feedback', 'Feedback'),
        ('culture', 'Culture'),
    ]

    TARGET_AUDIENCE_CHOICES = [
        ('all', 'All'),
        ('department', 'Department'),
        ('role', 'Role'),
        ('custom', 'Custom'),
    ]

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('paused', 'Paused'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    survey_type = models.CharField(max_length=50, choices=SURVEY_TYPE_CHOICES)
    questions = models.JSONField()  # Array of questions with types and options
    target_audience = models.CharField(max_length=20, choices=TARGET_AUDIENCE_CHOICES, default='all')
    department_ids = models.TextField(blank=True, null=True)  # JSON string for SQLite compatibility
    role_filters = models.TextField(blank=True, null=True)  # JSON string for SQLite compatibility
    employee_ids = models.TextField(blank=True, null=True)  # JSON string for SQLite compatibility
    is_anonymous = models.BooleanField(default=True)
    start_date = models.DateField()
    end_date = models.DateField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    response_count = models.IntegerField(default=0)
    target_count = models.IntegerField(default=0)

    # Audit fields
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_surveys')

    class Meta:
        db_table = 'employee_surveys'
        verbose_name = 'Employee Survey'
        verbose_name_plural = 'Employee Surveys'
        ordering = ['-created_at']

    def __str__(self):
        return self.title


class SurveyResponse(models.Model):
    """
    Survey responses
    Based on survey_responses table from database.txt
    """
    survey = models.ForeignKey(EmployeeSurvey, on_delete=models.CASCADE, related_name='responses')
    employee = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='survey_responses')  # NULL if anonymous
    responses = models.JSONField()  # Question ID -> Answer mapping
    completion_time_seconds = models.IntegerField(null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    submitted_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'survey_responses'
        verbose_name = 'Survey Response'
        verbose_name_plural = 'Survey Responses'
        unique_together = ['survey', 'employee']  # Prevent duplicate responses unless anonymous
        ordering = ['-submitted_at']

    def __str__(self):
        employee_name = self.employee.get_full_name() if self.employee else "Anonymous"
        return f"{self.survey.title} - {employee_name}"


class RecognitionCategory(models.Model):
    """
    Recognition and rewards categories
    Based on recognition_categories table from database.txt
    """
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    icon = models.CharField(max_length=50, blank=True, null=True)
    points_value = models.IntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'recognition_categories'
        verbose_name = 'Recognition Category'
        verbose_name_plural = 'Recognition Categories'
        ordering = ['name']

    def __str__(self):
        return self.name


class EmployeeRecognition(models.Model):
    """
    Employee recognition records
    Based on employee_recognitions table from database.txt
    """
    RECOGNITION_TYPE_CHOICES = [
        ('peer_to_peer', 'Peer to Peer'),
        ('manager_to_employee', 'Manager to Employee'),
        ('milestone', 'Milestone'),
        ('achievement', 'Achievement'),
        ('system', 'System'),
    ]

    MILESTONE_TYPE_CHOICES = [
        ('work_anniversary', 'Work Anniversary'),
        ('birthday', 'Birthday'),
        ('project_completion', 'Project Completion'),
        ('goal_achievement', 'Goal Achievement'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]

    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='recognitions_received')
    nominator = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='recognitions_given')  # NULL for system-generated
    category = models.ForeignKey(RecognitionCategory, on_delete=models.SET_NULL, null=True, blank=True, related_name='recognitions')
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    recognition_type = models.CharField(max_length=50, choices=RECOGNITION_TYPE_CHOICES)
    points_awarded = models.IntegerField(default=0)
    is_public = models.BooleanField(default=True)
    milestone_type = models.CharField(max_length=50, choices=MILESTONE_TYPE_CHOICES, blank=True, null=True)
    achievement_date = models.DateField(default=timezone.now)
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_recognitions')
    approved_at = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='approved')
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'employee_recognitions'
        verbose_name = 'Employee Recognition'
        verbose_name_plural = 'Employee Recognitions'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.recipient.get_full_name()}"


class WellnessProgram(models.Model):
    """
    Wellness programs and challenges
    Based on wellness_programs table from database.txt
    """
    PROGRAM_TYPE_CHOICES = [
        ('fitness_challenge', 'Fitness Challenge'),
        ('mental_health', 'Mental Health'),
        ('nutrition', 'Nutrition'),
        ('step_counter', 'Step Counter'),
        ('meditation', 'Meditation'),
        ('health_screening', 'Health Screening'),
    ]

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    program_type = models.CharField(max_length=50, choices=PROGRAM_TYPE_CHOICES)
    start_date = models.DateField()
    end_date = models.DateField()
    goal_type = models.CharField(max_length=50, blank=True, null=True)  # 'steps', 'hours', 'points', 'completion'
    goal_target = models.IntegerField(null=True, blank=True)
    reward_points = models.IntegerField(default=0)
    reward_description = models.TextField(blank=True, null=True)
    is_team_based = models.BooleanField(default=False)
    max_participants = models.IntegerField(null=True, blank=True)
    current_participants = models.IntegerField(default=0)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    created_at = models.DateTimeField(default=timezone.now)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_wellness_programs')

    class Meta:
        db_table = 'wellness_programs'
        verbose_name = 'Wellness Program'
        verbose_name_plural = 'Wellness Programs'
        ordering = ['-created_at']

    def __str__(self):
        return self.title


class WellnessProgramEnrollment(models.Model):
    """
    Wellness program participants
    Based on wellness_participants table from database.txt
    """
    program = models.ForeignKey(WellnessProgram, on_delete=models.CASCADE, related_name='participants')
    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='wellness_enrollments')
    team_name = models.CharField(max_length=100, blank=True, null=True)
    current_progress = models.IntegerField(default=0)
    goal_achieved = models.BooleanField(default=False)
    points_earned = models.IntegerField(default=0)
    joined_at = models.DateTimeField(default=timezone.now)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'wellness_participants'
        verbose_name = 'Wellness Program Enrollment'
        verbose_name_plural = 'Wellness Program Enrollments'
        unique_together = ['program', 'employee']
        ordering = ['-joined_at']

    def __str__(self):
        return f"{self.employee.get_full_name()} - {self.program.title}"


class WellnessActivityLog(models.Model):
    """
    Wellness activity logs
    Based on wellness_activity_logs table from database.txt
    """
    DATA_SOURCE_CHOICES = [
        ('manual', 'Manual'),
        ('fitness_app', 'Fitness App'),
        ('device_sync', 'Device Sync'),
    ]

    participant = models.ForeignKey(WellnessProgramEnrollment, on_delete=models.CASCADE, related_name='activity_logs')
    activity_date = models.DateField()
    activity_value = models.IntegerField()  # steps, hours, points, etc.
    notes = models.TextField(blank=True, null=True)
    data_source = models.CharField(max_length=50, choices=DATA_SOURCE_CHOICES, default='manual')
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'wellness_activity_logs'
        verbose_name = 'Wellness Activity Log'
        verbose_name_plural = 'Wellness Activity Logs'
        ordering = ['-activity_date']

    def __str__(self):
        return f"{self.participant.employee.get_full_name()} - {self.activity_date} - {self.activity_value}"


class EAPResource(models.Model):
    """
    Employee assistance programs (EAP) resources
    Based on eap_resources table from database.txt
    """
    RESOURCE_TYPE_CHOICES = [
        ('article', 'Article'),
        ('video', 'Video'),
        ('webinar', 'Webinar'),
        ('contact', 'Contact'),
        ('external_link', 'External Link'),
        ('document', 'Document'),
    ]

    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    category = models.CharField(max_length=100, blank=True, null=True)  # 'mental_health', 'financial_wellness', etc.
    resource_type = models.CharField(max_length=50, choices=RESOURCE_TYPE_CHOICES)
    content_url = models.TextField(blank=True, null=True)
    contact_info = models.TextField(blank=True, null=True)
    is_confidential = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    access_count = models.IntegerField(default=0)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'eap_resources'
        verbose_name = 'EAP Resource'
        verbose_name_plural = 'EAP Resources'
        ordering = ['title']

    def __str__(self):
        return self.title


class EAPAccessLog(models.Model):
    """
    EAP resource access logs
    Based on eap_access_logs table from database.txt
    """
    ACCESS_TYPE_CHOICES = [
        ('view', 'View'),
        ('download', 'Download'),
        ('contact', 'Contact'),
    ]

    resource = models.ForeignKey(EAPResource, on_delete=models.CASCADE, related_name='access_logs')
    employee = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='eap_accesses')  # NULL for anonymous
    access_type = models.CharField(max_length=20, choices=ACCESS_TYPE_CHOICES)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True, null=True)
    accessed_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'eap_access_logs'
        verbose_name = 'EAP Access Log'
        verbose_name_plural = 'EAP Access Logs'
        ordering = ['-accessed_at']

    def __str__(self):
        employee_name = self.employee.get_full_name() if self.employee else "Anonymous"
        return f"{self.resource.title} - {employee_name} - {self.access_type}"


class EmployeeFeedback(models.Model):
    """
    General employee feedback
    Custom model for employee feedback collection
    """
    FEEDBACK_TYPE_CHOICES = [
        ('suggestion', 'Suggestion'),
        ('complaint', 'Complaint'),
        ('compliment', 'Compliment'),
        ('general', 'General'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('reviewed', 'Reviewed'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
    ]

    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='feedback_given')
    feedback_type = models.CharField(max_length=20, choices=FEEDBACK_TYPE_CHOICES)
    subject = models.CharField(max_length=255)
    message = models.TextField()
    is_anonymous = models.BooleanField(default=False)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='feedback_reviewed')
    reviewed_at = models.DateTimeField(null=True, blank=True)
    response = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'employee_feedback'
        verbose_name = 'Employee Feedback'
        verbose_name_plural = 'Employee Feedback'
        ordering = ['-created_at']

    def __str__(self):
        employee_name = self.employee.get_full_name() if not self.is_anonymous else "Anonymous"
        return f"{self.subject} - {employee_name}"
