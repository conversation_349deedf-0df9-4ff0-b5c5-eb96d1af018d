# Notifications & Communication Models
# Based on notification modules from database.txt

from django.db import models
from django.utils import timezone
# from django.contrib.postgres.fields import ArrayField  # PostgreSQL specific
from .auth import User

# Placeholder for notification models
# Will be implemented with full CRUD operations

class NotificationTemplate(models.Model):
    """Enhanced notification template model"""
    NOTIFICATION_TYPE_CHOICES = [
        ('system', 'System'),
        ('workflow', 'Workflow'),
        ('reminder', 'Reminder'),
        ('announcement', 'Announcement'),
        ('alert', 'Alert'),
    ]

    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    notification_type = models.CharField(max_length=50, choices=NOTIFICATION_TYPE_CHOICES)
    subject = models.Char<PERSON>ield(max_length=255)
    body = models.TextField()
    template_variables = models.JSONField(default=list, blank=True)  # List of variable names
    is_active = models.Boolean<PERSON>ield(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_notification_templates')

    class Meta:
        db_table = 'notification_templates'
        ordering = ['name']

    def __str__(self):
        return self.name


class Notification(models.Model):
    """Enhanced notification model"""
    NOTIFICATION_TYPE_CHOICES = [
        ('system', 'System'),
        ('workflow', 'Workflow'),
        ('reminder', 'Reminder'),
        ('announcement', 'Announcement'),
        ('alert', 'Alert'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    sender = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='sent_notifications')
    template = models.ForeignKey(NotificationTemplate, on_delete=models.SET_NULL, null=True, blank=True, related_name='notifications')
    title = models.CharField(max_length=255)
    message = models.TextField()
    notification_type = models.CharField(max_length=50, choices=NOTIFICATION_TYPE_CHOICES, default='system')
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='medium')
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)
    sent_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'notifications'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.recipient} - {self.title}"


class EmailLog(models.Model):
    """Enhanced email log model"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
        ('bounced', 'Bounced'),
    ]

    recipient = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='email_logs')
    template = models.ForeignKey(NotificationTemplate, on_delete=models.SET_NULL, null=True, blank=True, related_name='email_logs')
    recipient_email = models.EmailField()
    sender_email = models.EmailField(blank=True, null=True)
    subject = models.CharField(max_length=255)
    body = models.TextField(blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    error_message = models.TextField(blank=True, null=True)
    sent_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'email_logs'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.recipient_email} - {self.subject}"
