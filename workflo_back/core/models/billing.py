# Company Management & Billing Models
# Based on billing modules from database.txt

from django.db import models
from django.utils import timezone
from .auth import User


class SubscriptionPlan(models.Model):
    """
    Company subscription plans
    Based on subscription_plans table from database.txt
    """
    BILLING_CYCLE_CHOICES = [
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
        ('yearly', 'Yearly'),
    ]
    
    plan_name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    max_employees = models.IntegerField(null=True, blank=True)
    features = models.JSONField()  # Array of included features
    price_per_employee = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    price_per_month = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    billing_cycle = models.CharField(max_length=20, choices=BILLING_CYCLE_CHOICES)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        db_table = 'subscription_plans'
        verbose_name = 'Subscription Plan'
        verbose_name_plural = 'Subscription Plans'
        ordering = ['plan_name']
    
    def __str__(self):
        return self.plan_name


class CompanySubscription(models.Model):
    """
    Company subscriptions
    Based on company_subscriptions table from database.txt
    """
    STATUS_CHOICES = [
        ('trial', 'Trial'),
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('cancelled', 'Cancelled'),
        ('expired', 'Expired'),
    ]
    
    plan = models.ForeignKey(SubscriptionPlan, on_delete=models.PROTECT, related_name='subscriptions')
    start_date = models.DateField()
    end_date = models.DateField()
    employee_count = models.IntegerField()
    monthly_cost = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    auto_renew = models.BooleanField(default=True)
    payment_method = models.CharField(max_length=50, blank=True, null=True)
    billing_contact = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='billing_subscriptions')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'company_subscriptions'
        verbose_name = 'Company Subscription'
        verbose_name_plural = 'Company Subscriptions'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.plan.plan_name} - {self.status}"


class BillingInvoice(models.Model):
    """
    Billing invoices
    Based on billing_invoices table from database.txt
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('paid', 'Paid'),
        ('overdue', 'Overdue'),
        ('cancelled', 'Cancelled'),
    ]
    
    subscription = models.ForeignKey(CompanySubscription, on_delete=models.CASCADE, related_name='invoices')
    invoice_number = models.CharField(max_length=50, unique=True)
    billing_period_start = models.DateField()
    billing_period_end = models.DateField()
    subtotal = models.DecimalField(max_digits=10, decimal_places=2)
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='KSH')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    due_date = models.DateField()
    paid_date = models.DateField(null=True, blank=True)
    payment_reference = models.CharField(max_length=100, blank=True, null=True)
    payment_method = models.CharField(max_length=50, blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        db_table = 'billing_invoices'
        verbose_name = 'Billing Invoice'
        verbose_name_plural = 'Billing Invoices'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Invoice {self.invoice_number} - {self.total_amount} {self.currency}"


class ModuleSetting(models.Model):
    """
    Module settings and configurations
    Based on module_settings table from database.txt
    """
    SETTING_TYPE_CHOICES = [
        ('string', 'String'),
        ('integer', 'Integer'),
        ('boolean', 'Boolean'),
        ('json', 'JSON'),
        ('decimal', 'Decimal'),
    ]
    
    module_name = models.CharField(max_length=50)  # 'payroll', 'attendance', 'leave', etc.
    setting_key = models.CharField(max_length=100)
    setting_value = models.TextField(blank=True, null=True)
    setting_type = models.CharField(max_length=20, choices=SETTING_TYPE_CHOICES, default='string')
    description = models.TextField(blank=True, null=True)
    is_required = models.BooleanField(default=False)
    default_value = models.TextField(blank=True, null=True)
    validation_rules = models.JSONField(null=True, blank=True)  # Validation rules for the setting
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_module_settings')
    
    class Meta:
        db_table = 'module_settings'
        verbose_name = 'Module Setting'
        verbose_name_plural = 'Module Settings'
        unique_together = ['module_name', 'setting_key']
        ordering = ['module_name', 'setting_key']
    
    def __str__(self):
        return f"{self.module_name}.{self.setting_key}"


class FeatureToggle(models.Model):
    """
    Feature toggles
    Based on feature_toggles table from database.txt
    """
    ENVIRONMENT_CHOICES = [
        ('development', 'Development'),
        ('staging', 'Staging'),
        ('production', 'Production'),
    ]
    
    feature_name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    is_enabled = models.BooleanField(default=False)
    rollout_percentage = models.IntegerField(default=0)  # 0-100
    target_roles = models.TextField(blank=True, null=True)  # JSON string for SQLite compatibility
    target_departments = models.TextField(blank=True, null=True)  # JSON string for SQLite compatibility
    environment = models.CharField(max_length=20, choices=ENVIRONMENT_CHOICES, default='production')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_feature_toggles')
    
    class Meta:
        db_table = 'feature_toggles'
        verbose_name = 'Feature Toggle'
        verbose_name_plural = 'Feature Toggles'
        ordering = ['feature_name']
    
    def __str__(self):
        return f"{self.feature_name} ({'Enabled' if self.is_enabled else 'Disabled'})"
