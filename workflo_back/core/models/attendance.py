from django.db import models
from django.utils import timezone
# from django.contrib.postgres.fields import ArrayField  # PostgreSQL specific
from .auth import User


class AttendanceRecord(models.Model):
    """
    Attendance records for employees
    Based on attendance_records table from database.txt
    """
    STATUS_CHOICES = [
        ('present', 'Present'),
        ('absent', 'Absent'),
        ('late', 'Late'),
        ('early_out', 'Early Out'),
        ('half_day', 'Half Day'),
    ]
    
    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='attendance_records')
    date = models.DateField()
    check_in = models.DateTimeField(null=True, blank=True)
    check_out = models.DateTimeField(null=True, blank=True)
    break_start = models.DateTimeField(null=True, blank=True)
    break_end = models.DateTimeField(null=True, blank=True)
    total_hours = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    regular_hours = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    overtime_hours = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    break_time = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='present')
    notes = models.TextField(blank=True, null=True)
    biostar_synced = models.BooleanField(default=False)
    biostar_event_ids = models.TextField(blank=True, null=True)  # JSON string for SQLite compatibility
    
    # Audit fields
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'attendance_records'
        verbose_name = 'Attendance Record'
        verbose_name_plural = 'Attendance Records'
        unique_together = ['employee', 'date']
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.employee.first_name} {self.employee.last_name} - {self.date}"
    
    def calculate_hours(self):
        """Calculate total hours, regular hours, and overtime hours"""
        if self.check_in and self.check_out:
            total_time = self.check_out - self.check_in
            total_hours = total_time.total_seconds() / 3600

            # Subtract break time if available
            if self.break_start and self.break_end:
                break_time = self.break_end - self.break_start
                break_hours = break_time.total_seconds() / 3600
                total_hours -= break_hours
                self.break_time = break_hours

            self.total_hours = total_hours

            # Get standard work hours from settings
            from django.conf import settings
            standard_hours = getattr(settings, 'DYNAMIC_SALARY_CONFIG', {}).get('STANDARD_WORK_HOURS', 8)

            # Calculate regular and overtime hours
            if total_hours <= standard_hours:
                self.regular_hours = total_hours
                self.overtime_hours = 0
            else:
                self.regular_hours = standard_hours
                self.overtime_hours = total_hours - standard_hours

            # Update status based on hours worked
            if total_hours >= standard_hours:
                self.status = 'present'
            elif total_hours >= (standard_hours * 0.5):
                self.status = 'half_day'
            else:
                self.status = 'late'

            self.save()

    def is_late(self, standard_start_time='09:00'):
        """Check if employee was late"""
        if not self.check_in:
            return False

        # Parse standard start time
        hour, minute = map(int, standard_start_time.split(':'))
        standard_start = self.check_in.replace(hour=hour, minute=minute, second=0, microsecond=0)

        return self.check_in > standard_start

    def get_overtime_eligibility(self):
        """Check if overtime hours are eligible for payment"""
        if not self.overtime_hours or self.overtime_hours <= 0:
            return {'eligible': False, 'reason': 'No overtime hours'}

        # Check if it's a weekend or holiday
        is_weekend = self.date.weekday() >= 5

        # Check for company holidays
        from ..models.leave import CompanyHoliday
        is_holiday = CompanyHoliday.objects.filter(date=self.date).exists()

        return {
            'eligible': True,
            'overtime_hours': self.overtime_hours,
            'is_weekend': is_weekend,
            'is_holiday': is_holiday,
            'requires_approval': True
        }


class BiostarEvent(models.Model):
    """
    BioStar integration events
    Based on biostar_events table from database.txt
    """
    EVENT_TYPE_CHOICES = [
        ('ENTRY', 'Entry'),
        ('EXIT', 'Exit'),
        ('DENIED', 'Denied'),
    ]
    
    biostar_event_id = models.CharField(max_length=100, unique=True)
    employee = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='biostar_events')
    device_id = models.CharField(max_length=100, blank=True, null=True)
    device_name = models.CharField(max_length=255, blank=True, null=True)
    event_type = models.CharField(max_length=20, choices=EVENT_TYPE_CHOICES)
    event_datetime = models.DateTimeField()
    location = models.CharField(max_length=255, blank=True, null=True)
    processed = models.BooleanField(default=False)
    attendance_record = models.ForeignKey(AttendanceRecord, on_delete=models.SET_NULL, null=True, blank=True, related_name='biostar_events')
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        db_table = 'biostar_events'
        verbose_name = 'BioStar Event'
        verbose_name_plural = 'BioStar Events'
        ordering = ['-event_datetime']
    
    def __str__(self):
        return f"{self.biostar_event_id} - {self.event_type} - {self.event_datetime}"


class BiostarDevice(models.Model):
    """
    BioStar devices
    Based on biostar_devices table from database.txt
    """
    STATUS_CHOICES = [
        ('online', 'Online'),
        ('offline', 'Offline'),
        ('maintenance', 'Maintenance'),
    ]
    
    biostar_device_id = models.CharField(max_length=100, unique=True)
    name = models.CharField(max_length=255)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    port = models.IntegerField(null=True, blank=True)
    location = models.CharField(max_length=255, blank=True, null=True)
    device_type = models.CharField(max_length=100, blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='offline')
    last_seen = models.DateTimeField(null=True, blank=True)
    
    # Audit fields
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'biostar_devices'
        verbose_name = 'BioStar Device'
        verbose_name_plural = 'BioStar Devices'
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.biostar_device_id})"
