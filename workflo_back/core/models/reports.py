# Reports & Analytics Models
# Based on reports modules from database.txt

from django.db import models
from django.utils import timezone
from .auth import User


class SavedReport(models.Model):
    """
    Saved reports
    Based on saved_reports table from database.txt
    """
    REPORT_TYPE_CHOICES = [
        ('payroll', 'Payroll'),
        ('attendance', 'Attendance'),
        ('leave', 'Leave'),
        ('performance', 'Performance'),
        ('custom', 'Custom'),
    ]
    
    SCHEDULE_FREQUENCY_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
        ('yearly', 'Yearly'),
    ]
    
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    report_type = models.CharField(max_length=50, choices=REPORT_TYPE_CHOICES)
    parameters = models.JSONField(null=True, blank=True)  # Report parameters and filters
    schedule_frequency = models.CharField(max_length=20, choices=SCHEDULE_FREQUENCY_CHOICES, blank=True, null=True)
    schedule_day = models.IntegerField(null=True, blank=True)  # Day of week/month for scheduled reports
    schedule_time = models.TimeField(null=True, blank=True)
    recipients = models.TextField(blank=True, null=True)  # JSON string for SQLite compatibility - Array of user IDs
    is_active = models.BooleanField(default=True)
    last_generated = models.DateTimeField(null=True, blank=True)
    next_generation = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_reports')
    
    class Meta:
        db_table = 'saved_reports'
        verbose_name = 'Saved Report'
        verbose_name_plural = 'Saved Reports'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name


class ReportHistory(models.Model):
    """
    Report generation history
    Based on report_history table from database.txt
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('generating', 'Generating'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    
    saved_report = models.ForeignKey(SavedReport, on_delete=models.SET_NULL, null=True, blank=True, related_name='history')
    generated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='generated_reports')
    file_path = models.TextField(blank=True, null=True)
    file_size = models.BigIntegerField(null=True, blank=True)
    generation_time_seconds = models.IntegerField(null=True, blank=True)
    parameters_used = models.JSONField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='completed')
    error_message = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        db_table = 'report_history'
        verbose_name = 'Report History'
        verbose_name_plural = 'Report History'
        ordering = ['-created_at']
    
    def __str__(self):
        report_name = self.saved_report.name if self.saved_report else "Ad-hoc Report"
        return f"{report_name} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"
