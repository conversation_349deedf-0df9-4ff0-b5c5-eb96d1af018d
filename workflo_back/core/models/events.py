# Company Events & Culture Models
# Based on company events modules from database.txt

from django.db import models
from django.utils import timezone
from .auth import User


class CompanyEvent(models.Model):
    """
    Company events and culture activities
    Based on company_events table from database.txt
    """
    EVENT_TYPE_CHOICES = [
        ('meeting', 'Meeting'),
        ('training', 'Training'),
        ('social', 'Social'),
        ('team_building', 'Team Building'),
        ('celebration', 'Celebration'),
        ('announcement', 'Announcement'),
        ('wellness', 'Wellness'),
    ]
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('scheduled', 'Scheduled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    event_type = models.CharField(max_length=50, choices=EVENT_TYPE_CHOICES)
    start_datetime = models.DateTimeField()
    end_datetime = models.DateTimeField()
    location = models.CharField(max_length=255, blank=True, null=True)
    is_virtual = models.BooleanField(default=False)
    meeting_link = models.TextField(blank=True, null=True)
    max_participants = models.IntegerField(null=True, blank=True)
    current_participants = models.IntegerField(default=0)
    is_mandatory = models.BooleanField(default=False)
    department_ids = models.TextField(blank=True, null=True)  # JSON string for SQLite compatibility
    role_filters = models.TextField(blank=True, null=True)  # JSON string for SQLite compatibility
    organizer = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='organized_events')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='scheduled')
    registration_required = models.BooleanField(default=False)
    registration_deadline = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_events')
    
    class Meta:
        db_table = 'company_events'
        verbose_name = 'Company Event'
        verbose_name_plural = 'Company Events'
        ordering = ['-start_datetime']
    
    def __str__(self):
        return self.title


class EventParticipant(models.Model):
    """
    Event participants/registrations
    Based on event_participants table from database.txt
    """
    REGISTRATION_STATUS_CHOICES = [
        ('registered', 'Registered'),
        ('confirmed', 'Confirmed'),
        ('attended', 'Attended'),
        ('absent', 'Absent'),
        ('cancelled', 'Cancelled'),
    ]
    
    ATTENDANCE_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('present', 'Present'),
        ('absent', 'Absent'),
        ('late', 'Late'),
    ]
    
    event = models.ForeignKey(CompanyEvent, on_delete=models.CASCADE, related_name='participants')
    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='event_participations')
    registration_status = models.CharField(max_length=20, choices=REGISTRATION_STATUS_CHOICES, default='registered')
    attendance_status = models.CharField(max_length=20, choices=ATTENDANCE_STATUS_CHOICES, default='pending')
    feedback_rating = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    feedback_comments = models.TextField(blank=True, null=True)
    registered_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        db_table = 'event_participants'
        verbose_name = 'Event Participant'
        verbose_name_plural = 'Event Participants'
        unique_together = ['event', 'employee']
        ordering = ['-registered_at']
    
    def __str__(self):
        return f"{self.event.title} - {self.employee.get_full_name()}"
