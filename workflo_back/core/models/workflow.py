# Workflow Management Models
# Based on workflow modules from database.txt

from django.db import models
from django.utils import timezone
from .auth import User


class WorkflowDefinition(models.Model):
    """
    Workflow definitions (approval workflows, processes)
    Based on workflow_definitions table from database.txt
    """
    WORKFLOW_TYPE_CHOICES = [
        ('leave_approval', 'Leave Approval'),
        ('overtime_approval', 'Overtime Approval'),
        ('expense_approval', 'Expense Approval'),
        ('document_approval', 'Document Approval'),
        ('recruitment', 'Recruitment'),
        ('performance_review', 'Performance Review'),
        ('salary_adjustment', 'Salary Adjustment'),
        ('custom', 'Custom'),
    ]
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('archived', 'Archived'),
    ]
    
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    workflow_type = models.Char<PERSON><PERSON>(max_length=50, choices=WORKFLOW_TYPE_CHOICES)
    version = models.CharField(max_length=20, default='1.0')
    
    # Workflow configuration (JSON structure)
    workflow_steps = models.JSONField(default=list)  # List of workflow steps
    approval_rules = models.JSONField(default=dict)  # Approval rules and conditions
    escalation_rules = models.JSONField(default=dict)  # Escalation rules
    notification_settings = models.JSONField(default=dict)  # Notification configuration
    
    # Applicability
    department_ids = models.TextField(blank=True, null=True)  # JSON string for SQLite compatibility
    role_access = models.TextField(default='["all"]')  # JSON string for SQLite compatibility
    
    # Timing
    sla_hours = models.IntegerField(null=True, blank=True)  # Service Level Agreement in hours
    escalation_hours = models.IntegerField(null=True, blank=True)
    
    # Status and metadata
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    is_default = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_workflows')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_workflows')
    
    class Meta:
        db_table = 'workflow_definitions'
        verbose_name = 'Workflow Definition'
        verbose_name_plural = 'Workflow Definitions'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} (v{self.version})"


class WorkflowInstance(models.Model):
    """
    Workflow instances (actual workflow executions)
    Based on workflow_instances table from database.txt
    """
    STATUS_CHOICES = [
        ('initiated', 'Initiated'),
        ('in_progress', 'In Progress'),
        ('pending_approval', 'Pending Approval'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('cancelled', 'Cancelled'),
        ('completed', 'Completed'),
        ('escalated', 'Escalated'),
        ('expired', 'Expired'),
    ]
    
    workflow_definition = models.ForeignKey(WorkflowDefinition, on_delete=models.CASCADE, related_name='instances')
    
    # Request information
    initiated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='initiated_workflows')
    subject = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    
    # Related object (generic foreign key simulation)
    related_object_type = models.CharField(max_length=50, blank=True, null=True)  # e.g., 'leave_application', 'overtime_request'
    related_object_id = models.IntegerField(null=True, blank=True)
    
    # Workflow state
    current_step = models.IntegerField(default=0)
    current_approver = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='pending_approvals')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='initiated')
    
    # Workflow data
    workflow_data = models.JSONField(default=dict)  # Dynamic data for the workflow
    approval_history = models.JSONField(default=list)  # History of approvals/rejections
    
    # Timing
    initiated_at = models.DateTimeField(default=timezone.now)
    due_date = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    escalated_at = models.DateTimeField(null=True, blank=True)
    
    # Final outcome
    final_approver = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='final_approved_workflows')
    final_decision = models.CharField(max_length=20, blank=True, null=True)
    final_comments = models.TextField(blank=True, null=True)
    
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'workflow_instances'
        verbose_name = 'Workflow Instance'
        verbose_name_plural = 'Workflow Instances'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.workflow_definition.name} - {self.subject}"
    
    def get_current_step_name(self):
        """Get the name of the current workflow step"""
        if self.workflow_definition.workflow_steps and self.current_step < len(self.workflow_definition.workflow_steps):
            return self.workflow_definition.workflow_steps[self.current_step].get('name', f'Step {self.current_step + 1}')
        return 'Completed'
    
    def is_overdue(self):
        """Check if the workflow instance is overdue"""
        if self.due_date and self.status in ['initiated', 'in_progress', 'pending_approval']:
            return timezone.now() > self.due_date
        return False
    
    def get_next_approver(self):
        """Get the next approver based on workflow definition"""
        if self.workflow_definition.workflow_steps and self.current_step < len(self.workflow_definition.workflow_steps):
            step = self.workflow_definition.workflow_steps[self.current_step]
            approver_role = step.get('approver_role')
            if approver_role:
                # Logic to find approver based on role and department
                # This would be implemented based on business rules
                pass
        return None


class WorkflowStepExecution(models.Model):
    """
    Workflow step executions
    Based on workflow_step_executions table from database.txt
    """
    ACTION_TYPE_CHOICES = [
        ('approval', 'Approval'),
        ('review', 'Review'),
        ('notification', 'Notification'),
        ('auto_action', 'Auto Action'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('skipped', 'Skipped'),
        ('failed', 'Failed'),
    ]

    ACTION_TAKEN_CHOICES = [
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('escalated', 'Escalated'),
        ('delegated', 'Delegated'),
    ]

    workflow_instance = models.ForeignKey(WorkflowInstance, on_delete=models.CASCADE, related_name='step_executions')
    step_number = models.IntegerField()
    step_name = models.CharField(max_length=255)
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_workflow_steps')
    action_type = models.CharField(max_length=50, choices=ACTION_TYPE_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    action_taken = models.CharField(max_length=50, choices=ACTION_TAKEN_CHOICES, blank=True, null=True)
    comments = models.TextField(blank=True, null=True)
    due_date = models.DateTimeField(null=True, blank=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    escalated_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='escalated_workflow_steps')
    escalated_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'workflow_step_executions'
        verbose_name = 'Workflow Step Execution'
        verbose_name_plural = 'Workflow Step Executions'
        ordering = ['workflow_instance', 'step_number']

    def __str__(self):
        return f"{self.workflow_instance.subject} - Step {self.step_number}: {self.step_name}"


class AutomatedReminder(models.Model):
    """
    Automated reminders and notifications
    Based on automated_reminders table from database.txt
    """
    REMINDER_TYPE_CHOICES = [
        ('workflow_due', 'Workflow Due'),
        ('review_due', 'Review Due'),
        ('document_expiry', 'Document Expiry'),
        ('birthday', 'Birthday'),
        ('anniversary', 'Anniversary'),
    ]

    REPEAT_INTERVAL_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('yearly', 'Yearly'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('cancelled', 'Cancelled'),
        ('completed', 'Completed'),
    ]

    reminder_type = models.CharField(max_length=50, choices=REMINDER_TYPE_CHOICES)
    entity_type = models.CharField(max_length=50, blank=True, null=True)  # 'workflow_instance', 'performance_review', etc.
    entity_id = models.IntegerField(null=True, blank=True)
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='automated_reminders')
    title = models.CharField(max_length=255)
    message = models.TextField()
    reminder_datetime = models.DateTimeField()
    repeat_interval = models.CharField(max_length=20, choices=REPEAT_INTERVAL_CHOICES, blank=True, null=True)
    repeat_count = models.IntegerField(default=1)
    sent_count = models.IntegerField(default=0)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    last_sent_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'automated_reminders'
        verbose_name = 'Automated Reminder'
        verbose_name_plural = 'Automated Reminders'
        ordering = ['reminder_datetime']

    def __str__(self):
        return f"{self.title} - {self.recipient.get_full_name()}"
