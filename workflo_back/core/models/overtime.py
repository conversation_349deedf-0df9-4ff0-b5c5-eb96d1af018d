from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from .auth import User
from .organization import Department
from .attendance import AttendanceRecord


class OvertimeType(models.Model):
    """
    Overtime types and policies
    Based on overtime_types table from database.txt
    """
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    rate_multiplier = models.DecimalField(max_digits=3, decimal_places=2, default=1.5)  # e.g., 1.5 for time-and-a-half
    max_hours_per_day = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)
    max_hours_per_week = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)
    max_hours_per_month = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    requires_pre_approval = models.BooleanField(default=True)
    auto_approve_threshold = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)  # Auto-approve if under this many hours
    is_active = models.BooleanField(default=True)
    
    # Audit fields
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_overtime_types')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_overtime_types')
    
    class Meta:
        db_table = 'overtime_types'
        verbose_name = 'Overtime Type'
        verbose_name_plural = 'Overtime Types'
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.rate_multiplier}x)"


class OvertimeRequest(models.Model):
    """
    Overtime requests (pre-approval system)
    Based on overtime_requests table from database.txt
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('cancelled', 'Cancelled'),
        ('completed', 'Completed'),
    ]
    
    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='overtime_requests')
    overtime_type = models.ForeignKey(OvertimeType, on_delete=models.CASCADE, related_name='requests')
    
    # Request details
    request_date = models.DateField()
    planned_start_time = models.TimeField()
    planned_end_time = models.TimeField()
    planned_hours = models.DecimalField(max_digits=4, decimal_places=2)
    reason = models.TextField()
    justification = models.TextField(blank=True, null=True)
    project_code = models.CharField(max_length=50, blank=True, null=True)
    department_approval_required = models.BooleanField(default=True)
    
    # Approval workflow
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    requested_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='requested_overtime')
    supervisor_approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='supervisor_approved_overtime')
    supervisor_approved_at = models.DateTimeField(null=True, blank=True)
    supervisor_comments = models.TextField(blank=True, null=True)
    admin_approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='admin_approved_overtime')
    admin_approved_at = models.DateTimeField(null=True, blank=True)
    admin_comments = models.TextField(blank=True, null=True)
    
    # Final approval
    final_approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='final_approved_overtime')
    final_approved_at = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True, null=True)
    
    # Completion tracking
    actual_start_time = models.DateTimeField(null=True, blank=True)
    actual_end_time = models.DateTimeField(null=True, blank=True)
    actual_hours = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)
    completion_notes = models.TextField(blank=True, null=True)
    completed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='completed_overtime')
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # BioStar Integration
    auto_detected = models.BooleanField(default=False)  # True if detected from BioStar
    biostar_event_id = models.CharField(max_length=100, blank=True, null=True)  # Reference to BioStar event
    attendance_record = models.ForeignKey(AttendanceRecord, on_delete=models.SET_NULL, null=True, blank=True, related_name='overtime_requests')

    # Audit fields
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_overtime_requests')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_overtime_requests')
    
    class Meta:
        db_table = 'overtime_requests'
        verbose_name = 'Overtime Request'
        verbose_name_plural = 'Overtime Requests'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.employee.first_name} {self.employee.last_name} - {self.request_date} ({self.planned_hours}h)"
    
    def calculate_planned_hours(self):
        """Calculate planned hours from start and end times"""
        from datetime import datetime, timedelta

        # Convert time objects to datetime for calculation
        start_datetime = datetime.combine(self.request_date, self.planned_start_time)
        end_datetime = datetime.combine(self.request_date, self.planned_end_time)

        # Handle overnight shifts
        if end_datetime <= start_datetime:
            end_datetime += timedelta(days=1)

        duration = end_datetime - start_datetime
        self.planned_hours = duration.total_seconds() / 3600
        return self.planned_hours

    def auto_approve_if_eligible(self):
        """Auto-approve overtime request if it meets criteria"""
        if not self.auto_detected:
            return False

        # Check if overtime type allows auto-approval
        if (self.overtime_type.auto_approve_threshold and
            self.planned_hours <= self.overtime_type.auto_approve_threshold):
            self.status = 'approved'
            self.supervisor_approved_at = timezone.now()
            self.supervisor_comments = 'Auto-approved based on BioStar attendance data'
            self.save()
            return True

        return False

    def get_approval_status(self):
        """Get detailed approval status"""
        return {
            'status': self.status,
            'auto_detected': self.auto_detected,
            'supervisor_approved': bool(self.supervisor_approved_at),
            'admin_approved': bool(self.admin_approved_at),
            'final_approved': bool(self.final_approved_at),
            'requires_supervisor': self.department_approval_required,
            'can_auto_approve': (
                self.auto_detected and
                self.overtime_type.auto_approve_threshold and
                self.planned_hours <= self.overtime_type.auto_approve_threshold
            )
        }


class OvertimeRecord(models.Model):
    """
    Overtime records (actual overtime worked)
    Based on overtime_records table from database.txt
    """
    OVERTIME_CATEGORY_CHOICES = [
        ('weekday', 'Weekday'),
        ('weekend', 'Weekend'),
        ('holiday', 'Holiday'),
        ('emergency', 'Emergency'),
        ('project_deadline', 'Project Deadline'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('paid', 'Paid'),
    ]

    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='overtime_records')
    overtime_request = models.ForeignKey(OvertimeRequest, on_delete=models.SET_NULL, null=True, blank=True, related_name='records')
    overtime_type = models.ForeignKey(OvertimeType, on_delete=models.CASCADE, related_name='records')
    attendance_record = models.ForeignKey(AttendanceRecord, on_delete=models.SET_NULL, null=True, blank=True, related_name='overtime_records')

    # Overtime details
    overtime_date = models.DateField()
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    total_hours = models.DecimalField(max_digits=4, decimal_places=2)
    rate_multiplier = models.DecimalField(max_digits=3, decimal_places=2)

    # Calculation details
    regular_hourly_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    overtime_hourly_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    currency = models.CharField(max_length=3, default='KSH')

    # Classification
    overtime_category = models.CharField(max_length=30, choices=OVERTIME_CATEGORY_CHOICES, null=True, blank=True)
    is_emergency = models.BooleanField(default=False)
    is_pre_approved = models.BooleanField(default=False)

    # Approval status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_overtime_records')
    approved_at = models.DateTimeField(null=True, blank=True)
    approval_comments = models.TextField(blank=True, null=True)

    # Payroll integration
    pay_cycle = models.ForeignKey('PayCycle', on_delete=models.SET_NULL, null=True, blank=True, related_name='overtime_records')
    included_in_payroll = models.BooleanField(default=False)
    payroll_processed_at = models.DateTimeField(null=True, blank=True)

    # Audit fields
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_overtime_records')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_overtime_records')

    class Meta:
        db_table = 'overtime_records'
        verbose_name = 'Overtime Record'
        verbose_name_plural = 'Overtime Records'
        unique_together = ['employee', 'overtime_date', 'start_time']
        ordering = ['-overtime_date', '-start_time']

    def __str__(self):
        return f"{self.employee.first_name} {self.employee.last_name} - {self.overtime_date} ({self.total_hours}h)"

    def calculate_amount(self):
        """Calculate overtime amount based on hourly rate and multiplier"""
        if self.regular_hourly_rate and self.rate_multiplier:
            self.overtime_hourly_rate = self.regular_hourly_rate * self.rate_multiplier
            self.total_amount = self.overtime_hourly_rate * self.total_hours
            self.save()
        return self.total_amount


class OvertimeApprovalWorkflow(models.Model):
    """
    Overtime approval workflows
    Based on overtime_approval_workflows table from database.txt
    """
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    department = models.ForeignKey('Department', on_delete=models.SET_NULL, null=True, blank=True, related_name='overtime_workflows')

    # Workflow steps
    requires_supervisor_approval = models.BooleanField(default=True)
    requires_admin_approval = models.BooleanField(default=False)
    requires_hr_approval = models.BooleanField(default=False)

    # Approval thresholds
    supervisor_approval_threshold = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)
    admin_approval_threshold = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)
    auto_approval_threshold = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)

    # Time limits
    approval_deadline_hours = models.IntegerField(default=24)
    advance_notice_hours = models.IntegerField(default=4)

    # Escalation rules
    escalation_enabled = models.BooleanField(default=True)
    escalation_hours = models.IntegerField(default=24)
    escalate_to_admin = models.BooleanField(default=True)
    escalate_to_hr = models.BooleanField(default=False)

    # Notification settings
    notify_employee = models.BooleanField(default=True)
    notify_supervisor = models.BooleanField(default=True)
    notify_admin = models.BooleanField(default=False)
    notify_hr = models.BooleanField(default=False)

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_overtime_workflows')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_overtime_workflows')

    class Meta:
        db_table = 'overtime_approval_workflows'
        verbose_name = 'Overtime Approval Workflow'
        verbose_name_plural = 'Overtime Approval Workflows'

    def __str__(self):
        return self.name


class OvertimeBudget(models.Model):
    """
    Overtime budget and limits
    Based on overtime_budgets table from database.txt
    """
    department = models.ForeignKey('Department', on_delete=models.SET_NULL, null=True, blank=True, related_name='overtime_budgets')
    employee = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='overtime_budgets')

    # Budget period
    budget_year = models.IntegerField()
    budget_month = models.IntegerField(null=True, blank=True)  # NULL for annual budget

    # Budget limits
    allocated_hours = models.DecimalField(max_digits=6, decimal_places=2)
    allocated_amount = models.DecimalField(max_digits=15, decimal_places=2)
    used_hours = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    used_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    # Alerts and thresholds
    warning_threshold_percentage = models.IntegerField(default=80)
    block_threshold_percentage = models.IntegerField(default=100)

    # Status
    is_active = models.BooleanField(default=True)
    budget_exceeded = models.BooleanField(default=False)

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_overtime_budgets')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_overtime_budgets')

    class Meta:
        db_table = 'overtime_budgets'
        verbose_name = 'Overtime Budget'
        verbose_name_plural = 'Overtime Budgets'
        unique_together = ['department', 'employee', 'budget_year', 'budget_month']

    @property
    def remaining_hours(self):
        return self.allocated_hours - self.used_hours

    @property
    def remaining_amount(self):
        return self.allocated_amount - self.used_amount

    def __str__(self):
        if self.employee:
            return f"{self.employee.first_name} {self.employee.last_name} - {self.budget_year}"
        return f"{self.department.name} - {self.budget_year}"


class OvertimeCalculation(models.Model):
    """
    Overtime calculator (calculates overtime payments for employees)
    Based on overtime_calculations table from database.txt
    """
    STATUS_CHOICES = [
        ('calculated', 'Calculated'),
        ('approved', 'Approved'),
        ('included_in_payroll', 'Included in Payroll'),
        ('paid', 'Paid'),
    ]

    CALCULATION_METHOD_CHOICES = [
        ('standard', 'Standard'),
        ('holiday', 'Holiday'),
        ('emergency', 'Emergency'),
    ]

    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='overtime_calculations')
    overtime_record = models.OneToOneField(OvertimeRecord, on_delete=models.CASCADE, related_name='calculation')
    pay_cycle = models.ForeignKey('PayCycle', on_delete=models.SET_NULL, null=True, blank=True, related_name='overtime_calculations')

    # Employee salary information at time of calculation
    base_hourly_rate = models.DecimalField(max_digits=10, decimal_places=2)
    overtime_rate_multiplier = models.DecimalField(max_digits=3, decimal_places=2)
    calculated_overtime_rate = models.DecimalField(max_digits=10, decimal_places=2)

    # Overtime details
    total_overtime_hours = models.DecimalField(max_digits=4, decimal_places=2)
    regular_hours_equivalent = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)

    # Calculation breakdown
    gross_overtime_amount = models.DecimalField(max_digits=12, decimal_places=2)

    # Tax calculations on overtime (Kenya specific)
    overtime_tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    overtime_tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    # Statutory deductions on overtime
    overtime_nssf_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    overtime_nhif_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    overtime_housing_levy = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    # Total deductions from overtime
    total_overtime_deductions = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    # Net overtime payment
    net_overtime_amount = models.DecimalField(max_digits=12, decimal_places=2)

    # Calculation metadata
    calculation_date = models.DateTimeField(default=timezone.now)
    calculation_method = models.CharField(max_length=50, choices=CALCULATION_METHOD_CHOICES, default='standard')
    currency = models.CharField(max_length=3, default='KSH')
    exchange_rate = models.DecimalField(max_digits=10, decimal_places=4, default=1.0000)

    # Approval and processing
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='calculated')
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_overtime_calculations')
    approved_at = models.DateTimeField(null=True, blank=True)

    # Integration with payroll
    included_in_payroll = models.BooleanField(default=False)
    payroll_record = models.ForeignKey('PayrollRecord', on_delete=models.SET_NULL, null=True, blank=True, related_name='overtime_calculations')

    # Audit trail
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_overtime_calculations')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_overtime_calculations')

    class Meta:
        db_table = 'overtime_calculations'
        verbose_name = 'Overtime Calculation'
        verbose_name_plural = 'Overtime Calculations'

    def __str__(self):
        return f"{self.employee.first_name} {self.employee.last_name} - {self.calculation_date.date()}"
