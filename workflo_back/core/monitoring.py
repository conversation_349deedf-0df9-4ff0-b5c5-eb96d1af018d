"""
System Health Monitoring Module
Comprehensive monitoring for WorkFlo Backend system health
"""

import os
import psutil
import logging
import time
from datetime import datetime, timedelta
from django.utils import timezone
from django.db import connection
from django.core.cache import cache
from django.conf import settings
from django.db.models import Count, Q
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from drf_spectacular.utils import extend_schema

from .models.auth import User, UserSession
from .models.system import AuditLog, ActivityLog, SystemSetting
from .models.attendance import AttendanceRecord
from .models.leave import LeaveApplication
from .models.payroll import PayrollRecord

logger = logging.getLogger(__name__)


class SystemHealthMonitor:
    """Comprehensive system health monitoring"""
    
    def __init__(self):
        self.start_time = time.time()
        self.health_data = {}
    
    def check_database_health(self):
        """Check database connectivity and performance"""
        try:
            start_time = time.time()
            
            # Test database connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
            
            query_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            
            # Get database statistics
            user_count = User.objects.count()
            active_sessions = UserSession.objects.filter(expires_at__gt=timezone.now()).count()
            
            return {
                'status': 'healthy' if query_time < 100 else 'warning',
                'response_time_ms': round(query_time, 2),
                'total_users': user_count,
                'active_sessions': active_sessions,
                'connection_status': 'connected'
            }
        except Exception as e:
            logger.error(f"Database health check failed: {str(e)}")
            return {
                'status': 'critical',
                'error': str(e),
                'connection_status': 'failed'
            }
    
    def check_system_resources(self):
        """Check system resource usage"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available_gb = memory.available / (1024**3)
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            disk_free_gb = disk.free / (1024**3)
            
            # Network statistics
            network = psutil.net_io_counters()
            
            # Determine overall status
            status = 'healthy'
            if cpu_percent > 80 or memory_percent > 85 or disk_percent > 90:
                status = 'warning'
            if cpu_percent > 95 or memory_percent > 95 or disk_percent > 95:
                status = 'critical'
            
            return {
                'status': status,
                'cpu': {
                    'usage_percent': round(cpu_percent, 2),
                    'cores': psutil.cpu_count()
                },
                'memory': {
                    'usage_percent': round(memory_percent, 2),
                    'available_gb': round(memory_available_gb, 2),
                    'total_gb': round(memory.total / (1024**3), 2)
                },
                'disk': {
                    'usage_percent': round(disk_percent, 2),
                    'free_gb': round(disk_free_gb, 2),
                    'total_gb': round(disk.total / (1024**3), 2)
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                }
            }
        except Exception as e:
            logger.error(f"System resource check failed: {str(e)}")
            return {
                'status': 'critical',
                'error': str(e)
            }
    
    def check_application_health(self):
        """Check application-specific health metrics"""
        try:
            now = timezone.now()
            last_hour = now - timedelta(hours=1)
            last_24_hours = now - timedelta(hours=24)
            
            # Recent activity metrics
            recent_logins = UserSession.objects.filter(created_at__gte=last_hour).count()
            recent_audit_logs = AuditLog.objects.filter(created_at__gte=last_hour).count()
            recent_errors = AuditLog.objects.filter(
                created_at__gte=last_hour,
                action__icontains='error'
            ).count()
            
            # Daily metrics
            daily_attendance = AttendanceRecord.objects.filter(date=now.date()).count()
            daily_leave_apps = LeaveApplication.objects.filter(applied_date__date=now.date()).count()
            
            # Active users
            active_users_24h = User.objects.filter(
                last_login__gte=last_24_hours,
                is_active=True
            ).count()
            
            # Error rate calculation
            error_rate = (recent_errors / max(recent_audit_logs, 1)) * 100
            
            # Determine status
            status = 'healthy'
            if error_rate > 5:
                status = 'warning'
            if error_rate > 15:
                status = 'critical'
            
            return {
                'status': status,
                'metrics': {
                    'recent_logins_1h': recent_logins,
                    'recent_errors_1h': recent_errors,
                    'error_rate_percent': round(error_rate, 2),
                    'daily_attendance_records': daily_attendance,
                    'daily_leave_applications': daily_leave_apps,
                    'active_users_24h': active_users_24h
                },
                'uptime_seconds': round(time.time() - self.start_time, 2)
            }
        except Exception as e:
            logger.error(f"Application health check failed: {str(e)}")
            return {
                'status': 'critical',
                'error': str(e)
            }
    
    def check_cache_health(self):
        """Check cache system health"""
        try:
            start_time = time.time()
            
            # Test cache write/read
            test_key = 'health_check_test'
            test_value = f'test_{int(time.time())}'
            
            cache.set(test_key, test_value, 60)
            retrieved_value = cache.get(test_key)
            
            response_time = (time.time() - start_time) * 1000
            
            if retrieved_value == test_value:
                return {
                    'status': 'healthy',
                    'response_time_ms': round(response_time, 2),
                    'read_write_test': 'passed'
                }
            else:
                return {
                    'status': 'warning',
                    'response_time_ms': round(response_time, 2),
                    'read_write_test': 'failed'
                }
        except Exception as e:
            logger.warning(f"Cache health check failed: {str(e)}")
            return {
                'status': 'warning',
                'error': str(e),
                'message': 'Cache not available, using fallback'
            }
    
    def check_external_services(self):
        """Check external service dependencies"""
        services = {}
        
        # Check email service (if configured)
        try:
            from django.core.mail import get_connection
            connection = get_connection()
            connection.open()
            connection.close()
            services['email'] = {'status': 'healthy', 'service': 'email_backend'}
        except Exception as e:
            services['email'] = {'status': 'warning', 'error': str(e)}
        
        # Check file storage
        try:
            import tempfile
            with tempfile.NamedTemporaryFile(delete=True) as tmp:
                tmp.write(b'health check')
                tmp.flush()
            services['file_storage'] = {'status': 'healthy', 'service': 'local_storage'}
        except Exception as e:
            services['file_storage'] = {'status': 'critical', 'error': str(e)}
        
        return services
    
    def get_comprehensive_health(self):
        """Get comprehensive system health report"""
        health_report = {
            'timestamp': timezone.now().isoformat(),
            'overall_status': 'healthy',
            'components': {}
        }
        
        # Check all components
        components = {
            'database': self.check_database_health(),
            'system_resources': self.check_system_resources(),
            'application': self.check_application_health(),
            'cache': self.check_cache_health(),
            'external_services': self.check_external_services()
        }
        
        health_report['components'] = components
        
        # Determine overall status
        statuses = []
        for component_name, component_data in components.items():
            if isinstance(component_data, dict) and 'status' in component_data:
                statuses.append(component_data['status'])
            elif isinstance(component_data, dict):
                # For external services, check individual service statuses
                for service_data in component_data.values():
                    if isinstance(service_data, dict) and 'status' in service_data:
                        statuses.append(service_data['status'])
        
        if 'critical' in statuses:
            health_report['overall_status'] = 'critical'
        elif 'warning' in statuses:
            health_report['overall_status'] = 'warning'
        
        return health_report


@extend_schema(
    description="Get comprehensive system health status",
    responses={200: "System health report"}
)
class SystemHealthView(APIView):
    """API endpoint for system health monitoring"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """Get system health status"""
        monitor = SystemHealthMonitor()
        health_report = monitor.get_comprehensive_health()
        
        # Log health check
        logger.info(f"Health check performed by {request.user.email}, status: {health_report['overall_status']}")
        
        # Return appropriate HTTP status based on health
        if health_report['overall_status'] == 'critical':
            return Response(health_report, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        elif health_report['overall_status'] == 'warning':
            return Response(health_report, status=status.HTTP_200_OK)
        else:
            return Response(health_report, status=status.HTTP_200_OK)


@extend_schema(
    description="Get system metrics and statistics",
    responses={200: "System metrics"}
)
class SystemMetricsView(APIView):
    """API endpoint for system metrics"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """Get system metrics"""
        try:
            now = timezone.now()
            last_24_hours = now - timedelta(hours=24)
            last_7_days = now - timedelta(days=7)
            last_30_days = now - timedelta(days=30)
            
            metrics = {
                'timestamp': now.isoformat(),
                'user_metrics': {
                    'total_users': User.objects.count(),
                    'active_users': User.objects.filter(is_active=True).count(),
                    'users_logged_in_24h': User.objects.filter(last_login__gte=last_24_hours).count(),
                    'new_users_7d': User.objects.filter(date_joined__gte=last_7_days).count()
                },
                'activity_metrics': {
                    'audit_logs_24h': AuditLog.objects.filter(created_at__gte=last_24_hours).count(),
                    'activity_logs_24h': ActivityLog.objects.filter(created_at__gte=last_24_hours).count(),
                    'attendance_records_today': AttendanceRecord.objects.filter(date=now.date()).count(),
                    'leave_applications_30d': LeaveApplication.objects.filter(applied_date__gte=last_30_days).count()
                },
                'system_metrics': {
                    'database_size_mb': self._get_database_size(),
                    'log_files_size_mb': self._get_log_files_size(),
                    'media_files_size_mb': self._get_media_files_size()
                }
            }
            
            return Response(metrics)
            
        except Exception as e:
            logger.error(f"Error getting system metrics: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve system metrics'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _get_database_size(self):
        """Get database size in MB"""
        try:
            db_path = settings.DATABASES['default']['NAME']
            if os.path.exists(db_path):
                return round(os.path.getsize(db_path) / (1024 * 1024), 2)
        except:
            pass
        return 0
    
    def _get_log_files_size(self):
        """Get total log files size in MB"""
        try:
            logs_dir = os.path.join(settings.BASE_DIR, 'logs')
            if os.path.exists(logs_dir):
                total_size = sum(
                    os.path.getsize(os.path.join(logs_dir, f))
                    for f in os.listdir(logs_dir)
                    if os.path.isfile(os.path.join(logs_dir, f))
                )
                return round(total_size / (1024 * 1024), 2)
        except:
            pass
        return 0
    
    def _get_media_files_size(self):
        """Get total media files size in MB"""
        try:
            media_dir = settings.MEDIA_ROOT
            if os.path.exists(media_dir):
                total_size = sum(
                    os.path.getsize(os.path.join(dirpath, filename))
                    for dirpath, dirnames, filenames in os.walk(media_dir)
                    for filename in filenames
                )
                return round(total_size / (1024 * 1024), 2)
        except:
            pass
        return 0
