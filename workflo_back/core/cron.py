"""
Cron jobs for WorkFlo Backend
Comprehensive scheduled tasks for system maintenance and automation
"""

import logging
import os
from datetime import datetime, timedelta, date
from django.utils import timezone
from django.core.management import call_command
from django.db.models import Q
from django.conf import settings

# Import models
from .models.auth import User, UserSession
from .models.attendance import AttendanceRecord, BiostarEvent
from .models.leave import LeaveApplication, LeaveBalance
from .models.payroll import PayCycle, PayrollRecord
from .models.notifications import Notification, EmailLog
from .models.system import AuditLog, ActivityLog

logger = logging.getLogger(__name__)


def daily_cleanup():
    """
    Daily cleanup tasks
    - Clean expired sessions
    - Clean old audit logs
    - Update leave balances
    - Process pending notifications
    """
    logger.info("Starting daily cleanup tasks")
    
    try:
        # Clean expired user sessions
        expired_sessions = UserSession.objects.filter(expires_at__lt=timezone.now())
        expired_count = expired_sessions.count()
        expired_sessions.delete()
        logger.info(f"Cleaned {expired_count} expired user sessions")
        
        # Clean old audit logs (older than 1 year)
        old_date = timezone.now() - timedelta(days=365)
        old_logs = AuditLog.objects.filter(created_at__lt=old_date)
        old_logs_count = old_logs.count()
        old_logs.delete()
        logger.info(f"Cleaned {old_logs_count} old audit logs")
        
        # Clean old activity logs (older than 6 months)
        activity_old_date = timezone.now() - timedelta(days=180)
        old_activity_logs = ActivityLog.objects.filter(created_at__lt=activity_old_date)
        old_activity_count = old_activity_logs.count()
        old_activity_logs.delete()
        logger.info(f"Cleaned {old_activity_count} old activity logs")
        
        # Update leave balances for new year
        current_year = date.today().year
        users_without_current_balance = User.objects.exclude(
            leave_balances__year=current_year
        ).filter(is_active=True, is_deleted=False)
        
        for user in users_without_current_balance:
            # Create leave balances for current year based on leave types
            from .models.leave import LeaveType
            leave_types = LeaveType.objects.filter(is_active=True)
            for leave_type in leave_types:
                if leave_type.max_days_per_year:
                    LeaveBalance.objects.get_or_create(
                        employee=user,
                        leave_type=leave_type,
                        year=current_year,
                        defaults={'allocated_days': leave_type.max_days_per_year}
                    )
        
        logger.info("Daily cleanup completed successfully")
        
    except Exception as e:
        logger.error(f"Error in daily cleanup: {str(e)}")


def monthly_reports():
    """
    Monthly report generation
    - Generate payroll reports
    - Generate attendance reports
    - Generate leave reports
    """
    logger.info("Starting monthly report generation")
    
    try:
        current_month = date.today().replace(day=1)
        previous_month = (current_month - timedelta(days=1)).replace(day=1)
        
        # Generate attendance summary
        attendance_records = AttendanceRecord.objects.filter(
            date__gte=previous_month,
            date__lt=current_month
        )
        
        logger.info(f"Generated attendance report for {attendance_records.count()} records")
        
        # Generate leave summary
        leave_applications = LeaveApplication.objects.filter(
            start_date__gte=previous_month,
            start_date__lt=current_month
        )
        
        logger.info(f"Generated leave report for {leave_applications.count()} applications")
        
        # Generate payroll summary
        payroll_records = PayrollRecord.objects.filter(
            pay_cycle__start_date__gte=previous_month,
            pay_cycle__start_date__lt=current_month
        )
        
        logger.info(f"Generated payroll report for {payroll_records.count()} records")
        
        logger.info("Monthly reports generated successfully")
        
    except Exception as e:
        logger.error(f"Error in monthly report generation: {str(e)}")


def sync_biostar_events():
    """
    Enhanced BioStar event synchronization with overtime detection and auto-approval
    """
    logger.info("Starting enhanced BioStar event synchronization")

    try:
        # Import services (avoid circular imports)
        from .services.biostar_sync import biostar_sync
        from .services.biostar_api import biostar_api

        # Test API connection first
        connection_test = biostar_api.test_connection()
        if not connection_test['connected']:
            logger.warning(f"BioStar API not available: {connection_test['message']}")
            return

        # Sync recent events (last 24 hours)
        end_time = timezone.now()
        start_time = end_time - timedelta(hours=24)

        # Perform full synchronization
        sync_result = biostar_sync.sync_events(start_time, end_time)
        logger.info(f"BioStar sync completed: {sync_result}")

        # Process any remaining unprocessed events
        unprocessed_events = BiostarEvent.objects.filter(processed=False)
        processed_count = 0
        overtime_auto_approved = 0

        for event in unprocessed_events[:100]:  # Limit to 100 events per run
            try:
                if event.employee:
                    # Get or create attendance record for the event date
                    event_date = event.event_datetime.date()
                    attendance_record, created = AttendanceRecord.objects.get_or_create(
                        employee=event.employee,
                        date=event_date,
                        defaults={
                            'status': 'present',
                            'biostar_synced': True
                        }
                    )

                    # Update attendance record based on event type
                    if event.event_type in ['entry', 'ENTRY']:
                        if not attendance_record.check_in:
                            attendance_record.check_in = event.event_datetime
                    elif event.event_type in ['exit', 'EXIT']:
                        attendance_record.check_out = event.event_datetime

                    # Calculate hours if both check-in and check-out are available
                    if attendance_record.check_in and attendance_record.check_out:
                        attendance_record.calculate_hours()

                        # Check for overtime and create request if needed
                        if attendance_record.overtime_hours and attendance_record.overtime_hours > 0:
                            from .models.overtime import OvertimeRequest, OvertimeType

                            # Check if overtime request already exists
                            existing_request = OvertimeRequest.objects.filter(
                                employee=event.employee,
                                attendance_record=attendance_record
                            ).first()

                            if not existing_request:
                                # Get default overtime type
                                default_overtime_type = OvertimeType.objects.filter(
                                    is_active=True
                                ).first()

                                if default_overtime_type:
                                    # Create overtime request
                                    overtime_request = OvertimeRequest.objects.create(
                                        employee=event.employee,
                                        overtime_type=default_overtime_type,
                                        request_date=event_date,
                                        planned_start_time=attendance_record.check_in.time(),
                                        planned_end_time=attendance_record.check_out.time(),
                                        planned_hours=attendance_record.overtime_hours,
                                        reason=f"Auto-detected overtime from BioStar attendance",
                                        auto_detected=True,
                                        biostar_event_id=event.biostar_event_id,
                                        attendance_record=attendance_record
                                    )

                                    # Try auto-approval
                                    if overtime_request.auto_approve_if_eligible():
                                        overtime_auto_approved += 1
                                        logger.info(f"Auto-approved overtime for {event.employee.employee_id}")

                    attendance_record.save()

                    # Mark event as processed
                    event.processed = True
                    event.attendance_record = attendance_record
                    event.save()
                    processed_count += 1

            except Exception as e:
                logger.error(f"Error processing event {event.biostar_event_id}: {str(e)}")

        logger.info(f"Processed {processed_count} events, auto-approved {overtime_auto_approved} overtime requests")

        # Sync real-time events
        try:
            realtime_result = biostar_sync.sync_realtime_events()
            logger.info(f"Real-time sync: {realtime_result}")
        except Exception as e:
            logger.warning(f"Real-time sync failed: {str(e)}")

    except Exception as e:
        logger.error(f"Error in enhanced BioStar synchronization: {str(e)}")


def weekly_backup():
    """
    Weekly database backup
    """
    logger.info("Starting weekly backup")
    
    try:
        # Create backup directory if it doesn't exist
        backup_dir = os.path.join(settings.BASE_DIR, 'backups')
        os.makedirs(backup_dir, exist_ok=True)
        
        # Generate backup filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'workflo_backup_{timestamp}.json'
        backup_path = os.path.join(backup_dir, backup_filename)
        
        # Create database backup using Django's dumpdata command
        with open(backup_path, 'w') as backup_file:
            call_command('dumpdata', '--natural-foreign', '--natural-primary', stdout=backup_file)
        
        logger.info(f"Database backup created: {backup_path}")
        
        # Clean old backups (keep only last 4 weeks)
        old_date = datetime.now() - timedelta(weeks=4)
        for filename in os.listdir(backup_dir):
            if filename.startswith('workflo_backup_'):
                file_path = os.path.join(backup_dir, filename)
                file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                if file_time < old_date:
                    os.remove(file_path)
                    logger.info(f"Removed old backup: {filename}")
        
        logger.info("Weekly backup completed successfully")
        
    except Exception as e:
        logger.error(f"Error in weekly backup: {str(e)}")


def process_notifications():
    """
    Process pending notifications and send emails
    """
    logger.info("Starting notification processing")
    
    try:
        # Get pending notifications
        pending_notifications = Notification.objects.filter(is_read=False)
        
        for notification in pending_notifications:
            try:
                # Create email log entry
                email_log = EmailLog.objects.create(
                    notification=notification,
                    recipient_email=notification.recipient.email,
                    subject=notification.title,
                    status='pending'
                )
                
                # Here you would integrate with your email service
                # For now, we'll just mark as sent
                email_log.status = 'sent'
                email_log.save()
                
                logger.debug(f"Processed notification for {notification.recipient.email}")
                
            except Exception as e:
                logger.error(f"Error processing notification {notification.id}: {str(e)}")
        
        logger.info(f"Processed {pending_notifications.count()} notifications")
        
    except Exception as e:
        logger.error(f"Error in notification processing: {str(e)}")


def calculate_overtime_budgets():
    """
    Calculate and update overtime budgets
    """
    logger.info("Starting overtime budget calculation")
    
    try:
        from .models.overtime import OvertimeBudget, OvertimeRecord
        
        current_month = date.today().replace(day=1)
        
        # Get all active overtime budgets for current month
        budgets = OvertimeBudget.objects.filter(
            budget_year=current_month.year,
            budget_month=current_month.month,
            is_active=True
        )
        
        for budget in budgets:
            # Calculate used hours and amount for this budget period
            overtime_records = OvertimeRecord.objects.filter(
                employee=budget.employee,
                overtime_date__year=budget.budget_year,
                overtime_date__month=budget.budget_month or current_month.month,
                status='approved'
            )
            
            total_hours = sum(record.total_hours for record in overtime_records)
            total_amount = sum(record.total_amount or 0 for record in overtime_records)
            
            # Update budget
            budget.used_hours = total_hours
            budget.used_amount = total_amount
            
            # Check if budget is exceeded
            if total_hours >= budget.allocated_hours or total_amount >= budget.allocated_amount:
                budget.budget_exceeded = True
            
            budget.save()
        
        logger.info(f"Updated {budgets.count()} overtime budgets")
        
    except Exception as e:
        logger.error(f"Error in overtime budget calculation: {str(e)}")


def auto_approve_leave_applications():
    """
    Auto-approve leave applications based on rules
    """
    logger.info("Starting auto-approval of leave applications")
    
    try:
        # Get pending leave applications that can be auto-approved
        pending_applications = LeaveApplication.objects.filter(
            status='pending',
            days_requested__lte=2,  # Auto-approve only short leaves
            leave_type__name__in=['Sick Leave', 'Emergency Leave']
        )
        
        for application in pending_applications:
            # Check if employee has sufficient balance
            try:
                balance = LeaveBalance.objects.get(
                    employee=application.employee,
                    leave_type=application.leave_type,
                    year=application.start_date.year
                )
                
                if balance.remaining_days >= application.days_requested:
                    application.status = 'approved'
                    application.approved_at = timezone.now()
                    application.reviewer_comments = 'Auto-approved by system'
                    application.save()
                    
                    # Update leave balance
                    balance.used_days += application.days_requested
                    balance.save()
                    
                    logger.info(f"Auto-approved leave application {application.id}")
                
            except LeaveBalance.DoesNotExist:
                logger.warning(f"No leave balance found for application {application.id}")
        
        logger.info(f"Processed {pending_applications.count()} leave applications for auto-approval")
        
    except Exception as e:
        logger.error(f"Error in auto-approval of leave applications: {str(e)}")


def system_health_check():
    """
    Perform system health checks and log status
    """
    logger.info("Starting system health check")
    
    try:
        # Check database connectivity
        user_count = User.objects.count()
        logger.info(f"Database check: {user_count} users in system")
        
        # Check disk space
        import shutil
        total, used, free = shutil.disk_usage("/")
        disk_usage_percent = (used / total) * 100
        logger.info(f"Disk usage: {disk_usage_percent:.1f}%")
        
        if disk_usage_percent > 90:
            logger.warning("High disk usage detected!")
        
        # Check recent errors
        recent_errors = AuditLog.objects.filter(
            created_at__gte=timezone.now() - timedelta(hours=1),
            action__icontains='error'
        ).count()
        
        if recent_errors > 10:
            logger.warning(f"High error rate detected: {recent_errors} errors in last hour")
        
        logger.info("System health check completed")
        
    except Exception as e:
        logger.error(f"Error in system health check: {str(e)}")
