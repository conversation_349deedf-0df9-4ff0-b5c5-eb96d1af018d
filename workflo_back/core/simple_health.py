"""
Simple Health Check Views
Basic health monitoring without external dependencies
"""

import logging
import time
from datetime import datetime
from django.utils import timezone
from django.db import connection
from django.http import JsonResponse
from django.views import View
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from drf_spectacular.utils import extend_schema

logger = logging.getLogger(__name__)


class SimpleHealthView(View):
    """Simple health check without authentication"""
    
    def get(self, request):
        """Basic health check"""
        try:
            # Test database connection
            start_time = time.time()
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
            
            db_time = (time.time() - start_time) * 1000
            
            health_data = {
                'status': 'healthy',
                'timestamp': timezone.now().isoformat(),
                'database': {
                    'status': 'connected',
                    'response_time_ms': round(db_time, 2)
                },
                'application': {
                    'status': 'running',
                    'version': '1.0.0'
                }
            }
            
            return JsonResponse(health_data)
            
        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            return JsonResponse({
                'status': 'unhealthy',
                'timestamp': timezone.now().isoformat(),
                'error': str(e)
            }, status=503)


@extend_schema(
    description="Get basic system health status",
    responses={200: "System health report"}
)
class SimpleSystemHealthView(APIView):
    """API endpoint for basic system health monitoring"""
    
    def get(self, request):
        """Get basic system health status"""
        try:
            # Test database connection
            start_time = time.time()
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
            
            db_time = (time.time() - start_time) * 1000
            
            health_data = {
                'status': 'healthy',
                'timestamp': timezone.now().isoformat(),
                'database': {
                    'status': 'connected',
                    'response_time_ms': round(db_time, 2)
                },
                'application': {
                    'status': 'running',
                    'version': '1.0.0',
                    'user': request.user.email if request.user.is_authenticated else 'anonymous'
                }
            }
            
            return Response(health_data)
            
        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            return Response({
                'status': 'unhealthy',
                'timestamp': timezone.now().isoformat(),
                'error': str(e)
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)


@extend_schema(
    description="Get basic system metrics",
    responses={200: "System metrics"}
)
class SimpleMetricsView(APIView):
    """API endpoint for basic system metrics"""
    
    def get(self, request):
        """Get basic system metrics"""
        try:
            from .models.auth import User
            
            metrics = {
                'timestamp': timezone.now().isoformat(),
                'basic_metrics': {
                    'total_users': User.objects.count(),
                    'active_users': User.objects.filter(is_active=True).count(),
                    'staff_users': User.objects.filter(is_staff=True).count(),
                    'superusers': User.objects.filter(is_superuser=True).count()
                },
                'system_info': {
                    'version': '1.0.0',
                    'environment': 'production' if not hasattr(request, 'DEBUG') else 'development'
                }
            }
            
            return Response(metrics)
            
        except Exception as e:
            logger.error(f"Error getting metrics: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve metrics'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
