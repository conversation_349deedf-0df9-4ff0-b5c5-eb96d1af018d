# Enhanced Features API Documentation

## 🏦 Bank Profiles API - Multiple Accounts per Employee

### Overview
Employees can now have multiple bank accounts with enhanced CRUD operations and role-based access control.

### Endpoints

#### 1. **GET /api/bank-profiles/**
Get all bank profiles (filtered by user role)
- **Admin/HR**: See all bank profiles
- **Supervisor**: See department employees' bank profiles
- **Employee**: See only own bank profiles

**Query Parameters:**
- `employee` - Filter by employee ID
- `bank_name` - Filter by bank name
- `is_primary` - Filter by primary account status
- `account_type` - Filter by account type
- `is_active` - Filter by active status

#### 2. **POST /api/bank-profiles/**
Create new bank profile

**Request Body:**
```json
{
  "employee": 1,
  "bank_name": "Equity Bank",
  "branch_name": "Westlands Branch",
  "account_number": "**********",
  "account_name": "<PERSON>",
  "account_type": "savings",
  "swift_code": "EQBLKENA",
  "is_primary": true,
  "is_active": true
}
```

#### 3. **GET /api/bank-profiles/my_accounts/**
Get current user's bank accounts

#### 4. **POST /api/bank-profiles/{id}/set_primary/**
Set bank account as primary (removes primary flag from other accounts)

#### 5. **GET /api/bank-profiles/employee_accounts/?employee_id={id}**
Get bank accounts for specific employee (Admin/HR only)

---

## 👥 Emergency Contacts API - Multiple Contacts per Employee

### Overview
Employees can have multiple emergency contacts with priority ordering and enhanced management.

### Endpoints

#### 1. **GET /api/emergency-contacts/**
Get all emergency contacts (filtered by user role)

**Query Parameters:**
- `employee` - Filter by employee ID
- `relationship` - Filter by relationship type
- `is_active` - Filter by active status

#### 2. **POST /api/emergency-contacts/**
Create new emergency contact

**Request Body:**
```json
{
  "employee": 1,
  "contact_name": "Jane Doe",
  "relationship": "spouse",
  "phone_number": "+************",
  "email": "<EMAIL>",
  "address": "123 Main St, Nairobi",
  "priority_order": 1,
  "is_active": true
}
```

#### 3. **GET /api/emergency-contacts/my_contacts/**
Get current user's emergency contacts

#### 4. **POST /api/emergency-contacts/{id}/update_priority/**
Update priority order of emergency contact

**Request Body:**
```json
{
  "priority_order": 2
}
```

#### 5. **GET /api/emergency-contacts/employee_contacts/?employee_id={id}**
Get emergency contacts for specific employee (Admin/HR only)

---

## 🏢 Company Information API - Admin CRUD Operations

### Overview
Complete company information management with admin-only CRUD operations and public viewing access.

### Endpoints

#### 1. **GET /api/company-info/current/**
Get current company information (accessible to all authenticated users)

**Response:**
```json
{
  "id": 1,
  "company_name": "WorkFlo Solutions",
  "logo_url": "http://localhost:8000/media/company/logos/logo.png",
  "description": "Leading HR management solutions",
  "address": "123 Business St",
  "city": "Nairobi",
  "state": "Nairobi County",
  "postal_code": "00100",
  "country": "Kenya",
  "phone_number": "+************",
  "email": "<EMAIL>",
  "website": "https://workflo.com",
  "tax_id": "P051234567A",
  "registration_number": "CPR/2023/123456",
  "industry": "Technology",
  "company_size": "51-200",
  "founded_year": 2020,
  "mission_statement": "To revolutionize HR management",
  "vision_statement": "Leading HR tech company in Africa",
  "values": "Innovation, Integrity, Excellence",
  "timezone": "Africa/Nairobi",
  "currency": "KSH",
  "fiscal_year_start_month": 1,
  "working_days_per_week": 5,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-06-01T12:00:00Z",
  "updated_by": 1
}
```

#### 2. **POST /api/company-info/** (Admin Only)
Create company information

**Request Body:**
```json
{
  "company_name": "WorkFlo Solutions",
  "description": "Leading HR management solutions",
  "address": "123 Business St",
  "city": "Nairobi",
  "country": "Kenya",
  "phone_number": "+************",
  "email": "<EMAIL>",
  "website": "https://workflo.com",
  "industry": "Technology",
  "company_size": "51-200",
  "founded_year": 2020
}
```

#### 3. **PUT/PATCH /api/company-info/{id}/** (Admin Only)
Update company information

#### 4. **DELETE /api/company-info/{id}/** (Admin Only)
Delete company information

#### 5. **POST /api/company-info/{id}/upload_logo/** (Admin Only)
Upload company logo

**Request:** Multipart form data with `logo` file

**Response:**
```json
{
  "message": "Logo uploaded successfully",
  "logo_url": "http://localhost:8000/media/company/logos/logo.png"
}
```

#### 6. **DELETE /api/company-info/{id}/remove_logo/** (Admin Only)
Remove company logo

#### 7. **GET /api/company-info/basic_info/**
Get basic company information for public display

**Response:**
```json
{
  "company_name": "WorkFlo Solutions",
  "logo_url": "http://localhost:8000/media/company/logos/logo.png",
  "phone_number": "+************",
  "email": "<EMAIL>",
  "website": "https://workflo.com",
  "address": "123 Business St",
  "city": "Nairobi",
  "country": "Kenya"
}
```

---

## 🔐 Role-Based Access Control

### Bank Profiles Access:
- **Admin/HR**: Full access to all bank profiles
- **Supervisor**: Access to department employees' bank profiles
- **Employee**: Access to own bank profiles only

### Emergency Contacts Access:
- **Admin/HR**: Full access to all emergency contacts
- **Supervisor**: Access to department employees' emergency contacts
- **Employee**: Access to own emergency contacts only

### Company Information Access:
- **Admin**: Full CRUD operations
- **All Users**: Read-only access to company information

---

## 📝 Validation Rules

### Bank Profiles:
- Account number must be unique per bank
- Only one primary account per employee
- Account name validation
- Bank name from predefined list

### Emergency Contacts:
- Phone number format validation (Kenyan format)
- Email validation
- Priority order must be unique per employee
- Relationship from predefined choices

### Company Information:
- Logo file size limit: 2MB
- Logo file types: JPEG, PNG, GIF
- Phone number validation (Kenyan format)
- Email validation
- Website URL validation

---

## 🚀 Enhanced Features

### Bank Profiles:
- ✅ Multiple accounts per employee
- ✅ Primary account designation
- ✅ Account type classification
- ✅ Role-based filtering
- ✅ Audit trail (created_by, updated_by)

### Emergency Contacts:
- ✅ Multiple contacts per employee
- ✅ Priority ordering
- ✅ Relationship categorization
- ✅ Contact status management
- ✅ Role-based filtering

### Company Information:
- ✅ Comprehensive company profile
- ✅ Logo upload/management
- ✅ Admin-only modifications
- ✅ Public information access
- ✅ Audit trail tracking

---

## 📊 Usage Examples

### Adding Multiple Bank Accounts:
```bash
# Add primary savings account
curl -X POST /api/bank-profiles/ \
  -H "Authorization: Bearer {token}" \
  -d '{"employee": 1, "bank_name": "Equity Bank", "account_type": "savings", "is_primary": true}'

# Add secondary current account
curl -X POST /api/bank-profiles/ \
  -H "Authorization: Bearer {token}" \
  -d '{"employee": 1, "bank_name": "KCB Bank", "account_type": "current", "is_primary": false}'
```

### Managing Emergency Contacts:
```bash
# Add primary emergency contact
curl -X POST /api/emergency-contacts/ \
  -H "Authorization: Bearer {token}" \
  -d '{"employee": 1, "contact_name": "Jane Doe", "relationship": "spouse", "priority_order": 1}'

# Add secondary emergency contact
curl -X POST /api/emergency-contacts/ \
  -H "Authorization: Bearer {token}" \
  -d '{"employee": 1, "contact_name": "John Smith", "relationship": "parent", "priority_order": 2}'
```

### Company Information Management:
```bash
# Update company information (Admin only)
curl -X PATCH /api/company-info/1/ \
  -H "Authorization: Bearer {admin_token}" \
  -d '{"company_name": "Updated Company Name", "phone_number": "+************"}'

# Upload company logo (Admin only)
curl -X POST /api/company-info/1/upload_logo/ \
  -H "Authorization: Bearer {admin_token}" \
  -F "logo=@company_logo.png"
```
