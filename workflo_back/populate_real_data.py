#!/usr/bin/env python
"""
Populate WorkFlo Backend with Real Production Data
This script creates realistic employee data, attendance records, and payroll information
"""

import os
import sys
import django
from datetime import datetime, timedelta, date, time
from decimal import Decimal
import random

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'workflo_back.settings')
django.setup()

from core.models.auth import User
from core.models.organization import Department
from core.models.employees import EmployeeProfile, SalaryProfile, BankProfile, EmergencyContact
from core.models.attendance import AttendanceRecord, BiostarDevice
from core.models.overtime import OvertimeType, OvertimeRequest
from core.models.leave import LeaveType, LeaveBalance, CompanyHoliday
from core.models.payroll import PayCycle, PayrollRecord
from django.utils import timezone

def create_departments():
    """Create realistic department structure"""
    print("Creating departments...")
    
    departments = [
        {
            'name': 'Information Technology',
            'description': 'Software development, system administration, and IT support',
            'budget': 2000000,
            'location': 'Building A - 3rd Floor'
        },
        {
            'name': 'Human Resources',
            'description': 'Employee relations, recruitment, and HR administration',
            'budget': 800000,
            'location': 'Building A - 2nd Floor'
        },
        {
            'name': 'Finance & Accounting',
            'description': 'Financial planning, accounting, and payroll management',
            'budget': 1200000,
            'location': 'Building A - 1st Floor'
        },
        {
            'name': 'Operations',
            'description': 'Daily operations, logistics, and facility management',
            'budget': 1500000,
            'location': 'Building B - Ground Floor'
        },
        {
            'name': 'Sales & Marketing',
            'description': 'Sales operations, marketing campaigns, and customer relations',
            'budget': 1800000,
            'location': 'Building B - 2nd Floor'
        }
    ]
    
    created_departments = {}
    for dept_data in departments:
        dept, created = Department.objects.get_or_create(
            name=dept_data['name'],
            defaults=dept_data
        )
        created_departments[dept_data['name']] = dept
        if created:
            print(f"  ✅ Created department: {dept.name}")
        else:
            print(f"  ℹ️  Department exists: {dept.name}")
    
    return created_departments

def create_employees(departments):
    """Create realistic employee data"""
    print("Creating employees...")
    
    employees_data = [
        # IT Department
        {
            'user_data': {
                'email': '<EMAIL>',
                'username': 'john.doe',
                'first_name': 'John',
                'last_name': 'Doe',
                'employee_id': 'WF001',
                'role': 'admin'
            },
            'profile_data': {
                'department': departments['Information Technology'],
                'job_title': 'IT Director',
                'hire_date': date(2022, 1, 15),
                'employment_type': 'full_time',
                'work_location': 'office',
                'phone_number': '+************',
                'national_id': '********',
                'kra_pin': 'A001234567B'
            },
            'salary_data': {
                'basic_salary': Decimal('180000.00'),
                'allowances': Decimal('30000.00'),
                'hourly_rate': Decimal('1025.64'),
                'overtime_rate': Decimal('1538.46'),
                'pay_frequency': 'monthly',
                'currency': 'KSH'
            },
            'bank_data': {
                'bank_name': 'Kenya Commercial Bank',
                'bank_code': '01',
                'account_number': '********90',
                'account_name': 'John Doe',
                'account_type': 'savings',
                'is_primary': True
            }
        },
        {
            'user_data': {
                'email': '<EMAIL>',
                'username': 'sarah.johnson',
                'first_name': 'Sarah',
                'last_name': 'Johnson',
                'employee_id': 'WF002',
                'role': 'supervisor'
            },
            'profile_data': {
                'department': departments['Information Technology'],
                'job_title': 'Senior Software Engineer',
                'hire_date': date(2022, 3, 1),
                'employment_type': 'full_time',
                'work_location': 'hybrid',
                'phone_number': '+************',
                'national_id': '********',
                'kra_pin': 'A002345678C'
            },
            'salary_data': {
                'basic_salary': Decimal('140000.00'),
                'allowances': Decimal('20000.00'),
                'hourly_rate': Decimal('800.00'),
                'overtime_rate': Decimal('1200.00'),
                'pay_frequency': 'monthly',
                'currency': 'KSH'
            },
            'bank_data': {
                'bank_name': 'Equity Bank',
                'bank_code': '68',
                'account_number': '********01',
                'account_name': 'Sarah Johnson',
                'account_type': 'current',
                'is_primary': True
            }
        },
        # HR Department
        {
            'user_data': {
                'email': '<EMAIL>',
                'username': 'grace.wanjiku',
                'first_name': 'Grace',
                'last_name': 'Wanjiku',
                'employee_id': 'WF003',
                'role': 'hr'
            },
            'profile_data': {
                'department': departments['Human Resources'],
                'job_title': 'HR Manager',
                'hire_date': date(2021, 8, 15),
                'employment_type': 'full_time',
                'work_location': 'office',
                'phone_number': '+************',
                'national_id': '********',
                'kra_pin': 'A003456789D'
            },
            'salary_data': {
                'basic_salary': Decimal('130000.00'),
                'allowances': Decimal('25000.00'),
                'hourly_rate': Decimal('742.86'),
                'overtime_rate': Decimal('1114.29'),
                'pay_frequency': 'monthly',
                'currency': 'KSH'
            },
            'bank_data': {
                'bank_name': 'Cooperative Bank',
                'bank_code': '11',
                'account_number': '********12',
                'account_name': 'Grace Wanjiku',
                'account_type': 'savings',
                'is_primary': True
            }
        },
        # Finance Department
        {
            'user_data': {
                'email': '<EMAIL>',
                'username': 'michael.kimani',
                'first_name': 'Michael',
                'last_name': 'Kimani',
                'employee_id': 'WF004',
                'role': 'accountant'
            },
            'profile_data': {
                'department': departments['Finance & Accounting'],
                'job_title': 'Finance Manager',
                'hire_date': date(2021, 11, 1),
                'employment_type': 'full_time',
                'work_location': 'office',
                'phone_number': '+************',
                'national_id': '********',
                'kra_pin': 'A004567890E'
            },
            'salary_data': {
                'basic_salary': Decimal('125000.00'),
                'allowances': Decimal('20000.00'),
                'hourly_rate': Decimal('714.29'),
                'overtime_rate': Decimal('1071.43'),
                'pay_frequency': 'monthly',
                'currency': 'KSH'
            },
            'bank_data': {
                'bank_name': 'Standard Chartered Bank',
                'bank_code': '02',
                'account_number': '********23',
                'account_name': 'Michael Kimani',
                'account_type': 'current',
                'is_primary': True
            }
        },
        # Operations Department
        {
            'user_data': {
                'email': '<EMAIL>',
                'username': 'alice.mutua',
                'first_name': 'Alice',
                'last_name': 'Mutua',
                'employee_id': 'WF005',
                'role': 'supervisor'
            },
            'profile_data': {
                'department': departments['Operations'],
                'job_title': 'Operations Manager',
                'hire_date': date(2022, 2, 14),
                'employment_type': 'full_time',
                'work_location': 'office',
                'phone_number': '+************',
                'national_id': '********',
                'kra_pin': 'A005678901F'
            },
            'salary_data': {
                'basic_salary': Decimal('115000.00'),
                'allowances': Decimal('18000.00'),
                'hourly_rate': Decimal('657.14'),
                'overtime_rate': Decimal('985.71'),
                'pay_frequency': 'monthly',
                'currency': 'KSH'
            },
            'bank_data': {
                'bank_name': 'NCBA Bank',
                'bank_code': '07',
                'account_number': '********34',
                'account_name': 'Alice Mutua',
                'account_type': 'savings',
                'is_primary': True
            }
        },
        # Additional employees
        {
            'user_data': {
                'email': '<EMAIL>',
                'username': 'james.mwangi',
                'first_name': 'James',
                'last_name': 'Mwangi',
                'employee_id': 'WF006',
                'role': 'employee'
            },
            'profile_data': {
                'department': departments['Information Technology'],
                'job_title': 'Software Developer',
                'hire_date': date(2023, 1, 10),
                'employment_type': 'full_time',
                'work_location': 'remote',
                'phone_number': '+************',
                'national_id': '********',
                'kra_pin': 'A006789012G'
            },
            'salary_data': {
                'basic_salary': Decimal('95000.00'),
                'allowances': Decimal('12000.00'),
                'hourly_rate': Decimal('542.86'),
                'overtime_rate': Decimal('814.29'),
                'pay_frequency': 'monthly',
                'currency': 'KSH'
            },
            'bank_data': {
                'bank_name': 'Absa Bank',
                'bank_code': '03',
                'account_number': '**********',
                'account_name': 'James Mwangi',
                'account_type': 'savings',
                'is_primary': True
            }
        }
    ]
    
    created_employees = []
    for emp_data in employees_data:
        try:
            # Create user
            user, created = User.objects.get_or_create(
                email=emp_data['user_data']['email'],
                defaults={
                    'username': emp_data['user_data']['username'],
                    'first_name': emp_data['user_data']['first_name'],
                    'last_name': emp_data['user_data']['last_name'],
                    'employee_id': emp_data['user_data']['employee_id'],
                    'role': emp_data['user_data']['role'],
                    'is_active': True
                }
            )
            
            if created:
                user.set_password('workflo2024')
                user.save()
                print(f"  ✅ Created user: {user.first_name} {user.last_name}")
            else:
                print(f"  ℹ️  User exists: {user.first_name} {user.last_name}")
            
            # Create employee profile
            profile, created = EmployeeProfile.objects.get_or_create(
                user=user,
                defaults=emp_data['profile_data']
            )
            
            # Create salary profile
            salary_profile, created = SalaryProfile.objects.get_or_create(
                employee=user,
                defaults={
                    'effective_from': emp_data['profile_data']['hire_date'],
                    **emp_data['salary_data']
                }
            )
            
            # Create bank profile
            bank_profile, created = BankProfile.objects.get_or_create(
                employee=user,
                account_number=emp_data['bank_data']['account_number'],
                defaults=emp_data['bank_data']
            )
            
            # Create emergency contact
            emergency_contact, created = EmergencyContact.objects.get_or_create(
                employee=user,
                contact_name=f"{user.first_name} Emergency Contact",
                defaults={
                    'relationship': 'Spouse',
                    'phone_number': '+************',
                    'email': f'emergency.{user.username}@example.com',
                    'priority_order': 1
                }
            )
            
            created_employees.append(user)
            
        except Exception as e:
            print(f"  ❌ Error creating employee {emp_data['user_data']['first_name']}: {str(e)}")
    
    return created_employees

def create_attendance_records(employees):
    """Create realistic attendance records for the past month"""
    print("Creating attendance records...")
    
    # Create attendance for the past 30 days
    start_date = date.today() - timedelta(days=30)
    end_date = date.today()
    
    current_date = start_date
    while current_date <= end_date:
        # Skip weekends
        if current_date.weekday() < 5:  # Monday = 0, Friday = 4
            
            for employee in employees:
                # 90% attendance rate (some employees may be absent)
                if random.random() < 0.9:
                    # Random check-in time between 8:00 and 9:30
                    check_in_hour = random.randint(8, 9)
                    check_in_minute = random.randint(0, 30) if check_in_hour == 9 else random.randint(0, 59)
                    
                    # Random check-out time between 17:00 and 19:00
                    check_out_hour = random.randint(17, 18)
                    check_out_minute = random.randint(0, 59)
                    
                    check_in = timezone.make_aware(
                        datetime.combine(current_date, time(check_in_hour, check_in_minute))
                    )
                    check_out = timezone.make_aware(
                        datetime.combine(current_date, time(check_out_hour, check_out_minute))
                    )
                    
                    # Determine status
                    if check_in_hour > 9 or (check_in_hour == 9 and check_in_minute > 0):
                        status = 'late'
                    else:
                        status = 'present'
                    
                    attendance, created = AttendanceRecord.objects.get_or_create(
                        employee=employee,
                        date=current_date,
                        defaults={
                            'check_in': check_in,
                            'check_out': check_out,
                            'status': status,
                            'biostar_synced': False  # Manual entry
                        }
                    )
                    
                    if created:
                        attendance.calculate_hours()
                        
                        # Create overtime request if overtime hours > 0
                        if attendance.overtime_hours and attendance.overtime_hours > 0:
                            overtime_type = OvertimeType.objects.first()
                            if overtime_type:
                                OvertimeRequest.objects.get_or_create(
                                    employee=employee,
                                    request_date=current_date,
                                    defaults={
                                        'overtime_type': overtime_type,
                                        'planned_start_time': check_in.time(),
                                        'planned_end_time': check_out.time(),
                                        'planned_hours': attendance.overtime_hours,
                                        'reason': 'Project work - manual entry',
                                        'status': 'approved',
                                        'auto_detected': False
                                    }
                                )
        
        current_date += timedelta(days=1)
    
    print(f"  ✅ Created attendance records for {(end_date - start_date).days + 1} days")

def create_system_data():
    """Create system configuration data"""
    print("Creating system configuration...")
    
    # Create overtime types
    overtime_types = [
        {
            'name': 'Standard Overtime',
            'description': 'Regular overtime work during weekdays',
            'rate_multiplier': Decimal('1.5'),
            'max_hours_per_day': 4,
            'max_hours_per_week': 20,
            'requires_pre_approval': True,
            'auto_approve_threshold': 2,
            'is_active': True
        },
        {
            'name': 'Weekend Overtime',
            'description': 'Overtime work during weekends',
            'rate_multiplier': Decimal('2.0'),
            'max_hours_per_day': 8,
            'max_hours_per_week': 16,
            'requires_pre_approval': True,
            'auto_approve_threshold': 1,
            'is_active': True
        },
        {
            'name': 'Holiday Overtime',
            'description': 'Overtime work during public holidays',
            'rate_multiplier': Decimal('2.5'),
            'max_hours_per_day': 8,
            'max_hours_per_week': 8,
            'requires_pre_approval': True,
            'auto_approve_threshold': 0,
            'is_active': True
        }
    ]
    
    for ot_data in overtime_types:
        ot_type, created = OvertimeType.objects.get_or_create(
            name=ot_data['name'],
            defaults=ot_data
        )
        if created:
            print(f"  ✅ Created overtime type: {ot_type.name}")
    
    # Create leave types
    leave_types = [
        {
            'name': 'Annual Leave',
            'description': 'Annual vacation leave',
            'days_per_year': 21,
            'max_consecutive_days': 21,
            'requires_approval': True,
            'is_paid': True,
            'is_active': True
        },
        {
            'name': 'Sick Leave',
            'description': 'Medical leave for illness',
            'days_per_year': 14,
            'max_consecutive_days': 7,
            'requires_approval': False,
            'is_paid': True,
            'is_active': True
        },
        {
            'name': 'Maternity Leave',
            'description': 'Maternity leave for new mothers',
            'days_per_year': 90,
            'max_consecutive_days': 90,
            'requires_approval': True,
            'is_paid': True,
            'is_active': True
        }
    ]
    
    for leave_data in leave_types:
        leave_type, created = LeaveType.objects.get_or_create(
            name=leave_data['name'],
            defaults=leave_data
        )
        if created:
            print(f"  ✅ Created leave type: {leave_type.name}")
    
    # Create company holidays for 2024
    holidays_2024 = [
        ('2024-01-01', 'New Year\'s Day'),
        ('2024-04-01', 'Easter Monday'),
        ('2024-05-01', 'Labour Day'),
        ('2024-06-01', 'Madaraka Day'),
        ('2024-10-20', 'Mashujaa Day'),
        ('2024-12-12', 'Jamhuri Day'),
        ('2024-12-25', 'Christmas Day'),
        ('2024-12-26', 'Boxing Day')
    ]
    
    for holiday_date, holiday_name in holidays_2024:
        holiday, created = CompanyHoliday.objects.get_or_create(
            date=datetime.strptime(holiday_date, '%Y-%m-%d').date(),
            defaults={
                'name': holiday_name,
                'description': f'Kenya public holiday - {holiday_name}',
                'is_recurring': True
            }
        )
        if created:
            print(f"  ✅ Created holiday: {holiday.name}")
    
    # Create pay cycle
    pay_cycle, created = PayCycle.objects.get_or_create(
        name='Monthly Payroll',
        defaults={
            'frequency': 'monthly',
            'start_date': date(2024, 1, 1),
            'end_date': date(2024, 1, 31),
            'pay_date': date(2024, 2, 5),
            'is_active': True
        }
    )
    if created:
        print(f"  ✅ Created pay cycle: {pay_cycle.name}")

def main():
    """Main function to populate all real data"""
    print("🚀 Starting Real Data Population for WorkFlo Backend...")
    print("=" * 60)
    
    try:
        # Create departments
        departments = create_departments()
        
        # Create employees
        employees = create_employees(departments)
        
        # Create system configuration
        create_system_data()
        
        # Create attendance records
        create_attendance_records(employees)
        
        print("\n" + "=" * 60)
        print("✅ Real Data Population Completed Successfully!")
        print(f"📊 Summary:")
        print(f"   - Departments: {Department.objects.count()}")
        print(f"   - Employees: {User.objects.count()}")
        print(f"   - Attendance Records: {AttendanceRecord.objects.count()}")
        print(f"   - Overtime Types: {OvertimeType.objects.count()}")
        print(f"   - Leave Types: {LeaveType.objects.count()}")
        print(f"   - Company Holidays: {CompanyHoliday.objects.count()}")
        
        print(f"\n🔐 Default Login Credentials:")
        print(f"   Username: john.doe")
        print(f"   Password: workflo2024")
        print(f"   Role: Admin")
        
        print(f"\n🌐 Next Steps:")
        print(f"   1. Start the Django server: python manage.py runserver")
        print(f"   2. Access admin panel: http://localhost:8000/admin/")
        print(f"   3. Test API endpoints: http://localhost:8000/api/")
        print(f"   4. Review attendance data and payroll calculations")
        
    except Exception as e:
        print(f"\n❌ Error during data population: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
