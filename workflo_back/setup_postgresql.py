#!/usr/bin/env python3
"""
PostgreSQL Setup Script for WorkFlo Backend
Creates database and user for the WorkFlo project
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def run_command(command, input_text=None, check=True):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            input=input_text,
            check=check
        )
        return result
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {command}")
        logger.error(f"Error: {e.stderr}")
        return e


def check_postgresql_status():
    """Check if PostgreSQL is running"""
    logger.info("🔍 Checking PostgreSQL status...")
    
    result = run_command("pg_isready", check=False)
    if result.returncode == 0:
        logger.info("✅ PostgreSQL is running")
        return True
    else:
        logger.error("❌ PostgreSQL is not running")
        return False


def create_database_and_user():
    """Create database and user for WorkFlo"""
    logger.info("🗄️ Setting up PostgreSQL database and user...")
    
    # Database configuration
    db_name = "workflo_db"
    db_user = "workflo_user"
    db_password = "workflo_password"
    
    # SQL commands to create database and user
    sql_commands = f"""
-- Create user if not exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '{db_user}') THEN
        CREATE USER {db_user} WITH PASSWORD '{db_password}';
    END IF;
END
$$;

-- Create database if not exists
SELECT 'CREATE DATABASE {db_name} OWNER {db_user}'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '{db_name}')\\gexec

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE {db_name} TO {db_user};
ALTER USER {db_user} CREATEDB;
"""
    
    # Try different connection methods
    connection_methods = [
        # Method 1: Connect as postgres user via sudo
        f"sudo -u postgres psql",
        # Method 2: Connect to local postgres database
        f"psql -d postgres",
        # Method 3: Connect with explicit host
        f"psql -h localhost -d postgres -U postgres",
    ]
    
    for method in connection_methods:
        logger.info(f"Trying connection method: {method}")
        try:
            result = run_command(f'echo "{sql_commands}" | {method}', check=False)
            if result.returncode == 0:
                logger.info("✅ Database and user created successfully")
                return True
            else:
                logger.warning(f"Method failed: {result.stderr}")
        except Exception as e:
            logger.warning(f"Method failed with exception: {str(e)}")
            continue
    
    logger.error("❌ Could not create database and user with any method")
    return False


def test_connection():
    """Test connection to the WorkFlo database"""
    logger.info("🔗 Testing database connection...")
    
    # Set environment variables for connection
    env = os.environ.copy()
    env.update({
        'PGDATABASE': 'workflo_db',
        'PGUSER': 'workflo_user',
        'PGPASSWORD': 'workflo_password',
        'PGHOST': 'localhost',
        'PGPORT': '5432'
    })
    
    # Test connection
    result = run_command(
        'psql -c "SELECT version();"',
        check=False
    )
    
    if result.returncode == 0:
        logger.info("✅ Database connection successful")
        logger.info(f"Database version: {result.stdout.strip()}")
        return True
    else:
        logger.error("❌ Database connection failed")
        logger.error(f"Error: {result.stderr}")
        return False


def create_env_file():
    """Create .env file with PostgreSQL configuration"""
    logger.info("📝 Creating .env file...")
    
    env_content = """# WorkFlo Backend Environment Variables
# PostgreSQL Configuration
DB_NAME=workflo_db
DB_USER=workflo_user
DB_PASSWORD=workflo_password
DB_HOST=localhost
DB_PORT=5432

# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,testserver

# Set to False to use PostgreSQL
USE_SQLITE=False

# CORS Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Cache & Redis (optional)
REDIS_URL=redis://127.0.0.1:6379/1

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
DEFAULT_FROM_EMAIL=<EMAIL>

# API Rate Limiting
ANON_RATE_LIMIT=100/hour
USER_RATE_LIMIT=1000/hour

# BioStar Integration (disabled for development)
USE_BIOSTAR_INTEGRATION=False
"""
    
    env_path = Path("workflo_back/.env")
    try:
        with open(env_path, 'w') as f:
            f.write(env_content)
        logger.info(f"✅ Created .env file at {env_path}")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to create .env file: {str(e)}")
        return False


def main():
    """Main setup function"""
    logger.info("🚀 Starting PostgreSQL setup for WorkFlo Backend...")
    
    # Check if PostgreSQL is running
    if not check_postgresql_status():
        logger.error("PostgreSQL is not running. Please start PostgreSQL service first.")
        sys.exit(1)
    
    # Create database and user
    if not create_database_and_user():
        logger.warning("Could not create database automatically. You may need to create it manually.")
        logger.info("Manual setup commands:")
        logger.info("sudo -u postgres psql")
        logger.info("CREATE DATABASE workflo_db;")
        logger.info("CREATE USER workflo_user WITH PASSWORD 'workflo_password';")
        logger.info("GRANT ALL PRIVILEGES ON DATABASE workflo_db TO workflo_user;")
        logger.info("ALTER USER workflo_user CREATEDB;")
    
    # Create .env file
    create_env_file()
    
    # Test connection
    if test_connection():
        logger.info("🎉 PostgreSQL setup completed successfully!")
        logger.info("You can now run migrations with: python manage.py migrate")
    else:
        logger.warning("Setup completed but connection test failed.")
        logger.info("You may need to configure PostgreSQL authentication manually.")
    
    logger.info("📋 Next steps:")
    logger.info("1. cd workflo_back")
    logger.info("2. source venv/bin/activate")
    logger.info("3. python manage.py migrate")
    logger.info("4. python server.py")


if __name__ == '__main__':
    main()
