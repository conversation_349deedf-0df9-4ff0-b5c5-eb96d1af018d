# BioStar Integration API Reference

## Authentication

All API endpoints require JWT authentication. Include the token in the Authorization header:

```http
Authorization: Bearer <your_jwt_token>
```

## Base URL

```
https://your-domain.com/api/
```

## Attendance Management Endpoints

### 1. Sync BioStar Data

Synchronize attendance data from BioStar for a specified date range.

**Endpoint:** `POST /attendance-records/sync_biostar/`

**Request Body:**
```json
{
    "start_date": "2024-01-01",
    "end_date": "2024-01-31"
}
```

**Response:**
```json
{
    "success": true,
    "message": "BioStar synchronization completed",
    "data": {
        "synced_count": 150,
        "attendance_records_created": 45,
        "overtime_detected": 12
    }
}
```

**Error Response:**
```json
{
    "success": false,
    "message": "Synchronization failed: Connection timeout"
}
```

### 2. Get Attendance Summary

Get attendance summary statistics for an employee or department.

**Endpoint:** `GET /attendance-records/attendance_summary/`

**Query Parameters:**
- `employee_id` (optional): Filter by specific employee
- `start_date` (optional): Start date (YYYY-MM-DD)
- `end_date` (optional): End date (YYYY-MM-DD)

**Example Request:**
```http
GET /attendance-records/attendance_summary/?employee_id=1&start_date=2024-01-01&end_date=2024-01-31
```

**Response:**
```json
{
    "period": {
        "start_date": "2024-01-01",
        "end_date": "2024-01-31",
        "working_days": 22
    },
    "summary": {
        "total_days": 20,
        "total_hours": 160.0,
        "total_regular_hours": 152.0,
        "total_overtime_hours": 8.0,
        "attendance_rate": 90.91
    }
}
```

### 3. Calculate Dynamic Salary

Calculate employee salary based on attendance data.

**Endpoint:** `POST /attendance-records/calculate_salary/`

**Request Body:**
```json
{
    "employee_id": 1,
    "start_date": "2024-01-01",
    "end_date": "2024-01-31"
}
```

**Response:**
```json
{
    "success": true,
    "calculation": {
        "employee_id": 1,
        "employee_name": "John Doe",
        "calculation_period": {
            "start_date": "2024-01-01",
            "end_date": "2024-01-31"
        },
        "salary_profile": {
            "basic_salary": 50000.00,
            "hourly_rate": 284.09,
            "allowances": 5000.00,
            "overtime_rate": 426.14
        },
        "working_days": {
            "total_days": 31,
            "working_days": 22,
            "weekend_days": 8,
            "holiday_days": 1,
            "expected_working_days": 22
        },
        "attendance_summary": {
            "days_present": 20,
            "days_late": 2,
            "days_absent": 2,
            "total_hours_worked": 160.0,
            "total_regular_hours": 152.0,
            "total_overtime_hours": 8.0,
            "attendance_rate": 90.91,
            "punctuality_rate": 90.0
        },
        "overtime_summary": {
            "total_overtime_hours": 8.0,
            "regular_overtime_hours": 6.0,
            "weekend_overtime_hours": 2.0,
            "holiday_overtime_hours": 0.0,
            "total_overtime_amount": 3409.12,
            "overtime_records_count": 3
        },
        "base_calculation": {
            "base_amount": 45454.55,
            "allowances": 5000.00,
            "expected_hours": 176.0,
            "actual_hours": 152.0,
            "calculation_method": "dynamic"
        },
        "deductions": {
            "late_penalty": 454.55,
            "absence_deduction": 4545.45,
            "leave_deduction": 0.0
        },
        "totals": {
            "gross_salary": 53863.67,
            "total_deductions": 5000.00,
            "net_salary": 48863.67
        },
        "calculation_method": "dynamic",
        "calculated_at": "2024-01-31T10:30:00Z"
    }
}
```

## BioStar Events Endpoints

### 1. Get Real-time Events

Fetch real-time events from BioStar API.

**Endpoint:** `GET /biostar-events/realtime_events/`

**Response:**
```json
{
    "success": true,
    "data": {
        "processed_count": 5
    }
}
```

### 2. Get Unprocessed Events

Get list of unprocessed BioStar events.

**Endpoint:** `GET /biostar-events/unprocessed_events/`

**Response:**
```json
{
    "count": 25,
    "events": [
        {
            "id": 1,
            "biostar_event_id": "12345",
            "employee": 1,
            "device_id": "101",
            "device_name": "Main Entrance",
            "event_type": "entry",
            "event_datetime": "2024-01-31T08:30:00Z",
            "location": "Building A",
            "processed": false
        }
    ]
}
```

### 3. Process Event Manually

Manually process a specific BioStar event.

**Endpoint:** `POST /biostar-events/{id}/process_event/`

**Response:**
```json
{
    "success": true,
    "message": "Event processed successfully",
    "result": {
        "created": true,
        "overtime_detected": false
    }
}
```

## BioStar Devices Endpoints

### 1. Check Connection Status

Test BioStar API connection and get device status.

**Endpoint:** `GET /biostar-devices/connection_status/`

**Response:**
```json
{
    "api_connection": {
        "connected": true,
        "response_time": 0.25,
        "device_count": 3,
        "mock_mode": false,
        "message": "Successfully connected to BioStar API"
    },
    "devices": [
        {
            "id": 1,
            "name": "Main Entrance",
            "status": "online",
            "last_seen": "2024-01-31T10:25:00Z",
            "location": "Building A - Ground Floor"
        },
        {
            "id": 2,
            "name": "Office Floor",
            "status": "online",
            "last_seen": "2024-01-31T10:24:00Z",
            "location": "Building A - 2nd Floor"
        }
    ]
}
```

### 2. Sync Devices

Synchronize device information from BioStar.

**Endpoint:** `POST /biostar-devices/sync_devices/`

**Response:**
```json
{
    "success": true,
    "message": "Device synchronization completed",
    "data": {
        "synced_count": 3
    }
}
```

## Error Responses

### Standard Error Format

```json
{
    "error": "Error message description",
    "code": "ERROR_CODE",
    "details": {
        "field": "Additional error details"
    }
}
```

### Common Error Codes

| Code | Description |
|------|-------------|
| `BIOSTAR_CONNECTION_FAILED` | Cannot connect to BioStar API |
| `AUTHENTICATION_FAILED` | Invalid BioStar credentials |
| `EMPLOYEE_NOT_FOUND` | Employee ID not found |
| `INVALID_DATE_RANGE` | Invalid start/end date format |
| `SALARY_PROFILE_MISSING` | Employee salary profile not configured |
| `OVERTIME_TYPE_MISSING` | No overtime type configured |

## Rate Limiting

API endpoints are rate-limited based on user role:

- **Employee**: 100 requests/hour
- **Supervisor**: 500 requests/hour  
- **Admin/HR**: 1000 requests/hour

Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1643723400
```

## Pagination

List endpoints support pagination:

**Query Parameters:**
- `page`: Page number (default: 1)
- `page_size`: Items per page (default: 20, max: 100)

**Response Format:**
```json
{
    "count": 150,
    "next": "https://api.example.com/endpoint/?page=2",
    "previous": null,
    "results": [...]
}
```

## Filtering and Sorting

### Attendance Records

**Available Filters:**
- `employee`: Employee ID
- `date`: Specific date (YYYY-MM-DD)
- `date__range`: Date range (start,end)
- `status`: Attendance status
- `biostar_synced`: Boolean filter

**Available Sorting:**
- `date`: Sort by date
- `employee__first_name`: Sort by employee name
- `total_hours`: Sort by hours worked

**Example:**
```http
GET /attendance-records/?employee=1&date__range=2024-01-01,2024-01-31&ordering=-date
```

### BioStar Events

**Available Filters:**
- `employee`: Employee ID
- `event_type`: Event type (entry/exit)
- `processed`: Boolean filter
- `event_datetime__range`: DateTime range

**Example:**
```http
GET /biostar-events/?processed=false&event_type=entry&ordering=-event_datetime
```

## Webhooks (Future Enhancement)

Webhook endpoints for real-time notifications:

### Event Types
- `attendance.created`: New attendance record
- `overtime.detected`: Overtime detected
- `overtime.approved`: Overtime approved
- `biostar.device_offline`: Device went offline

### Webhook Payload
```json
{
    "event": "attendance.created",
    "timestamp": "2024-01-31T10:30:00Z",
    "data": {
        "attendance_record_id": 123,
        "employee_id": 1,
        "date": "2024-01-31"
    }
}
```

## SDK Examples

### Python SDK Example

```python
import requests

class WorkFloAPI:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {'Authorization': f'Bearer {token}'}
    
    def sync_biostar(self, start_date, end_date):
        url = f"{self.base_url}/attendance-records/sync_biostar/"
        data = {"start_date": start_date, "end_date": end_date}
        response = requests.post(url, json=data, headers=self.headers)
        return response.json()
    
    def calculate_salary(self, employee_id, start_date, end_date):
        url = f"{self.base_url}/attendance-records/calculate_salary/"
        data = {
            "employee_id": employee_id,
            "start_date": start_date,
            "end_date": end_date
        }
        response = requests.post(url, json=data, headers=self.headers)
        return response.json()

# Usage
api = WorkFloAPI("https://api.workflo.com/api", "your_token")
result = api.calculate_salary(1, "2024-01-01", "2024-01-31")
```

### JavaScript SDK Example

```javascript
class WorkFloAPI {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl;
        this.headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
    }
    
    async syncBiostar(startDate, endDate) {
        const response = await fetch(`${this.baseUrl}/attendance-records/sync_biostar/`, {
            method: 'POST',
            headers: this.headers,
            body: JSON.stringify({
                start_date: startDate,
                end_date: endDate
            })
        });
        return response.json();
    }
    
    async calculateSalary(employeeId, startDate, endDate) {
        const response = await fetch(`${this.baseUrl}/attendance-records/calculate_salary/`, {
            method: 'POST',
            headers: this.headers,
            body: JSON.stringify({
                employee_id: employeeId,
                start_date: startDate,
                end_date: endDate
            })
        });
        return response.json();
    }
}

// Usage
const api = new WorkFloAPI('https://api.workflo.com/api', 'your_token');
const result = await api.calculateSalary(1, '2024-01-01', '2024-01-31');
```
