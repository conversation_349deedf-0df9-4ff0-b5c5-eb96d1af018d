#!/usr/bin/env python3
"""
Database Connection and Migration Test Script
Tests PostgreSQL connection, runs migrations, and validates the database setup
"""

import os
import sys
import logging
from pathlib import Path

# Add the project directory to Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(BASE_DIR))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'workflo_back.settings')

import django
django.setup()

from django.core.management import call_command
from django.db import connection, connections
from django.conf import settings
from django.contrib.auth import get_user_model
from django.test.utils import get_runner

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

User = get_user_model()


def test_database_connection():
    """Test database connectivity"""
    logger.info("🔍 Testing database connection...")
    
    try:
        # Test basic connection
        with connection.cursor() as cursor:
            # Use appropriate version query based on database engine
            db_engine = settings.DATABASES['default']['ENGINE']
            if 'postgresql' in db_engine:
                cursor.execute("SELECT version()")
                db_version = cursor.fetchone()[0]
                logger.info(f"✅ Connected to PostgreSQL: {db_version}")
            elif 'sqlite' in db_engine:
                cursor.execute("SELECT sqlite_version()")
                db_version = cursor.fetchone()[0]
                logger.info(f"✅ Connected to SQLite: {db_version}")
            else:
                cursor.execute("SELECT 1")
                logger.info(f"✅ Connected to database: {db_engine}")

        # Test connection settings
        db_settings = settings.DATABASES['default']
        logger.info(f"📊 Database: {db_settings['NAME']}")
        if db_settings.get('HOST'):
            logger.info(f"🏠 Host: {db_settings['HOST']}:{db_settings.get('PORT', 'default')}")
        if db_settings.get('USER'):
            logger.info(f"👤 User: {db_settings['USER']}")

        return True

    except Exception as e:
        logger.error(f"❌ Database connection failed: {str(e)}")
        return False


def test_migrations():
    """Test database migrations"""
    logger.info("🔄 Testing database migrations...")
    
    try:
        # Check migration status
        call_command('showmigrations', verbosity=1)
        
        # Make migrations
        logger.info("📝 Making migrations...")
        call_command('makemigrations', verbosity=1, interactive=False)
        
        # Apply migrations
        logger.info("⚡ Applying migrations...")
        call_command('migrate', verbosity=1, interactive=False)
        
        logger.info("✅ Migrations completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {str(e)}")
        return False


def test_model_operations():
    """Test basic model operations"""
    logger.info("🧪 Testing model operations...")
    
    try:
        # Test User model
        user_count = User.objects.count()
        logger.info(f"👥 Users in database: {user_count}")
        
        # Test creating a test user
        test_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'username': 'test_user',
                'first_name': 'Test',
                'last_name': 'User',
                'employee_id': 'TEST001',
                'role': 'employee'
            }
        )
        
        if created:
            test_user.set_password('testpass123')
            test_user.save()
            logger.info("✅ Test user created successfully")
        else:
            logger.info("ℹ️ Test user already exists")
        
        # Test other models
        from core.models.organization import Department
        from core.models.leave import LeaveType
        
        dept_count = Department.objects.count()
        leave_type_count = LeaveType.objects.count()
        
        logger.info(f"🏢 Departments: {dept_count}")
        logger.info(f"🏖️ Leave types: {leave_type_count}")
        
        logger.info("✅ Model operations test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Model operations test failed: {str(e)}")
        return False


def test_database_performance():
    """Test database performance"""
    logger.info("⚡ Testing database performance...")

    try:
        import time

        # Test query performance using Django ORM
        start_time = time.time()

        # Use Django ORM instead of raw SQL for compatibility
        user_count = User.objects.count()

        query_time = (time.time() - start_time) * 1000

        logger.info(f"📊 Query time: {query_time:.2f}ms")
        logger.info(f"👥 User count: {user_count}")

        if query_time < 100:
            logger.info("✅ Database performance is good")
        elif query_time < 500:
            logger.warning("⚠️ Database performance is acceptable")
        else:
            logger.warning("⚠️ Database performance is slow")

        return True

    except Exception as e:
        logger.error(f"❌ Performance test failed: {str(e)}")
        return False


def test_database_schema():
    """Test database schema integrity"""
    logger.info("🏗️ Testing database schema...")

    try:
        # Get database engine
        db_engine = settings.DATABASES['default']['ENGINE']

        # Get all tables using Django's introspection
        from django.db import connection
        table_names = connection.introspection.table_names()

        logger.info(f"📋 Found {len(table_names)} tables in database")

        # Check for key tables
        expected_tables = [
            'auth_user', 'core_department', 'core_employeeprofile',
            'core_leaveapplication', 'core_attendancerecord', 'core_payrollrecord'
        ]

        missing_tables = []
        for table in expected_tables:
            if table not in table_names:
                missing_tables.append(table)

        if missing_tables:
            logger.warning(f"⚠️ Missing tables: {missing_tables}")
        else:
            logger.info("✅ All expected tables found")

        # Test table structure using Django models
        try:
            # Test if we can query key models
            from core.models.organization import Department
            from core.models.employees import EmployeeProfile
            from core.models.leave import LeaveApplication

            dept_count = Department.objects.count()
            profile_count = EmployeeProfile.objects.count()
            leave_count = LeaveApplication.objects.count()

            logger.info(f"📊 Table data: Departments({dept_count}), Profiles({profile_count}), Leaves({leave_count})")

        except Exception as model_error:
            logger.warning(f"⚠️ Model query test failed: {str(model_error)}")

        # Database-specific constraint checking
        if 'postgresql' in db_engine:
            try:
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT COUNT(*)
                        FROM information_schema.table_constraints
                        WHERE table_schema = 'public'
                    """)
                    constraint_count = cursor.fetchone()[0]
                    logger.info(f"🔒 PostgreSQL constraints: {constraint_count}")
            except Exception:
                logger.info("ℹ️ Could not check PostgreSQL constraints")

        return True

    except Exception as e:
        logger.error(f"❌ Schema test failed: {str(e)}")
        return False


def run_comprehensive_tests():
    """Run all database tests"""
    logger.info("🚀 Starting comprehensive database tests...")
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Database Migrations", test_migrations),
        ("Model Operations", test_model_operations),
        ("Database Performance", test_database_performance),
        ("Database Schema", test_database_schema),
    ]
    
    passed_tests = 0
    failed_tests = []
    
    for test_name, test_function in tests:
        logger.info(f"\n📋 Running: {test_name}")
        try:
            if test_function():
                passed_tests += 1
                logger.info(f"✅ {test_name} - PASSED")
            else:
                failed_tests.append(test_name)
                logger.error(f"❌ {test_name} - FAILED")
        except Exception as e:
            failed_tests.append(test_name)
            logger.error(f"❌ {test_name} - ERROR: {str(e)}")
    
    # Summary
    logger.info(f"\n📊 Test Results Summary:")
    logger.info(f"✅ Passed: {passed_tests}/{len(tests)}")
    logger.info(f"❌ Failed: {len(failed_tests)}/{len(tests)}")
    
    if failed_tests:
        logger.error(f"Failed tests: {', '.join(failed_tests)}")
        return False
    else:
        logger.info("🎉 All database tests passed!")
        return True


def main():
    """Main entry point"""
    try:
        success = run_comprehensive_tests()
        if success:
            logger.info("🎉 Database testing completed successfully!")
            sys.exit(0)
        else:
            logger.error("❌ Database testing failed!")
            sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
