#!/usr/bin/env python
"""
BioStar Integration Test Script
Tests all BioStar API functionality including mock mode and real API calls
"""

import os
import sys
import django
from datetime import datetime, timedelta, date

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'workflo_back.settings')
django.setup()

def test_biostar_mock_mode():
    """Test BioStar API in mock mode"""
    print("=" * 60)
    print("TESTING BIOSTAR API - MOCK MODE")
    print("=" * 60)
    
    # Enable mock mode
    os.environ['BIOSTAR_MOCK_MODE'] = 'True'
    
    # Import after setting environment variable
    from core.services.biostar_api import BioStarAPI
    
    # Create new instance with mock mode
    api = BioStarAPI()
    
    try:
        # Test 1: Connection Test
        print("\n1. Testing API Connection (Mock Mode)...")
        connection_result = api.test_connection()
        print(f"   Connection Result: {connection_result}")
        assert connection_result['connected'] == True
        assert connection_result['mock_mode'] == True
        print("   ✅ Connection test passed")
        
        # Test 2: Authentication
        print("\n2. Testing Authentication...")
        auth_result = api.authenticate()
        print(f"   Authentication Result: {auth_result}")
        assert auth_result == True
        print("   ✅ Authentication passed")
        
        # Test 3: Get Users
        print("\n3. Testing Get Users...")
        users = api.get_users(limit=5)
        print(f"   Found {len(users)} users")
        for user in users:
            print(f"   - User: {user.get('name')} (ID: {user.get('id')})")
        assert len(users) > 0
        print("   ✅ Get users passed")
        
        # Test 4: Get Devices
        print("\n4. Testing Get Devices...")
        devices = api.get_devices()
        print(f"   Found {len(devices)} devices")
        for device in devices:
            print(f"   - Device: {device.get('name')} (Status: {device.get('status')})")
        assert len(devices) > 0
        print("   ✅ Get devices passed")
        
        # Test 5: Get Events
        print("\n5. Testing Get Events...")
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=24)
        events = api.get_events(start_time, end_time, limit=10)
        print(f"   Found {len(events)} events")
        for event in events[:3]:  # Show first 3 events
            print(f"   - Event: {event.get('event_type')} at {event.get('datetime')}")
        assert len(events) > 0
        print("   ✅ Get events passed")
        
        # Test 6: Real-time Events
        print("\n6. Testing Real-time Events...")
        realtime_events = api.get_realtime_events()
        print(f"   Found {len(realtime_events)} real-time events")
        print("   ✅ Real-time events passed")
        
        print("\n🎉 All Mock Mode Tests Passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Mock Mode Test Failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_biostar_sync_service():
    """Test BioStar Sync Service"""
    print("\n" + "=" * 60)
    print("TESTING BIOSTAR SYNC SERVICE")
    print("=" * 60)
    
    try:
        from core.services.biostar_sync import BioStarSyncService
        
        sync_service = BioStarSyncService()
        
        # Test 1: Sync Devices
        print("\n1. Testing Device Sync...")
        device_result = sync_service.sync_devices()
        print(f"   Device Sync Result: {device_result}")
        print("   ✅ Device sync passed")
        
        # Test 2: Sync Users
        print("\n2. Testing User Sync...")
        user_result = sync_service.sync_users()
        print(f"   User Sync Result: {user_result}")
        print("   ✅ User sync passed")
        
        # Test 3: Sync Events
        print("\n3. Testing Event Sync...")
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        event_result = sync_service.sync_events(start_time, end_time)
        print(f"   Event Sync Result: {event_result}")
        print("   ✅ Event sync passed")
        
        print("\n🎉 All Sync Service Tests Passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Sync Service Test Failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_salary_calculator():
    """Test Dynamic Salary Calculator"""
    print("\n" + "=" * 60)
    print("TESTING DYNAMIC SALARY CALCULATOR")
    print("=" * 60)
    
    try:
        from core.services.salary_calculator import DynamicSalaryCalculator
        from core.models.auth import User
        from core.models.employees import SalaryProfile
        from core.models.attendance import AttendanceRecord
        
        calculator = DynamicSalaryCalculator()
        
        # Create test employee if doesn't exist
        test_employee, created = User.objects.get_or_create(
            employee_id='TEST001',
            defaults={
                'username': 'testuser001',
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'Employee',
                'role': 'employee'
            }
        )
        
        # Create salary profile if doesn't exist
        salary_profile, created = SalaryProfile.objects.get_or_create(
            employee=test_employee,
            defaults={
                'basic_salary': 50000.00,
                'hourly_rate': 284.09,
                'allowances': 5000.00,
                'overtime_rate': 426.14,
                'currency': 'KSH'
            }
        )
        
        print(f"\n1. Testing with employee: {test_employee.first_name} {test_employee.last_name}")
        print(f"   Basic Salary: KSH {salary_profile.basic_salary}")
        
        # Test salary calculation
        start_date = date.today().replace(day=1)  # First day of current month
        end_date = date.today()
        
        print(f"\n2. Calculating salary for period: {start_date} to {end_date}")
        
        calculation = calculator.calculate_employee_salary(test_employee, start_date, end_date)
        
        print(f"\n3. Salary Calculation Results:")
        print(f"   Employee: {calculation['employee_name']}")
        print(f"   Period: {calculation['calculation_period']['start_date']} to {calculation['calculation_period']['end_date']}")
        print(f"   Working Days: {calculation['working_days']['working_days']}")
        print(f"   Attendance Rate: {calculation['attendance_summary']['attendance_rate']:.2f}%")
        print(f"   Total Hours: {calculation['attendance_summary']['total_hours_worked']}")
        print(f"   Overtime Hours: {calculation['attendance_summary']['total_overtime_hours']}")
        print(f"   Gross Salary: KSH {calculation['totals']['gross_salary']:.2f}")
        print(f"   Deductions: KSH {calculation['totals']['total_deductions']:.2f}")
        print(f"   Net Salary: KSH {calculation['totals']['net_salary']:.2f}")
        print(f"   Calculation Method: {calculation['calculation_method']}")
        
        assert 'totals' in calculation
        assert 'net_salary' in calculation['totals']
        print("   ✅ Salary calculation passed")
        
        print("\n🎉 Salary Calculator Tests Passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Salary Calculator Test Failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_attendance_models():
    """Test Enhanced Attendance Models"""
    print("\n" + "=" * 60)
    print("TESTING ENHANCED ATTENDANCE MODELS")
    print("=" * 60)
    
    try:
        from core.models.attendance import AttendanceRecord, BiostarEvent, BiostarDevice
        from core.models.auth import User
        from django.utils import timezone
        
        # Get or create test employee
        test_employee, created = User.objects.get_or_create(
            employee_id='TEST001',
            defaults={
                'username': 'testuser001',
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'Employee',
                'role': 'employee'
            }
        )
        
        print(f"\n1. Testing with employee: {test_employee.first_name} {test_employee.last_name}")
        
        # Test 2: Create BioStar Device
        print("\n2. Creating BioStar Device...")
        device, created = BiostarDevice.objects.get_or_create(
            biostar_device_id='TEST_DEVICE_001',
            defaults={
                'name': 'Test Main Entrance',
                'ip_address': '*************',
                'port': 1470,
                'location': 'Test Building - Ground Floor',
                'device_type': 'Access Control',
                'status': 'online'
            }
        )
        print(f"   Device: {device.name} (Status: {device.status})")
        print("   ✅ Device creation passed")
        
        # Test 3: Create BioStar Events
        print("\n3. Creating BioStar Events...")
        now = timezone.now()
        
        # Entry event
        entry_event, created = BiostarEvent.objects.get_or_create(
            biostar_event_id='TEST_EVENT_001',
            defaults={
                'employee': test_employee,
                'device_id': device.biostar_device_id,
                'device_name': device.name,
                'event_type': 'entry',
                'event_datetime': now.replace(hour=8, minute=30),
                'location': device.location,
                'processed': False
            }
        )
        
        # Exit event (with overtime)
        exit_event, created = BiostarEvent.objects.get_or_create(
            biostar_event_id='TEST_EVENT_002',
            defaults={
                'employee': test_employee,
                'device_id': device.biostar_device_id,
                'device_name': device.name,
                'event_type': 'exit',
                'event_datetime': now.replace(hour=18, minute=30),  # 10 hours = 2 hours overtime
                'location': device.location,
                'processed': False
            }
        )
        
        print(f"   Entry Event: {entry_event.event_datetime}")
        print(f"   Exit Event: {exit_event.event_datetime}")
        print("   ✅ Event creation passed")
        
        # Test 4: Create Attendance Record
        print("\n4. Creating Attendance Record...")
        attendance, created = AttendanceRecord.objects.get_or_create(
            employee=test_employee,
            date=now.date(),
            defaults={
                'check_in': entry_event.event_datetime,
                'check_out': exit_event.event_datetime,
                'status': 'present'
            }
        )
        
        # Calculate hours
        attendance.calculate_hours()
        
        print(f"   Date: {attendance.date}")
        print(f"   Check-in: {attendance.check_in}")
        print(f"   Check-out: {attendance.check_out}")
        print(f"   Total Hours: {attendance.total_hours}")
        print(f"   Regular Hours: {attendance.regular_hours}")
        print(f"   Overtime Hours: {attendance.overtime_hours}")
        print(f"   Status: {attendance.status}")
        
        # Test overtime eligibility
        overtime_eligibility = attendance.get_overtime_eligibility()
        print(f"   Overtime Eligible: {overtime_eligibility['eligible']}")
        if overtime_eligibility['eligible']:
            print(f"   Overtime Hours: {overtime_eligibility['overtime_hours']}")
            print(f"   Weekend: {overtime_eligibility['is_weekend']}")
            print(f"   Holiday: {overtime_eligibility['is_holiday']}")
        
        print("   ✅ Attendance record passed")
        
        print("\n🎉 Attendance Model Tests Passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Attendance Model Test Failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_overtime_management():
    """Test Overtime Management"""
    print("\n" + "=" * 60)
    print("TESTING OVERTIME MANAGEMENT")
    print("=" * 60)
    
    try:
        from core.models.overtime import OvertimeType, OvertimeRequest
        from core.models.auth import User
        from core.models.attendance import AttendanceRecord
        from django.utils import timezone
        
        # Get test employee
        test_employee = User.objects.get(employee_id='TEST001')
        
        print(f"\n1. Testing with employee: {test_employee.first_name} {test_employee.last_name}")
        
        # Test 2: Create Overtime Type
        print("\n2. Creating Overtime Type...")
        overtime_type, created = OvertimeType.objects.get_or_create(
            name='Test Standard Overtime',
            defaults={
                'description': 'Test overtime type',
                'rate_multiplier': 1.5,
                'max_hours_per_day': 4,
                'requires_pre_approval': True,
                'auto_approve_threshold': 2,
                'is_active': True
            }
        )
        print(f"   Overtime Type: {overtime_type.name}")
        print(f"   Rate Multiplier: {overtime_type.rate_multiplier}x")
        print(f"   Auto-approve Threshold: {overtime_type.auto_approve_threshold} hours")
        print("   ✅ Overtime type creation passed")
        
        # Test 3: Create Overtime Request (Auto-detected)
        print("\n3. Creating Auto-detected Overtime Request...")
        
        # Get attendance record
        attendance = AttendanceRecord.objects.filter(employee=test_employee).first()
        
        overtime_request, created = OvertimeRequest.objects.get_or_create(
            employee=test_employee,
            overtime_type=overtime_type,
            request_date=timezone.now().date(),
            defaults={
                'planned_start_time': timezone.now().time().replace(hour=8, minute=30),
                'planned_end_time': timezone.now().time().replace(hour=18, minute=30),
                'planned_hours': 2.0,  # 2 hours overtime
                'reason': 'Auto-detected overtime from BioStar attendance',
                'auto_detected': True,
                'biostar_event_id': 'TEST_EVENT_002',
                'attendance_record': attendance
            }
        )
        
        print(f"   Request Date: {overtime_request.request_date}")
        print(f"   Planned Hours: {overtime_request.planned_hours}")
        print(f"   Auto-detected: {overtime_request.auto_detected}")
        print(f"   Status: {overtime_request.status}")
        
        # Test auto-approval
        print("\n4. Testing Auto-approval...")
        approval_status = overtime_request.get_approval_status()
        print(f"   Can Auto-approve: {approval_status['can_auto_approve']}")
        
        if approval_status['can_auto_approve']:
            auto_approved = overtime_request.auto_approve_if_eligible()
            print(f"   Auto-approval Result: {auto_approved}")
            if auto_approved:
                print(f"   New Status: {overtime_request.status}")
                print(f"   Approved At: {overtime_request.supervisor_approved_at}")
        
        print("   ✅ Overtime management passed")
        
        print("\n🎉 Overtime Management Tests Passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Overtime Management Test Failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_real_biostar_api():
    """Test Real BioStar API (if credentials are available)"""
    print("\n" + "=" * 60)
    print("TESTING REAL BIOSTAR API")
    print("=" * 60)
    
    # Disable mock mode
    os.environ['BIOSTAR_MOCK_MODE'] = 'False'
    
    try:
        from core.services.biostar_api import BioStarAPI
        
        # Create new instance without mock mode
        api = BioStarAPI()
        
        print("\n1. Testing Real API Connection...")
        connection_result = api.test_connection()
        print(f"   Connection Result: {connection_result}")
        
        if connection_result['connected']:
            print("   ✅ Real API connection successful!")
            
            # Test authentication
            print("\n2. Testing Real Authentication...")
            auth_result = api.authenticate()
            print(f"   Authentication: {auth_result}")
            
            if auth_result:
                # Test getting users
                print("\n3. Testing Real Get Users...")
                users = api.get_users(limit=3)
                print(f"   Found {len(users)} users")
                for user in users:
                    print(f"   - {user.get('name', 'Unknown')} (ID: {user.get('id', 'N/A')})")
                
                # Test getting devices
                print("\n4. Testing Real Get Devices...")
                devices = api.get_devices()
                print(f"   Found {len(devices)} devices")
                for device in devices:
                    print(f"   - {device.get('name', 'Unknown')} (Status: {device.get('status', 'N/A')})")
                
                print("\n🎉 Real API Tests Passed!")
                return True
            else:
                print("   ❌ Real API authentication failed")
                return False
        else:
            print(f"   ⚠️  Real API connection failed: {connection_result['message']}")
            print("   This is expected if BioStar server is not accessible or credentials are incorrect")
            return False
            
    except Exception as e:
        print(f"\n❌ Real API Test Failed: {str(e)}")
        print("   This is expected if BioStar server is not accessible")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting BioStar Integration Tests...")
    
    test_results = []
    
    # Run all tests
    test_results.append(("Mock Mode API", test_biostar_mock_mode()))
    test_results.append(("Sync Service", test_biostar_sync_service()))
    test_results.append(("Salary Calculator", test_salary_calculator()))
    test_results.append(("Attendance Models", test_attendance_models()))
    test_results.append(("Overtime Management", test_overtime_management()))
    test_results.append(("Real API", test_real_biostar_api()))
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    print(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests completed successfully!")
        print("\nBioStar integration is working correctly!")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
