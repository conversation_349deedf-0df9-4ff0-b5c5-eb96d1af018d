#!/usr/bin/env python3
"""
WorkFlo Backend Production Server for Render.com Deployment
Comprehensive server setup with database management, health monitoring, and cron jobs
"""

import os
import sys
import logging
import signal
import time
import threading
from pathlib import Path
from datetime import datetime

# Add the project directory to Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(BASE_DIR))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'workflo_back.settings')

import django
django.setup()

from django.core.management import execute_from_command_line, call_command
from django.core.wsgi import get_wsgi_application
from django.db import connection, connections
from django.conf import settings
from django.contrib.auth import get_user_model
from core.monitoring import SystemHealthMonitor
from core.cron import (
    daily_cleanup, monthly_reports, sync_biostar_events,
    weekly_backup, process_notifications, system_health_check
)

# Configure logging for production
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Add file logging if possible
try:
    file_handler = logging.FileHandler('/tmp/server.log')
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logging.getLogger().addHandler(file_handler)
except (OSError, PermissionError):
    # If we can't write to file, just use console logging
    pass

logger = logging.getLogger(__name__)

User = get_user_model()


class WorkFloServer:
    """Main server class for WorkFlo Backend"""
    
    def __init__(self):
        self.running = False
        self.cron_thread = None
        self.health_monitor = SystemHealthMonitor()
        
    def check_database_connection(self):
        """Test database connectivity"""
        logger.info("🔍 Testing database connection...")
        
        try:
            # Test database connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                
            if result:
                logger.info("✅ Database connection successful")
                return True
            else:
                logger.error("❌ Database connection failed - no result")
                return False
                
        except Exception as e:
            logger.error(f"❌ Database connection failed: {str(e)}")
            return False
    
    def run_migrations(self):
        """Run database migrations"""
        logger.info("🔄 Running database migrations...")
        
        try:
            # Make migrations
            call_command('makemigrations', verbosity=1, interactive=False)
            
            # Apply migrations
            call_command('migrate', verbosity=1, interactive=False)
            
            logger.info("✅ Database migrations completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Migration failed: {str(e)}")
            return False
    
    def collect_static_files(self):
        """Collect static files for production"""
        logger.info("📁 Collecting static files...")
        
        try:
            call_command('collectstatic', verbosity=1, interactive=False, clear=True)
            logger.info("✅ Static files collected successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Static file collection failed: {str(e)}")
            return False
    
    def create_superuser(self):
        """Create superuser if it doesn't exist"""
        logger.info("👤 Creating superuser...")
        
        try:
            if not User.objects.filter(is_superuser=True).exists():
                superuser = User.objects.create_user(
                    email='<EMAIL>',
                    username='admin',
                    first_name='System',
                    last_name='Administrator',
                    employee_id='EMP0001',
                    role='admin',
                    is_staff=True,
                    is_superuser=True,
                    is_active=True
                )
                superuser.set_password('admin123')
                superuser.save()
                logger.info("✅ Superuser created: <EMAIL> / admin123")
            else:
                logger.info("ℹ️ Superuser already exists")
            return True
            
        except Exception as e:
            logger.error(f"❌ Superuser creation failed: {str(e)}")
            return False
    
    def populate_sample_data(self):
        """Populate database with sample data"""
        logger.info("📊 Populating sample data...")
        
        try:
            # Check if sample data already exists
            if User.objects.filter(role__in=['hr', 'supervisor', 'accountant', 'employee']).count() >= 8:
                logger.info("ℹ️ Sample data already exists")
                return True
            
            # Run the populate sample data command
            call_command('populate_sample_data', verbosity=1)
            
            # Create additional test users for different roles
            self.create_additional_test_users()
            
            logger.info("✅ Sample data populated successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Sample data population failed: {str(e)}")
            return False
    
    def create_additional_test_users(self):
        """Create additional test users with different roles"""
        logger.info("👥 Creating additional test users...")
        
        from core.models.organization import Department
        from core.models.employees import EmployeeProfile, SalaryProfile
        from core.models.leave import LeaveType, LeaveBalance
        from datetime import date, timedelta
        
        try:
            # Get or create departments
            hr_dept, _ = Department.objects.get_or_create(
                name='Human Resources',
                defaults={'description': 'HR Department'}
            )
            it_dept, _ = Department.objects.get_or_create(
                name='Information Technology',
                defaults={'description': 'IT Department'}
            )
            finance_dept, _ = Department.objects.get_or_create(
                name='Finance & Accounting',
                defaults={'description': 'Finance Department'}
            )
            
            additional_users = [
                {
                    'email': '<EMAIL>',
                    'username': 'supervisor',
                    'first_name': 'John',
                    'last_name': 'Supervisor',
                    'employee_id': 'EMP0006',
                    'role': 'supervisor',
                    'department': it_dept,
                    'job_title': 'Team Supervisor',
                    'salary': 110000
                },
                {
                    'email': '<EMAIL>',
                    'username': 'hr_staff',
                    'first_name': 'Mary',
                    'last_name': 'HR Staff',
                    'employee_id': 'EMP0007',
                    'role': 'hr',
                    'department': hr_dept,
                    'job_title': 'HR Specialist',
                    'salary': 85000
                },
                {
                    'email': '<EMAIL>',
                    'username': 'employee2',
                    'first_name': 'Alice',
                    'last_name': 'Employee',
                    'employee_id': 'EMP0008',
                    'role': 'employee',
                    'department': finance_dept,
                    'job_title': 'Finance Assistant',
                    'salary': 70000
                },
                {
                    'email': '<EMAIL>',
                    'username': 'employee3',
                    'first_name': 'Bob',
                    'last_name': 'Developer',
                    'employee_id': 'EMP0009',
                    'role': 'employee',
                    'department': it_dept,
                    'job_title': 'Junior Developer',
                    'salary': 75000
                }
            ]
            
            for user_data in additional_users:
                if not User.objects.filter(email=user_data['email']).exists():
                    # Create user
                    user = User.objects.create_user(
                        email=user_data['email'],
                        username=user_data['username'],
                        first_name=user_data['first_name'],
                        last_name=user_data['last_name'],
                        employee_id=user_data['employee_id'],
                        role=user_data['role'],
                        is_active=True
                    )
                    user.set_password('password123')
                    user.save()
                    
                    # Create employee profile
                    EmployeeProfile.objects.create(
                        user=user,
                        department=user_data['department'],
                        job_title=user_data['job_title'],
                        hire_date=date.today() - timedelta(days=100),
                        employment_type='full_time',
                        work_location='office'
                    )
                    
                    # Create salary profile
                    SalaryProfile.objects.create(
                        employee=user,
                        basic_salary=user_data['salary'],
                        allowances=user_data['salary'] * 0.15,
                        pay_frequency='monthly',
                        effective_from=date.today() - timedelta(days=100)
                    )
                    
                    # Create leave balances
                    current_year = date.today().year
                    leave_types = LeaveType.objects.all()
                    
                    for leave_type in leave_types:
                        if leave_type.max_days_per_year:
                            LeaveBalance.objects.get_or_create(
                                employee=user,
                                leave_type=leave_type,
                                year=current_year,
                                defaults={'allocated_days': leave_type.max_days_per_year}
                            )
                    
                    logger.info(f"✅ Created user: {user.email}")
            
        except Exception as e:
            logger.error(f"❌ Additional user creation failed: {str(e)}")

    def setup_cron_jobs(self):
        """Setup and start cron job scheduler"""
        logger.info("⏰ Setting up cron jobs...")

        def run_cron_scheduler():
            """Background thread for running cron jobs"""
            import schedule

            # Schedule cron jobs
            schedule.every().day.at("02:00").do(daily_cleanup)
            schedule.every().monday.at("01:00").do(monthly_reports)  # Run monthly on first Monday
            schedule.every(15).minutes.do(sync_biostar_events)
            schedule.every().monday.at("01:00").do(weekly_backup)
            schedule.every(30).minutes.do(process_notifications)
            schedule.every(5).minutes.do(system_health_check)

            logger.info("✅ Cron jobs scheduled successfully")

            while self.running:
                try:
                    schedule.run_pending()
                    time.sleep(60)  # Check every minute
                except Exception as e:
                    logger.error(f"❌ Cron job error: {str(e)}")
                    time.sleep(60)

        try:
            self.cron_thread = threading.Thread(target=run_cron_scheduler, daemon=True)
            self.cron_thread.start()
            logger.info("✅ Cron scheduler started")
            return True

        except Exception as e:
            logger.error(f"❌ Cron setup failed: {str(e)}")
            return False

    def perform_health_check(self):
        """Perform comprehensive health check"""
        logger.info("🏥 Performing system health check...")

        try:
            health_report = self.health_monitor.get_comprehensive_health()

            if health_report['overall_status'] == 'healthy':
                logger.info("✅ System health check passed")
            elif health_report['overall_status'] == 'warning':
                logger.warning("⚠️ System health check shows warnings")
            else:
                logger.error("❌ System health check failed")

            # Log detailed health information
            for component, status in health_report['components'].items():
                if isinstance(status, dict) and 'status' in status:
                    logger.info(f"  {component}: {status['status']}")

            return health_report['overall_status'] in ['healthy', 'warning']

        except Exception as e:
            logger.error(f"❌ Health check failed: {str(e)}")
            return False

    def initialize_server(self):
        """Initialize the server with all necessary setup"""
        logger.info("🚀 Initializing WorkFlo Backend Server...")

        initialization_steps = [
            ("Database Connection", self.check_database_connection),
            ("Database Migrations", self.run_migrations),
            ("Static Files Collection", self.collect_static_files),
            ("Superuser Creation", self.create_superuser),
            ("Sample Data Population", self.populate_sample_data),
            ("Health Check", self.perform_health_check),
            ("Cron Jobs Setup", self.setup_cron_jobs),
        ]

        failed_steps = []

        for step_name, step_function in initialization_steps:
            logger.info(f"📋 Running: {step_name}")
            try:
                if not step_function():
                    failed_steps.append(step_name)
                    logger.error(f"❌ Failed: {step_name}")
                else:
                    logger.info(f"✅ Completed: {step_name}")
            except Exception as e:
                failed_steps.append(step_name)
                logger.error(f"❌ Error in {step_name}: {str(e)}")

        if failed_steps:
            logger.error(f"❌ Server initialization failed. Failed steps: {', '.join(failed_steps)}")
            return False

        logger.info("🎉 Server initialization completed successfully!")
        return True

    def start_server(self):
        """Start the production server"""
        logger.info("🌟 Starting WorkFlo Backend Production Server...")

        # Initialize server
        if not self.initialize_server():
            logger.error("❌ Server initialization failed. Exiting.")
            sys.exit(1)

        self.running = True

        # Setup signal handlers for graceful shutdown
        def signal_handler(signum, frame):
            logger.info(f"📡 Received signal {signum}. Shutting down gracefully...")
            self.shutdown_server()

        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)

        # Get WSGI application
        application = get_wsgi_application()

        # Start the server
        port = int(os.environ.get('PORT', 8000))
        host = os.environ.get('HOST', '0.0.0.0')

        logger.info(f"🌐 Server starting on {host}:{port}")
        logger.info("📊 Health monitoring endpoint: /api/health/")
        logger.info("📈 System metrics endpoint: /api/metrics/")
        logger.info("📚 API documentation: /api/docs/")

        try:
            # Use gunicorn for production
            from gunicorn.app.wsgiapp import WSGIApplication

            # Gunicorn configuration
            gunicorn_options = {
                'bind': f'{host}:{port}',
                'workers': int(os.environ.get('WEB_CONCURRENCY', 2)),
                'worker_class': 'sync',
                'worker_connections': 1000,
                'max_requests': 1000,
                'max_requests_jitter': 100,
                'timeout': 30,
                'keepalive': 2,
                'preload_app': True,
                'accesslog': '-',
                'errorlog': '-',
                'access_log_format': '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s',
                'capture_output': True,
                'enable_stdio_inheritance': True,
            }

            # Create and run Gunicorn application
            class StandaloneApplication(WSGIApplication):
                def __init__(self, app, options=None):
                    self.options = options or {}
                    self.application = app
                    super().__init__()

                def load_config(self):
                    for key, value in self.options.items():
                        self.cfg.set(key.lower(), value)

                def load(self):
                    return self.application

            StandaloneApplication(application, gunicorn_options).run()

        except ImportError:
            # Fallback to Django development server
            logger.warning("⚠️ Gunicorn not available, using Django development server")
            from django.core.management.commands.runserver import Command as RunServerCommand

            command = RunServerCommand()
            command.handle(addrport=f'{host}:{port}', verbosity=1, use_reloader=False)

        except Exception as e:
            logger.error(f"❌ Server startup failed: {str(e)}")
            self.shutdown_server()
            sys.exit(1)

    def shutdown_server(self):
        """Gracefully shutdown the server"""
        logger.info("🛑 Shutting down server...")

        self.running = False

        # Wait for cron thread to finish
        if self.cron_thread and self.cron_thread.is_alive():
            logger.info("⏰ Waiting for cron jobs to finish...")
            self.cron_thread.join(timeout=30)

        # Close database connections
        connections.close_all()

        logger.info("✅ Server shutdown completed")


def main():
    """Main entry point"""
    try:
        server = WorkFloServer()
        server.start_server()
    except KeyboardInterrupt:
        logger.info("🛑 Server interrupted by user")
    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
