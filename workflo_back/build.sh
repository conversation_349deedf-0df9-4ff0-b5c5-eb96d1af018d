#!/usr/bin/env bash
# Build script for Render deployment

set -o errexit  # exit on error

echo "🚀 Starting WorkFlo Backend Build Process..."

# Install dependencies
echo "📦 Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p logs
mkdir -p staticfiles
mkdir -p media

# Set environment variable to use SQLite for build
export USE_SQLITE=True

# Collect static files
echo "� Collecting static files..."
python manage.py collectstatic --noinput

echo "⚠️ Skipping database migrations during build"
echo "   Migrations will run when the application starts with proper database connection"

echo "✅ Build process completed successfully!"
