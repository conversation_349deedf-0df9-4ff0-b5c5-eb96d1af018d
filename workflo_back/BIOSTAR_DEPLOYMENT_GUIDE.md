# BioStar Integration Deployment Guide

## Prerequisites

### System Requirements
- Python 3.8+
- PostgreSQL 12+
- Redis (optional, for caching)
- Network access to BioStar 2 API

### BioStar 2 Requirements
- BioStar 2 server with API access
- Valid API credentials (username/password)
- Network connectivity between Django server and BioStar server

## Installation Steps

### 1. Environment Setup

Create and activate virtual environment:
```bash
cd workflo_back
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

Install dependencies:
```bash
pip install -r requirements.txt
```

### 2. Environment Configuration

Create `.env` file in the project root:
```env
# Database Configuration
DB_NAME=workflo_db
DB_USER=workflo_user
DB_PASSWORD=your_secure_password
DB_HOST=localhost
DB_PORT=5432

# BioStar API Configuration
BIOSTAR_BASE_URL=https://ns.biostar2.com
BIOSTAR_USERNAME=dev
BIOSTAR_PASSWORD=d3vt3@ms
BIOSTAR_API_VERSION=v1
BIOSTAR_TIMEOUT=30
BIOSTAR_MAX_RETRIES=3
BIOSTAR_SYNC_INTERVAL=300
BIOSTAR_ENABLE_REAL_TIME=True
BIOSTAR_MOCK_MODE=False

# Dynamic Salary Configuration
DYNAMIC_SALARY_ENABLED=True
STANDARD_WORK_HOURS=8
STANDARD_WORK_DAYS=22
OVERTIME_THRESHOLD=8
OVERTIME_MULTIPLIER=1.5
WEEKEND_MULTIPLIER=2.0
HOLIDAY_MULTIPLIER=2.5
LATE_PENALTY_ENABLED=True
LATE_PENALTY_RATE=0.1
ABSENCE_DEDUCTION_ENABLED=True

# Security
SECRET_KEY=your_very_secure_secret_key
DEBUG=False
ALLOWED_HOSTS=your-domain.com,localhost

# Email Configuration (for notifications)
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379/0
```

### 3. Database Setup

Create PostgreSQL database:
```sql
CREATE DATABASE workflo_db;
CREATE USER workflo_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE workflo_db TO workflo_user;
```

Run migrations:
```bash
python manage.py makemigrations
python manage.py migrate
```

Create superuser:
```bash
python manage.py createsuperuser
```

### 4. Initial Data Setup

Create default overtime types:
```bash
python manage.py shell
```

```python
from core.models.overtime import OvertimeType

# Create default overtime types
OvertimeType.objects.create(
    name="Standard Overtime",
    description="Regular overtime work",
    rate_multiplier=1.5,
    max_hours_per_day=4,
    max_hours_per_week=20,
    requires_pre_approval=True,
    auto_approve_threshold=2,
    is_active=True
)

OvertimeType.objects.create(
    name="Weekend Overtime",
    description="Weekend work",
    rate_multiplier=2.0,
    max_hours_per_day=8,
    requires_pre_approval=True,
    auto_approve_threshold=1,
    is_active=True
)

OvertimeType.objects.create(
    name="Holiday Overtime",
    description="Holiday work",
    rate_multiplier=2.5,
    max_hours_per_day=8,
    requires_pre_approval=True,
    auto_approve_threshold=0,
    is_active=True
)
```

### 5. BioStar Connection Test

Test BioStar API connection:
```bash
python manage.py shell
```

```python
from core.services.biostar_api import biostar_api

# Test connection
result = biostar_api.test_connection()
print(f"Connected: {result['connected']}")
print(f"Message: {result['message']}")

if result['connected']:
    # Test data retrieval
    users = biostar_api.get_users(limit=5)
    print(f"Found {len(users)} users")
    
    devices = biostar_api.get_devices()
    print(f"Found {len(devices)} devices")
```

### 6. Cron Jobs Setup

Install crontab:
```bash
python manage.py crontab add
```

Verify cron jobs:
```bash
python manage.py crontab show
```

Expected output:
```
0 2 * * * /path/to/venv/bin/python /path/to/project/manage.py cron daily_cleanup
0 0 1 * * /path/to/venv/bin/python /path/to/project/manage.py cron monthly_reports
*/15 * * * * /path/to/venv/bin/python /path/to/project/manage.py cron sync_biostar_events
0 1 * * 1 /path/to/venv/bin/python /path/to/project/manage.py cron weekly_backup
*/30 * * * * /path/to/venv/bin/python /path/to/project/manage.py cron process_notifications
*/5 * * * * /path/to/venv/bin/python /path/to/project/manage.py cron system_health_check
```

## Production Deployment

### 1. Gunicorn Configuration

Create `gunicorn.conf.py`:
```python
bind = "0.0.0.0:8000"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
```

### 2. Nginx Configuration

Create `/etc/nginx/sites-available/workflo`:
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /static/ {
        alias /path/to/workflo_back/staticfiles/;
    }
    
    location /media/ {
        alias /path/to/workflo_back/media/;
    }
}
```

Enable site:
```bash
sudo ln -s /etc/nginx/sites-available/workflo /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 3. Systemd Service

Create `/etc/systemd/system/workflo.service`:
```ini
[Unit]
Description=WorkFlo Django Application
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/path/to/workflo_back
Environment=PATH=/path/to/workflo_back/venv/bin
ExecStart=/path/to/workflo_back/venv/bin/gunicorn --config gunicorn.conf.py workflo_back.wsgi:application
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

Enable and start service:
```bash
sudo systemctl daemon-reload
sudo systemctl enable workflo
sudo systemctl start workflo
sudo systemctl status workflo
```

### 4. SSL Configuration (Let's Encrypt)

Install Certbot:
```bash
sudo apt install certbot python3-certbot-nginx
```

Obtain SSL certificate:
```bash
sudo certbot --nginx -d your-domain.com
```

## Monitoring and Logging

### 1. Log Configuration

Ensure log directory exists:
```bash
mkdir -p /var/log/workflo
chown www-data:www-data /var/log/workflo
```

Update Django settings for production logging:
```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/workflo/workflo.log',
            'maxBytes': 1024*1024*10,  # 10MB
            'backupCount': 5,
            'formatter': 'verbose',
        },
        'biostar_file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/workflo/biostar.log',
            'maxBytes': 1024*1024*5,  # 5MB
            'backupCount': 3,
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'core.services': {
            'handlers': ['biostar_file'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'core.cron': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}
```

### 2. Health Monitoring

Create monitoring script `/usr/local/bin/workflo-health.sh`:
```bash
#!/bin/bash

# Check if service is running
if ! systemctl is-active --quiet workflo; then
    echo "WorkFlo service is not running"
    exit 1
fi

# Check BioStar connection
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/api/biostar-devices/connection_status/)
if [ "$response" != "200" ]; then
    echo "BioStar API health check failed"
    exit 1
fi

echo "All health checks passed"
exit 0
```

Add to crontab for monitoring:
```bash
*/5 * * * * /usr/local/bin/workflo-health.sh || echo "WorkFlo health check failed" | mail -s "WorkFlo Alert" <EMAIL>
```

## Backup and Recovery

### 1. Database Backup

Create backup script `/usr/local/bin/workflo-backup.sh`:
```bash
#!/bin/bash

BACKUP_DIR="/var/backups/workflo"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="workflo_db"
DB_USER="workflo_user"

mkdir -p $BACKUP_DIR

# Database backup
pg_dump -U $DB_USER -h localhost $DB_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# Media files backup
tar -czf $BACKUP_DIR/media_backup_$DATE.tar.gz /path/to/workflo_back/media/

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
```

### 2. Recovery Procedure

Database recovery:
```bash
# Stop application
sudo systemctl stop workflo

# Restore database
gunzip -c /var/backups/workflo/db_backup_YYYYMMDD_HHMMSS.sql.gz | psql -U workflo_user -h localhost workflo_db

# Restore media files
tar -xzf /var/backups/workflo/media_backup_YYYYMMDD_HHMMSS.tar.gz -C /

# Start application
sudo systemctl start workflo
```

## Troubleshooting

### Common Issues

#### 1. BioStar Connection Failed
```bash
# Check network connectivity
curl -I https://ns.biostar2.com

# Check credentials
python manage.py shell -c "from core.services.biostar_api import biostar_api; print(biostar_api.test_connection())"

# Check firewall
sudo ufw status
```

#### 2. Cron Jobs Not Running
```bash
# Check cron service
sudo systemctl status cron

# Check cron logs
sudo tail -f /var/log/syslog | grep CRON

# Manually run cron job
python manage.py cron sync_biostar_events
```

#### 3. High Memory Usage
```bash
# Check memory usage
free -h
ps aux --sort=-%mem | head

# Restart services
sudo systemctl restart workflo
sudo systemctl restart nginx
```

#### 4. Database Connection Issues
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check connections
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity WHERE datname='workflo_db';"

# Check database logs
sudo tail -f /var/log/postgresql/postgresql-*.log
```

## Performance Optimization

### 1. Database Optimization

Add indexes for frequently queried fields:
```sql
-- Attendance records
CREATE INDEX idx_attendance_employee_date ON attendance_records(employee_id, date);
CREATE INDEX idx_attendance_biostar_synced ON attendance_records(biostar_synced);

-- BioStar events
CREATE INDEX idx_biostar_events_processed ON biostar_events(processed);
CREATE INDEX idx_biostar_events_datetime ON biostar_events(event_datetime);

-- Overtime requests
CREATE INDEX idx_overtime_auto_detected ON overtime_requests(auto_detected);
CREATE INDEX idx_overtime_status ON overtime_requests(status);
```

### 2. Redis Caching

Install Redis:
```bash
sudo apt install redis-server
```

Configure Redis in Django settings:
```python
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'KEY_PREFIX': 'workflo',
        'TIMEOUT': 300,
    }
}
```

### 3. Static Files Optimization

Collect static files:
```bash
python manage.py collectstatic --noinput
```

Configure Nginx for static file caching:
```nginx
location /static/ {
    alias /path/to/workflo_back/staticfiles/;
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## Security Considerations

### 1. Environment Variables
- Store sensitive data in environment variables
- Use strong passwords and API keys
- Regularly rotate credentials

### 2. Network Security
- Use HTTPS in production
- Configure firewall rules
- Restrict database access

### 3. Application Security
- Keep dependencies updated
- Enable CSRF protection
- Use secure session settings
- Implement rate limiting

### 4. Monitoring
- Monitor failed login attempts
- Log API access
- Set up alerts for unusual activity

## Maintenance

### Regular Tasks
1. **Daily**: Check logs for errors
2. **Weekly**: Review backup integrity
3. **Monthly**: Update dependencies
4. **Quarterly**: Security audit

### Update Procedure
1. Backup database and media files
2. Test updates in staging environment
3. Deploy during maintenance window
4. Verify all services are working
5. Monitor for issues post-deployment
