#!/usr/bin/env python
"""
Simple BioStar API Test Script
Tests core BioStar functionality without complex Django dependencies
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'workflo_back.settings')
django.setup()

def test_biostar_mock_api():
    """Test BioStar API in mock mode"""
    print("=" * 50)
    print("TESTING BIOSTAR API - MOCK MODE")
    print("=" * 50)
    
    # Enable mock mode
    os.environ['BIOSTAR_MOCK_MODE'] = 'True'
    
    try:
        # Import after setting environment variable
        from core.services.biostar_api import BioStarAPI
        
        # Create new instance with mock mode
        api = BioStarAPI()
        print(f"Mock mode enabled: {api.mock_mode}")
        
        # Test 1: Connection Test
        print("\n1. Testing API Connection...")
        connection_result = api.test_connection()
        print(f"   Connected: {connection_result['connected']}")
        print(f"   Mock Mode: {connection_result['mock_mode']}")
        print(f"   Message: {connection_result['message']}")
        
        if not connection_result['connected']:
            print("   ❌ Connection test failed")
            return False
        
        print("   ✅ Connection test passed")
        
        # Test 2: Authentication
        print("\n2. Testing Authentication...")
        auth_result = api.authenticate()
        print(f"   Authentication successful: {auth_result}")
        
        if not auth_result:
            print("   ❌ Authentication failed")
            return False
        
        print("   ✅ Authentication passed")
        
        # Test 3: Get Users
        print("\n3. Testing Get Users...")
        users = api.get_users(limit=5)
        print(f"   Found {len(users)} users")
        
        if len(users) > 0:
            for i, user in enumerate(users[:3]):
                print(f"   User {i+1}: {user.get('name', 'Unknown')} (ID: {user.get('id', 'N/A')})")
            print("   ✅ Get users passed")
        else:
            print("   ❌ No users found")
            return False
        
        # Test 4: Get Devices
        print("\n4. Testing Get Devices...")
        devices = api.get_devices()
        print(f"   Found {len(devices)} devices")
        
        if len(devices) > 0:
            for i, device in enumerate(devices):
                print(f"   Device {i+1}: {device.get('name', 'Unknown')} (Status: {device.get('status', 'N/A')})")
            print("   ✅ Get devices passed")
        else:
            print("   ❌ No devices found")
            return False
        
        # Test 5: Get Events
        print("\n5. Testing Get Events...")
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=24)
        events = api.get_events(start_time, end_time, limit=10)
        print(f"   Found {len(events)} events")
        
        if len(events) > 0:
            for i, event in enumerate(events[:3]):
                print(f"   Event {i+1}: {event.get('event_type', 'Unknown')} at {event.get('datetime', 'N/A')}")
            print("   ✅ Get events passed")
        else:
            print("   ⚠️  No events found (this is normal for mock data)")
        
        print("\n🎉 All Mock API Tests Passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Mock API Test Failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_biostar_real_api():
    """Test Real BioStar API"""
    print("\n" + "=" * 50)
    print("TESTING REAL BIOSTAR API")
    print("=" * 50)
    
    # Disable mock mode
    os.environ['BIOSTAR_MOCK_MODE'] = 'False'
    
    try:
        from core.services.biostar_api import BioStarAPI
        
        # Create new instance without mock mode
        api = BioStarAPI()
        print(f"Mock mode disabled: {not api.mock_mode}")
        print(f"API URL: {api.base_url}")
        print(f"Username: {api.username}")
        
        # Test connection
        print("\n1. Testing Real API Connection...")
        connection_result = api.test_connection()
        print(f"   Connected: {connection_result['connected']}")
        print(f"   Response Time: {connection_result.get('response_time', 0):.2f}s")
        print(f"   Message: {connection_result['message']}")
        
        if connection_result['connected']:
            print("   ✅ Real API connection successful!")
            
            # Test getting users
            print("\n2. Testing Real Get Users...")
            users = api.get_users(limit=3)
            print(f"   Found {len(users)} users")
            
            if len(users) > 0:
                for i, user in enumerate(users):
                    print(f"   User {i+1}: {user.get('name', 'Unknown')} (ID: {user.get('id', 'N/A')})")
                print("   ✅ Real get users passed")
            else:
                print("   ⚠️  No users found")
            
            # Test getting devices
            print("\n3. Testing Real Get Devices...")
            devices = api.get_devices()
            print(f"   Found {len(devices)} devices")
            
            if len(devices) > 0:
                for i, device in enumerate(devices):
                    print(f"   Device {i+1}: {device.get('name', 'Unknown')} (Status: {device.get('status', 'N/A')})")
                print("   ✅ Real get devices passed")
            else:
                print("   ⚠️  No devices found")
            
            print("\n🎉 Real API Tests Passed!")
            return True
        else:
            print(f"   ⚠️  Real API connection failed: {connection_result['message']}")
            print("   This is expected if:")
            print("   - BioStar server is not accessible from this network")
            print("   - Credentials are incorrect")
            print("   - Firewall is blocking the connection")
            return False
            
    except Exception as e:
        print(f"\n❌ Real API Test Failed: {str(e)}")
        print("   This is expected if BioStar server is not accessible")
        return False

def test_salary_calculator_basic():
    """Test basic salary calculator functionality"""
    print("\n" + "=" * 50)
    print("TESTING SALARY CALCULATOR")
    print("=" * 50)
    
    try:
        from core.services.salary_calculator import DynamicSalaryCalculator
        
        calculator = DynamicSalaryCalculator()
        print(f"Calculator enabled: {calculator.enabled}")
        print(f"Standard work hours: {calculator.standard_work_hours}")
        print(f"Overtime multiplier: {calculator.overtime_multiplier}")
        print(f"Weekend multiplier: {calculator.weekend_multiplier}")
        
        # Test working days calculation
        from datetime import date
        start_date = date.today().replace(day=1)
        end_date = date.today()
        
        print(f"\n1. Testing working days calculation...")
        print(f"   Period: {start_date} to {end_date}")
        
        working_days = calculator._calculate_working_days(start_date, end_date)
        print(f"   Total days: {working_days['total_days']}")
        print(f"   Working days: {working_days['working_days']}")
        print(f"   Weekend days: {working_days['weekend_days']}")
        print(f"   Holiday days: {working_days['holiday_days']}")
        print("   ✅ Working days calculation passed")
        
        print("\n🎉 Salary Calculator Basic Tests Passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Salary Calculator Test Failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """Test configuration loading"""
    print("\n" + "=" * 50)
    print("TESTING CONFIGURATION")
    print("=" * 50)
    
    try:
        from django.conf import settings
        
        print("1. Testing BioStar API Configuration...")
        biostar_config = settings.BIOSTAR_API_CONFIG
        print(f"   Base URL: {biostar_config['BASE_URL']}")
        print(f"   Username: {biostar_config['USERNAME']}")
        print(f"   Timeout: {biostar_config['TIMEOUT']}s")
        print(f"   Max Retries: {biostar_config['MAX_RETRIES']}")
        print(f"   Mock Mode: {biostar_config['MOCK_MODE']}")
        print("   ✅ BioStar config loaded")
        
        print("\n2. Testing Dynamic Salary Configuration...")
        salary_config = settings.DYNAMIC_SALARY_CONFIG
        print(f"   Enabled: {salary_config['ENABLED']}")
        print(f"   Standard Hours: {salary_config['STANDARD_WORK_HOURS']}")
        print(f"   Overtime Multiplier: {salary_config['OVERTIME_MULTIPLIER']}")
        print(f"   Weekend Multiplier: {salary_config['WEEKEND_MULTIPLIER']}")
        print("   ✅ Salary config loaded")
        
        print("\n🎉 Configuration Tests Passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Configuration Test Failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Simple BioStar Integration Tests...")
    print(f"Python version: {sys.version}")
    print(f"Django version: {django.get_version()}")
    
    test_results = []
    
    # Run tests
    test_results.append(("Configuration", test_configuration()))
    test_results.append(("Mock API", test_biostar_mock_api()))
    test_results.append(("Salary Calculator", test_salary_calculator_basic()))
    test_results.append(("Real API", test_biostar_real_api()))
    
    # Print summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    print(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed >= 3:  # Allow real API to fail
        print("🎉 Core functionality is working!")
        print("\nNext steps:")
        print("1. Ensure BioStar server is accessible for real API testing")
        print("2. Create employee records and salary profiles")
        print("3. Run full integration tests")
    else:
        print("⚠️  Some core tests failed. Check the configuration and setup.")
    
    return passed >= 3

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
