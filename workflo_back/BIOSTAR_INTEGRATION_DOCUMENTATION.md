# BioStar 2 API Integration Documentation

## Overview

This document provides comprehensive documentation for the BioStar 2 API integration with the WorkFlo backend system. The integration enables dynamic salary calculation based on real-time attendance data from Suprema BioStar 2 biometric devices.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Configuration](#configuration)
3. [API Integration](#api-integration)
4. [Dynamic Salary Calculation](#dynamic-salary-calculation)
5. [Overtime Management](#overtime-management)
6. [Database Schema](#database-schema)
7. [API Endpoints](#api-endpoints)
8. [Cron Jobs](#cron-jobs)
9. [Testing](#testing)
10. [Troubleshooting](#troubleshooting)

## Architecture Overview

The BioStar integration consists of several key components:

### Core Services
- **BioStarAPI** (`core/services/biostar_api.py`): Handles API communication with BioStar 2
- **BioStarSyncService** (`core/services/biostar_sync.py`): Synchronizes data between BioStar and local database
- **DynamicSalaryCalculator** (`core/services/salary_calculator.py`): Calculates salaries based on attendance data

### Models
- **BiostarEvent**: Stores individual biometric events from devices
- **BiostarDevice**: Manages biometric device information
- **AttendanceRecord**: Enhanced with BioStar integration
- **OvertimeRequest**: Auto-detection and approval workflows

### API Views
- **AttendanceRecordViewSet**: Enhanced with BioStar sync and salary calculation
- **BiostarEventViewSet**: Real-time event management
- **BiostarDeviceViewSet**: Device status and management

## Configuration

### Environment Variables

Add the following to your `.env` file:

```env
# BioStar API Configuration
BIOSTAR_BASE_URL=https://ns.biostar2.com
BIOSTAR_USERNAME=dev
BIOSTAR_PASSWORD=d3vt3@ms
BIOSTAR_API_VERSION=v1
BIOSTAR_TIMEOUT=30
BIOSTAR_MAX_RETRIES=3
BIOSTAR_SYNC_INTERVAL=300
BIOSTAR_ENABLE_REAL_TIME=True
BIOSTAR_MOCK_MODE=False

# Dynamic Salary Configuration
DYNAMIC_SALARY_ENABLED=True
STANDARD_WORK_HOURS=8
STANDARD_WORK_DAYS=22
OVERTIME_THRESHOLD=8
OVERTIME_MULTIPLIER=1.5
WEEKEND_MULTIPLIER=2.0
HOLIDAY_MULTIPLIER=2.5
LATE_PENALTY_ENABLED=True
LATE_PENALTY_RATE=0.1
ABSENCE_DEDUCTION_ENABLED=True
```

### Django Settings

The configuration is automatically loaded in `settings.py`:

```python
# BioStar API Configuration
BIOSTAR_API_CONFIG = {
    'BASE_URL': config('BIOSTAR_BASE_URL', default='https://ns.biostar2.com'),
    'USERNAME': config('BIOSTAR_USERNAME', default='dev'),
    'PASSWORD': config('BIOSTAR_PASSWORD', default='d3vt3@ms'),
    # ... other settings
}

# Dynamic Salary Configuration
DYNAMIC_SALARY_CONFIG = {
    'ENABLED': config('DYNAMIC_SALARY_ENABLED', default=True, cast=bool),
    'STANDARD_WORK_HOURS': config('STANDARD_WORK_HOURS', default=8, cast=int),
    # ... other settings
}
```

## API Integration

### Authentication

The BioStar API client handles authentication automatically:

```python
from core.services.biostar_api import biostar_api

# Test connection
connection_status = biostar_api.test_connection()
if connection_status['connected']:
    print("BioStar API connected successfully")
```

### Data Synchronization

#### Manual Synchronization

```python
from core.services.biostar_sync import biostar_sync
from datetime import datetime, timedelta

# Sync events for the last 24 hours
end_time = datetime.now()
start_time = end_time - timedelta(hours=24)
result = biostar_sync.sync_events(start_time, end_time)
```

#### Automatic Synchronization

Cron job runs every 15 minutes:
```python
# In settings.py
CRONJOBS = [
    ('*/15 * * * *', 'core.cron.sync_biostar_events', '>> /tmp/cron_biostar.log'),
]
```

## Dynamic Salary Calculation

### Basic Usage

```python
from core.services.salary_calculator import salary_calculator
from core.models.auth import User
from datetime import date

employee = User.objects.get(employee_id='EMP001')
start_date = date(2024, 1, 1)
end_date = date(2024, 1, 31)

calculation = salary_calculator.calculate_employee_salary(
    employee, start_date, end_date
)
```

### Calculation Components

The salary calculation includes:

1. **Base Salary**: Calculated based on attendance rate
2. **Overtime**: Automatic detection and calculation
3. **Deductions**: Late penalties, absence deductions
4. **Allowances**: Fixed allowances from salary profile

### Example Response

```json
{
    "employee_id": 1,
    "employee_name": "John Doe",
    "calculation_period": {
        "start_date": "2024-01-01",
        "end_date": "2024-01-31"
    },
    "working_days": {
        "total_days": 31,
        "working_days": 22,
        "weekend_days": 8,
        "holiday_days": 1
    },
    "attendance_summary": {
        "days_present": 20,
        "days_late": 2,
        "days_absent": 2,
        "total_hours_worked": 160.0,
        "total_overtime_hours": 8.0,
        "attendance_rate": 90.91
    },
    "totals": {
        "gross_salary": 75000.00,
        "total_deductions": 5000.00,
        "net_salary": 70000.00
    }
}
```

## Overtime Management

### Auto-Detection

Overtime is automatically detected when:
1. Employee works more than standard hours (8 hours/day)
2. BioStar events show check-in/check-out times
3. Attendance record is calculated

### Auto-Approval

Overtime requests can be auto-approved if:
1. Hours are below auto-approval threshold
2. Overtime type allows auto-approval
3. Request was auto-detected from BioStar

```python
# In OvertimeRequest model
def auto_approve_if_eligible(self):
    if (self.auto_detected and 
        self.overtime_type.auto_approve_threshold and 
        self.planned_hours <= self.overtime_type.auto_approve_threshold):
        self.status = 'approved'
        self.save()
        return True
    return False
```

## Database Schema

### Key Tables

#### biostar_events
```sql
CREATE TABLE biostar_events (
    id SERIAL PRIMARY KEY,
    biostar_event_id VARCHAR(100) UNIQUE,
    employee_id INTEGER REFERENCES users(id),
    device_id VARCHAR(100),
    device_name VARCHAR(255),
    event_type VARCHAR(20),
    event_datetime TIMESTAMP,
    location VARCHAR(255),
    processed BOOLEAN DEFAULT FALSE,
    attendance_record_id INTEGER REFERENCES attendance_records(id),
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### biostar_devices
```sql
CREATE TABLE biostar_devices (
    id SERIAL PRIMARY KEY,
    biostar_device_id VARCHAR(100) UNIQUE,
    name VARCHAR(255),
    ip_address INET,
    port INTEGER,
    location VARCHAR(255),
    device_type VARCHAR(100),
    status VARCHAR(20) DEFAULT 'offline',
    last_seen TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### Enhanced Tables

#### attendance_records (Enhanced)
- Added `biostar_synced` field
- Enhanced `calculate_hours()` method
- Added overtime eligibility checking

#### overtime_requests (Enhanced)
- Added `auto_detected` field
- Added `biostar_event_id` field
- Added `attendance_record` foreign key

## API Endpoints

### Attendance Management

#### Sync BioStar Data
```http
POST /api/attendance-records/sync_biostar/
Content-Type: application/json

{
    "start_date": "2024-01-01",
    "end_date": "2024-01-31"
}
```

#### Get Attendance Summary
```http
GET /api/attendance-records/attendance_summary/?employee_id=1&start_date=2024-01-01&end_date=2024-01-31
```

#### Calculate Dynamic Salary
```http
POST /api/attendance-records/calculate_salary/
Content-Type: application/json

{
    "employee_id": 1,
    "start_date": "2024-01-01",
    "end_date": "2024-01-31"
}
```

### BioStar Events

#### Get Real-time Events
```http
GET /api/biostar-events/realtime_events/
```

#### Get Unprocessed Events
```http
GET /api/biostar-events/unprocessed_events/
```

#### Process Event Manually
```http
POST /api/biostar-events/{id}/process_event/
```

### BioStar Devices

#### Check Connection Status
```http
GET /api/biostar-devices/connection_status/
```

#### Sync Devices
```http
POST /api/biostar-devices/sync_devices/
```

## Cron Jobs

### BioStar Synchronization
- **Frequency**: Every 15 minutes
- **Function**: `core.cron.sync_biostar_events`
- **Purpose**: Sync events, detect overtime, auto-approve eligible requests

### Daily Cleanup
- **Frequency**: Daily at 2:00 AM
- **Function**: `core.cron.daily_cleanup`
- **Purpose**: Clean expired sessions, old logs, update balances

### Monthly Reports
- **Frequency**: First day of each month
- **Function**: `core.cron.monthly_reports`
- **Purpose**: Generate payroll reports, salary calculations

## Testing

### Mock Mode

Enable mock mode for testing without real BioStar connection:

```env
BIOSTAR_MOCK_MODE=True
```

### Unit Tests

```python
from django.test import TestCase
from core.services.biostar_api import biostar_api

class BioStarAPITest(TestCase):
    def test_connection(self):
        result = biostar_api.test_connection()
        self.assertTrue(result['connected'])
    
    def test_mock_users(self):
        users = biostar_api.get_users()
        self.assertIsInstance(users, list)
```

### Integration Tests

```python
from core.services.salary_calculator import salary_calculator

class SalaryCalculationTest(TestCase):
    def test_dynamic_calculation(self):
        # Create test data
        employee = User.objects.create(...)
        # Test calculation
        result = salary_calculator.calculate_employee_salary(...)
        self.assertIn('totals', result)
```

## Troubleshooting

### Common Issues

#### Connection Errors
```
Error: Failed to connect to BioStar API
Solution: Check network connectivity, API credentials, and firewall settings
```

#### Authentication Failures
```
Error: Authentication failed
Solution: Verify username/password in environment variables
```

#### Sync Issues
```
Error: Events not processing
Solution: Check employee mapping, verify BioStar user IDs match employee IDs
```

### Debugging

Enable debug logging:
```python
LOGGING = {
    'loggers': {
        'core.services': {
            'level': 'DEBUG',
        }
    }
}
```

### Monitoring

Check system health:
```http
GET /api/system/health/
```

Monitor BioStar connection:
```http
GET /api/biostar-devices/connection_status/
```

## Performance Considerations

1. **Batch Processing**: Events are processed in batches of 100
2. **Caching**: API tokens are cached for 1 hour
3. **Rate Limiting**: Respects BioStar API rate limits
4. **Database Optimization**: Proper indexing on frequently queried fields

## Security

1. **API Credentials**: Stored in environment variables
2. **Token Management**: Automatic refresh and secure storage
3. **Data Validation**: All inputs validated before processing
4. **Audit Logging**: All operations logged for compliance

## Future Enhancements

1. **Real-time WebSocket Integration**: Live attendance updates
2. **Advanced Analytics**: Attendance patterns and insights
3. **Mobile App Integration**: Employee self-service features
4. **Multi-tenant Support**: Support for multiple organizations
