services:
  # PostgreSQL Database
  - type: pserv
    name: workflo-postgres
    runtime: docker
    plan: starter
    dockerfilePath: ./Dockerfile.postgres
    disk:
      name: workflo-postgres-disk
      mountPath: /var/lib/postgresql/data
      sizeGB: 10
    envVars:
      - key: POSTGRES_DB
        value: workflo_db
      - key: POSTGRES_USER
        value: workflo_user
      - key: POSTGRES_PASSWORD
        generateValue: true

  # Redis Cache
  - type: redis
    name: workflo-redis
    plan: starter
    maxmemoryPolicy: allkeys-lru
    ipAllowList: []

  # Django Backend Web Service
  - type: web
    name: workflo-backend
    runtime: python
    plan: starter
    buildCommand: |
      pip install --upgrade pip
      pip install -r requirements.txt
      python manage.py collectstatic --noinput
    startCommand: python server.py
    healthCheckPath: /api/health/
    envVars:
      # Django Settings
      - key: SECRET_KEY
        generateValue: true
      - key: DEBUG
        value: "false"
      - key: ALLOWED_HOSTS
        value: workflo-backend.onrender.com,workflo-backend-h7mqp514a-victor-mbuguas-projects.onrender.com,localhost,127.0.0.1
      
      # Database Configuration
      - key: DATABASE_URL
        fromService:
          type: pserv
          name: workflo-postgres
          property: connectionString
      
      # Redis Configuration
      - key: REDIS_URL
        fromService:
          type: redis
          name: workflo-redis
          property: connectionString
      
      # CORS Settings
      - key: CORS_ALLOWED_ORIGINS
        value: https://workflo-front.vercel.app,http://localhost:3000,http://127.0.0.1:3000
      
      # Security Settings
      - key: SECURE_SSL_REDIRECT
        value: "true"

      # Email Configuration (using SendGrid or similar)
      - key: EMAIL_BACKEND
        value: django.core.mail.backends.smtp.EmailBackend
      - key: EMAIL_HOST
        value: smtp.sendgrid.net
      - key: EMAIL_PORT
        value: "587"
      - key: EMAIL_USE_TLS
        value: "true"
      - key: EMAIL_HOST_USER
        value: apikey
      - key: EMAIL_HOST_PASSWORD
        sync: false  # Set this manually in Render dashboard
      - key: DEFAULT_FROM_EMAIL
        value: <EMAIL>
      
      # Celery Configuration
      - key: CELERY_BROKER_URL
        fromService:
          type: redis
          name: workflo-redis
          property: connectionString
      - key: CELERY_RESULT_BACKEND
        fromService:
          type: redis
          name: workflo-redis
          property: connectionString
      
      # API Rate Limiting
      - key: ANON_RATE_LIMIT
        value: 100/hour
      - key: USER_RATE_LIMIT
        value: 1000/hour
      
      # BioStar Integration (disabled for production)
      - key: USE_BIOSTAR_INTEGRATION
        value: "false"

      # Production optimizations
      - key: WEB_CONCURRENCY
        value: "2"
      - key: PORT
        value: "10000"

  # Background Worker for Celery Tasks
  - type: worker
    name: workflo-worker
    runtime: python
    plan: starter
    buildCommand: |
      pip install --upgrade pip
      pip install -r requirements.txt
    startCommand: celery -A workflo_back worker --loglevel=info
    envVars:
      # Same environment variables as web service
      - key: SECRET_KEY
        fromService:
          type: web
          name: workflo-backend
          envVarKey: SECRET_KEY
      - key: DEBUG
        value: "false"
      - key: DATABASE_URL
        fromService:
          type: pserv
          name: workflo-postgres
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: workflo-redis
          property: connectionString
      - key: CELERY_BROKER_URL
        fromService:
          type: redis
          name: workflo-redis
          property: connectionString
      - key: CELERY_RESULT_BACKEND
        fromService:
          type: redis
          name: workflo-redis
          property: connectionString
